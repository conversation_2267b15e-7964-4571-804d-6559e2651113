package config

// AliyunSpeech 阿里云语音识别配置
type AliyunSpeech struct {
	AppKey          string `mapstructure:"app-key" json:"app-key" yaml:"app-key"`                               // APPKey
	Token           string `mapstructure:"token" json:"token" yaml:"token"`                                     // Token (已废弃，使用动态获取)
	URL             string `mapstructure:"url" json:"url" yaml:"url"`                                           // 接口URL
	AccessKeyId     string `mapstructure:"access-key-id" json:"access-key-id" yaml:"access-key-id"`             // AccessKeyId
	AccessKeySecret string `mapstructure:"access-key-secret" json:"access-key-secret" yaml:"access-key-secret"` // AccessKeySecret
}
