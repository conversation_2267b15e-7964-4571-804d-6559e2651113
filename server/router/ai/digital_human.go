package ai

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/gin-gonic/gin"
)

type DigitalHumanRouter struct{}

// InitDigitalHumanRouter 初始化数字人视频路由
func (router *DigitalHumanRouter) InitDigitalHumanRouter(Router *gin.RouterGroup) {
	digitalHumanRouterWithoutRecord := Router.Group("ai/digital-human")
	digitalHumanApi := v1.ApiGroupApp.AiApiGroup.DigitalHumanApi
	{

		digitalHumanRouterWithoutRecord.POST("create", digitalHumanApi.CreateDigitalHumanTask)              // 创建数字人视频任务（图生数字人）
		digitalHumanRouterWithoutRecord.GET("status/:taskId", digitalHumanApi.GetDigitalHumanTaskStatus)    // 获取任务状态（图生数字人）
		digitalHumanRouterWithoutRecord.GET("list", digitalHumanApi.GetDigitalHumanTaskList)                // 获取任务列表（图生数字人）

		digitalHumanRouterWithoutRecord.POST("heygem/create", digitalHumanApi.CreateHeygemDigitalHumanTask) // 创建HeyGem数字人视频任务（视频数字人）
		digitalHumanRouterWithoutRecord.GET("heygem/list", digitalHumanApi.GetHeygemTaskList)               // 获取HeyGem任务列表（视频数字人）
		digitalHumanRouterWithoutRecord.GET("heygem/status/:taskId", digitalHumanApi.GetHeygemTaskStatus)   // 获取HeyGem任务状态（视频数字人）
	}
}
