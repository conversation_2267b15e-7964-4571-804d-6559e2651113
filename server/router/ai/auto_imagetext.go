package ai

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AutoImageTextRouter struct{}

func (r *AutoImageTextRouter) InitAutoImageTextRouter(Router *gin.RouterGroup) {
	autoImageTextRouter := Router.Group("ai/imagetext/auto").Use(middleware.OperationRecord())
	autoImageTextApi := v1.ApiGroupApp.AiApiGroup.AutoImageTextApi
	{
		autoImageTextRouter.POST("submit", autoImageTextApi.SubmitAutoImageTextTask)           // 提交批量图文生成任务
		autoImageTextRouter.GET("status/:taskId", autoImageTextApi.GetAutoImageTextTaskStatus) // 获取任务状态
		autoImageTextRouter.GET("list", autoImageTextApi.ListAutoImageTextTasks)               // 获取任务列表
		autoImageTextRouter.DELETE("delete", autoImageTextApi.DeleteAutoImageTextTask)         // 删除任务
	}
}
