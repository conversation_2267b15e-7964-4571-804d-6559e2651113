package ai

import (
	"github.com/gin-gonic/gin"
)

type MinimaxVoiceFavoriteRouter struct{}

// InitMinimaxVoiceFavoriteRouter 初始化 MiniMax音色收藏 路由信息
func (s *MinimaxVoiceFavoriteRouter) InitMinimaxVoiceFavoriteRouter(Router *gin.RouterGroup) {
	minimaxVoiceFavoriteRouter := Router.Group("ai/minimax-voice-favorite")
	{
		minimaxVoiceFavoriteRouter.POST("add", minimaxVoiceFavoriteApi.AddFavoriteVoice)       // 添加收藏音色
		minimaxVoiceFavoriteRouter.POST("remove", minimaxVoiceFavoriteApi.RemoveFavoriteVoice) // 删除收藏音色
		minimaxVoiceFavoriteRouter.GET("list", minimaxVoiceFavoriteApi.GetFavoriteVoices)      // 获取收藏音色列表
	}
}
