package douyin

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type DyProductCategoryRouter struct{}

func (s *DyProductCategoryRouter) InitDyProductCategoryRouter(Router *gin.RouterGroup) {
	dyProductCategoryRouter := Router.Group("douyin/product-category").Use(middleware.OperationRecord())
	dyProductCategoryRouterWithoutRecord := Router.Group("douyin/product-category")

	{
		dyProductCategoryRouter.POST("", dyProductCategoryApi.CreateProductCategory)      // 创建商品分类
		dyProductCategoryRouter.PUT(":id", dyProductCategoryApi.UpdateProductCategory)    // 更新商品分类
		dyProductCategoryRouter.DELETE(":id", dyProductCategoryApi.DeleteProductCategory) // 删除商品分类
		dyProductCategoryRouter.PUT("status", dyProductCategoryApi.UpdateCategoryStatus)  // 更新分类状态
		dyProductCategoryRouter.PUT("sort", dyProductCategoryApi.UpdateCategorySort)      // 更新分类排序
	}
	{
		dyProductCategoryRouterWithoutRecord.GET("", dyProductCategoryApi.GetProductCategoryList)    // 获取商品分类列表
		dyProductCategoryRouterWithoutRecord.GET(":id", dyProductCategoryApi.GetProductCategoryById) // 根据ID获取商品分类
		dyProductCategoryRouterWithoutRecord.GET("all", dyProductCategoryApi.GetAllCategories)       // 获取所有启用的分类
	}
}
