package douyin

import (
	"testing"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	dyModel "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/glebarez/sqlite"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// setupTestDB 设置测试数据库和Redis
func setupTestDB(t *testing.T) {
	// 使用内存SQLite数据库进行测试
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		t.Fatal("failed to open test database:", err)
	}

	// 设置全局数据库
	global.GVA_DB = db

	// 设置测试用的Redis（使用模拟的Redis客户端）
	// 在测试环境中使用miniredis或者mock Redis
	testRedis := redis.NewClient(&redis.Options{
		Addr: "localhost:6379", // 这里会连接失败，但我们会忽略Redis错误
	})
	global.GVA_REDIS = testRedis

	// 自动迁移测试所需的表
	err = db.AutoMigrate(&dyModel.DyUser{})
	if err != nil {
		t.Fatal("failed to migrate test database:", err)
	}
}

func TestDyUserService_AddUser(t *testing.T) {
	// 设置测试数据库
	setupTestDB(t)

	dyUser := &dyModel.DyUser{
		UID:            "**********",
		Nickname:       "test",
		Avatar:         "test",
		UniqueId:       "test",
		ShortId:        "test",
		SecUid:         "test",
		FollowerCount:  100,
		FollowingCount: 100,
		AwemeCount:     100,
		TotalFavorited: 100,
		AccountRegion:  "test",
		Province:       "test",
		City:           "test",
		SysUserId:      1,
	}

	err := DyUserServiceApp.AddUser(dyUser)
	if err != nil {
		t.Fatal("AddUser failed:", err)
	}

	// 验证用户是否成功添加
	var savedUser dyModel.DyUser
	err = global.GVA_DB.Where("uid = ?", "**********").First(&savedUser).Error
	if err != nil {
		t.Fatal("failed to find saved user:", err)
	}

	// 验证数据是否正确
	if savedUser.Nickname != "test" {
		t.Errorf("expected nickname 'test', got '%s'", savedUser.Nickname)
	}
	if savedUser.UID != "**********" {
		t.Errorf("expected UID '**********', got '%s'", savedUser.UID)
	}
}
