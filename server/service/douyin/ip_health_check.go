package douyin

import (
	"fmt"
	"net/http"
	"net/url"
	"sync"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type IPHealthCheckService struct{}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	MaxConcurrent       int           // 最大并发数
	Timeout             time.Duration // 超时时间
	MaxConsecutiveFails int           // 最大连续失败次数，超过后标记为不健康
	CheckInterval       time.Duration // 检查间隔
	TestTargets         []string      // 测试目标URLs
}

// DefaultHealthCheckConfig 默认配置
func DefaultHealthCheckConfig() HealthCheckConfig {
	return HealthCheckConfig{
		MaxConcurrent:       10,
		Timeout:             15 * time.Second,
		MaxConsecutiveFails: 3,
		CheckInterval:       5 * time.Minute,
		TestTargets: []string{
			"http://httpbin.org/ip", // 测试代理IP返回
			"https://www.baidu.com", // 测试国内网站访问
			"http://myip.ipip.net",  // 简单的IP检测服务
		},
	}
}

// CheckSingleIP 检查单个IP健康状态
// persistToDB: 是否保存到数据库并更新IP状态
func (s *IPHealthCheckService) CheckSingleIP(ipAddress string, config HealthCheckConfig, persistToDB bool) *douyin.IpHealthCheckLog {
	log := &douyin.IpHealthCheckLog{
		IP:        ipAddress,
		CheckTime: time.Now(),
		Status:    "failed",
	}

	// 创建代理客户端 - 支持认证
	var proxyURL *url.URL
	var err error

	// 检查是否有代理认证配置
	if global.GVA_CONFIG.Kuaidaili.Username != "" && global.GVA_CONFIG.Kuaidaili.Password != "" {
		// 使用认证代理
		proxyURL, err = url.Parse(fmt.Sprintf("http://%s:%s@%s",
			global.GVA_CONFIG.Kuaidaili.Username,
			global.GVA_CONFIG.Kuaidaili.Password,
			ipAddress))
	} else {
		// 普通代理
		proxyURL, err = url.Parse(fmt.Sprintf("http://%s", ipAddress))
	}

	if err != nil {
		log.ErrorMessage = fmt.Sprintf("解析代理地址失败: %v", err)
		if persistToDB {
			s.saveLogAndUpdateStatus(log)
		}
		return log
	}

	// 设置HTTP客户端
	client := &http.Client{
		Timeout: config.Timeout,
		Transport: &http.Transport{
			Proxy: http.ProxyURL(proxyURL),
		},
	}

	// 测试多个目标，失败时重试最多3次
	var totalResponseTime int
	var successCount int
	maxRetries := 3

	for _, target := range config.TestTargets {
		var targetSuccess bool

		// 对每个目标重试最多3次
		for retry := 0; retry < maxRetries && !targetSuccess; retry++ {
			startTime := time.Now()

			req, err := http.NewRequest("GET", target, nil)
			if err != nil {
				continue
			}

			// 设置用户代理
			req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

			resp, err := client.Do(req)
			responseTime := int(time.Since(startTime).Milliseconds())
			totalResponseTime += responseTime

			if err != nil {
				log.ErrorMessage = fmt.Sprintf("请求失败(重试%d/%d): %v", retry+1, maxRetries, err)
				log.TestTarget = target
				// 如果不是最后一次重试，稍微等待一下再重试
				if retry < maxRetries-1 {
					time.Sleep(time.Second)
				}
				continue
			}

			log.HTTPStatus = resp.StatusCode
			resp.Body.Close()

			// 检查是否成功
			if resp.StatusCode >= 200 && resp.StatusCode < 400 {
				log.Status = "success"
				log.TestTarget = target
				successCount++
				targetSuccess = true
				// 清除错误信息（因为最终成功了）
				log.ErrorMessage = ""
				break // 跳出重试循环，该目标成功
			} else {
				log.ErrorMessage = fmt.Sprintf("HTTP状态码错误(重试%d/%d): %d", retry+1, maxRetries, resp.StatusCode)
				// 如果不是最后一次重试，稍微等待一下再重试
				if retry < maxRetries-1 {
					time.Sleep(time.Second)
				}
			}
		}

		// 如果有一个目标成功，就不需要测试其他目标了
		if targetSuccess {
			break
		}
	}

	if successCount > 0 {
		log.ResponseTime = totalResponseTime / (successCount * maxRetries) // 考虑重试次数的平均响应时间
	} else {
		log.ResponseTime = totalResponseTime / (len(config.TestTargets) * maxRetries)
		if log.ErrorMessage == "" {
			log.ErrorMessage = fmt.Sprintf("所有测试目标在%d次重试后都失败，最后HTTP状态码: %d", maxRetries, log.HTTPStatus)
		}
	}

	// 如果需要持久化到数据库
	if persistToDB {
		s.saveLogAndUpdateStatus(log)
	}

	return log
}

// saveLogAndUpdateStatus 保存日志并更新IP状态
func (s *IPHealthCheckService) saveLogAndUpdateStatus(log *douyin.IpHealthCheckLog) {
	// 保存检查日志到数据库
	if err := global.GVA_DB.Create(log).Error; err != nil {
		global.GVA_LOG.Error("保存健康检查日志失败",
			zap.String("ip", log.IP),
			zap.Error(err))
	}

	// 更新数据库中IP的健康状态
	updates := map[string]interface{}{
		"last_health_check": time.Now(),
		"response_time":     log.ResponseTime,
	}

	if log.Status == "success" {
		updates["health_status"] = "healthy"
	} else {
		updates["health_status"] = "unhealthy"
	}

	// 更新IP状态
	if err := global.GVA_DB.Model(&douyin.IpPool{}).Where("ip = ?", log.IP).Updates(updates).Error; err != nil {
		global.GVA_LOG.Error("更新IP健康状态失败",
			zap.String("ip", log.IP),
			zap.Error(err))
	}
}

// CheckAllIPs 检查所有启用的IP
func (s *IPHealthCheckService) CheckAllIPs() error {
	config := DefaultHealthCheckConfig()

	// 只获取启用状态的IP（status = true）
	var ips []douyin.IpPool
	err := global.GVA_DB.Where("status = ?", true).Find(&ips).Error
	if err != nil {
		return fmt.Errorf("获取IP列表失败: %v", err)
	}

	if len(ips) == 0 {
		global.GVA_LOG.Info("没有需要检查的启用状态IP")
		return nil
	}

	global.GVA_LOG.Info(fmt.Sprintf("开始健康检查，共 %d 个启用状态的IP", len(ips)))

	// 使用协程池控制并发
	semaphore := make(chan struct{}, config.MaxConcurrent)
	var wg sync.WaitGroup

	for _, ip := range ips {
		wg.Add(1)
		go func(ipPool douyin.IpPool) {
			defer wg.Done()
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			s.checkAndUpdateIP(ipPool, config)
		}(ip)
	}

	wg.Wait()
	global.GVA_LOG.Info("健康检查完成")
	return nil
}

// checkAndUpdateIP 检查并更新单个IP的状态（用于批量检查，包含高级逻辑）
func (s *IPHealthCheckService) checkAndUpdateIP(ipPool douyin.IpPool, config HealthCheckConfig) {
	// 执行健康检查（不保存到数据库，避免重复）
	log := s.CheckSingleIP(ipPool.IP, config, false)

	// 手动保存检查日志
	if err := global.GVA_DB.Create(log).Error; err != nil {
		global.GVA_LOG.Error("保存健康检查日志失败",
			zap.String("ip", ipPool.IP),
			zap.Error(err))
	}

	// 开始事务更新IP状态（包含高级逻辑：连续失败计数、自动禁用等）
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新IP统计信息
	updates := map[string]interface{}{
		"last_health_check":  time.Now(),
		"response_time":      log.ResponseTime,
		"health_check_total": gorm.Expr("health_check_total + 1"),
	}

	if log.Status == "success" {
		// 成功时重置连续失败次数
		updates["consecutive_fails"] = 0
		updates["health_status"] = "healthy"
		// 移除自动恢复逻辑：禁用状态的IP不会被自动启用
	} else {
		// 失败时增加计数
		updates["consecutive_fails"] = gorm.Expr("consecutive_fails + 1")
		updates["health_check_fails"] = gorm.Expr("health_check_fails + 1")

		// 仅更新健康状态，不自动禁用IP
		updates["health_status"] = "unhealthy"
	}

	// 更新可用率
	if err := tx.Model(&douyin.IpPool{}).Where("id = ?", ipPool.ID).Updates(updates).Error; err != nil {
		tx.Rollback()
		global.GVA_LOG.Error("更新IP状态失败",
			zap.String("ip", ipPool.IP),
			zap.Error(err))
		return
	}

	// 重新计算可用率
	var stats struct {
		HealthCheckTotal int64 `gorm:"column:health_check_total"`
		HealthCheckFails int64 `gorm:"column:health_check_fails"`
	}
	if err := tx.Model(&douyin.IpPool{}).Where("id = ?", ipPool.ID).
		Select("health_check_total, health_check_fails").
		Scan(&stats).Error; err != nil {
		global.GVA_LOG.Error("获取IP检查统计失败",
			zap.String("ip", ipPool.IP),
			zap.Error(err))
	}

	if stats.HealthCheckTotal > 0 {
		availabilityRate := float64(stats.HealthCheckTotal-stats.HealthCheckFails) / float64(stats.HealthCheckTotal) * 100
		tx.Model(&douyin.IpPool{}).Where("id = ?", ipPool.ID).
			Update("availability_rate", availabilityRate)
	}

	if err := tx.Commit().Error; err != nil {
		global.GVA_LOG.Error("提交事务失败",
			zap.String("ip", ipPool.IP),
			zap.Error(err))
	}
}

// unbindUsersFromIP 解绑IP的所有用户
func (s *IPHealthCheckService) unbindUsersFromIP(tx *gorm.DB, ip string) {
	// 解绑抖音用户
	if err := tx.Model(&douyin.DyUser{}).Where("bind_ip = ?", ip).Update("bind_ip", nil).Error; err != nil {
		global.GVA_LOG.Error("解绑抖音用户失败", zap.String("ip", ip), zap.Error(err))
	}

	// 解绑火山版用户
	if err := tx.Model(&douyin.FlameUser{}).Where("bind_ip = ?", ip).Update("bind_ip", nil).Error; err != nil {
		global.GVA_LOG.Error("解绑火山版用户失败", zap.String("ip", ip), zap.Error(err))
	}

	// 更新IP的用户计数
	if err := tx.Model(&douyin.IpPool{}).Where("ip = ?", ip).Update("user_count", 0).Error; err != nil {
		global.GVA_LOG.Error("更新IP用户计数失败", zap.String("ip", ip), zap.Error(err))
	}
}

// GetHealthCheckLogs 获取健康检查日志
func (s *IPHealthCheckService) GetHealthCheckLogs(ip string, limit int) ([]douyin.IpHealthCheckLog, error) {
	var logs []douyin.IpHealthCheckLog
	query := global.GVA_DB.Order("check_time DESC")

	if ip != "" {
		query = query.Where("ip = ?", ip)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&logs).Error
	return logs, err
}

// GetHealthStats 获取健康统计信息
func (s *IPHealthCheckService) GetHealthStats() (map[string]interface{}, error) {
	var totalIPs, healthyIPs, unhealthyIPs, unknownIPs int64

	// 统计总IP数
	global.GVA_DB.Model(&douyin.IpPool{}).Where("status = ?", true).Count(&totalIPs)

	// 统计各状态IP数
	global.GVA_DB.Model(&douyin.IpPool{}).Where("status = ? AND health_status = ?", true, "healthy").Count(&healthyIPs)
	global.GVA_DB.Model(&douyin.IpPool{}).Where("status = ? AND health_status = ?", true, "unhealthy").Count(&unhealthyIPs)
	global.GVA_DB.Model(&douyin.IpPool{}).Where("status = ? AND health_status = ?", true, "unknown").Count(&unknownIPs)

	// 计算平均可用率
	var avgAvailability float64
	global.GVA_DB.Model(&douyin.IpPool{}).Where("status = ? AND health_check_total > 0", true).
		Select("AVG(availability_rate)").Scan(&avgAvailability)

	// 获取最近24小时的检查次数
	var recentChecks int64
	global.GVA_DB.Model(&douyin.IpHealthCheckLog{}).
		Where("check_time > ?", time.Now().Add(-24*time.Hour)).
		Count(&recentChecks)

	return map[string]interface{}{
		"totalIPs":        totalIPs,
		"healthyIPs":      healthyIPs,
		"unhealthyIPs":    unhealthyIPs,
		"unknownIPs":      unknownIPs,
		"avgAvailability": avgAvailability,
		"recentChecks":    recentChecks,
	}, nil
}

// CheckSingleIPForAPI 用于API调用的简化健康检查方法
func (s *IPHealthCheckService) CheckSingleIPForAPI(ipAddress string) (*douyin.IpHealthCheckLog, error) {
	config := DefaultHealthCheckConfig()

	// 执行健康检查并保存到数据库
	log := s.CheckSingleIP(ipAddress, config, true)

	return log, nil
}

// CleanOldLogs 清理旧的检查日志
func (s *IPHealthCheckService) CleanOldLogs(retentionDays int) error {
	cutoffTime := time.Now().AddDate(0, 0, -retentionDays)
	return global.GVA_DB.Where("check_time < ?", cutoffTime).Delete(&douyin.IpHealthCheckLog{}).Error
}
