package douyin

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"sync" // 添加sync包
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/valyala/fasthttp"
)

// MoreApiHttpClient more-api使用的http客户端，用于与抖音API通信
type MoreApiHttpClient struct {
	BaseURL string
	Client  *http.Client
	mutex   sync.RWMutex // 添加互斥锁保护客户端操作
}

// NewDyHttpClient 创建一个新的抖音HTTP客户端
func NewMoreDyHttpClient(baseURL string) *MoreApiHttpClient {
	return &MoreApiHttpClient{
		BaseURL: baseURL,
		Client: &http.Client{
			Timeout: 60 * time.Second, // 设置超时
		},
	}
}

// CleanupConnections 清理连接池中的无效连接
func (c *MoreApiHttpClient) CleanupConnections() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 创建新的客户端实例来替换当前的客户端
	// 这会强制清理所有现有连接
	c.Client = &http.Client{
		Timeout: 60 * time.Second, // 设置超时
	}

	global.GVA_LOG.Info("HTTP连接池已清理")
}

// GetClient 获取HTTP客户端实例
func (c *MoreApiHttpClient) GetClient() *http.Client {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.Client
}

// DoRequest 发送HTTP请求的通用方法
func (c *MoreApiHttpClient) DoRequest(method, path string, reqBody map[string]any, respBody interface{}) error {
	if reqBody != nil && reqBody["proxy"] == "" {
		reqBody["proxy"] = global.GVA_CONFIG.Kuaidaili.TunnelURL
	}

	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("marshal request body failed: %w", err)
	}

	req := fasthttp.AcquireRequest()
	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(req)
	defer fasthttp.ReleaseResponse(resp)

	req.SetRequestURI(fmt.Sprintf("%s%s", c.BaseURL, path))
	req.Header.SetMethod(method)
	req.Header.SetContentType("application/json")

	// 修改连接头，使用 close 而不是 keep-alive
	req.Header.Set("Connection", "close")

	req.SetBody(jsonBody)

	// 记录请求开始时间
	requestStartTime := time.Now()
	global.GVA_LOG.Info(fmt.Sprintf("开始发送请求到 %s", path))

	// 设置更长的超时时间
	// err = c.GetClient().DoTimeout(req, resp, 60*time.Second)

	// 记录请求结束时间和耗时
	requestDuration := time.Since(requestStartTime)
	global.GVA_LOG.Info(fmt.Sprintf("请求完成，耗时: %v", requestDuration))

	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}

	if resp.StatusCode() != fasthttp.StatusOK {
		return fmt.Errorf("request failed with status code: %d, body: %s",
			resp.StatusCode(), string(resp.Body()))
	}

	// 先尝试解析响应
	var rawResp struct {
		Code int         `json:"code"`
		Data interface{} `json:"data"`
		Msg  string      `json:"msg"`
	}
	if err := json.Unmarshal(resp.Body(), &rawResp); err != nil {
		return fmt.Errorf("unmarshal response body failed: %w, body: %s", err, string(resp.Body()))
	}

	// 如果返回错误码,解码错误信息并返回
	if rawResp.Code != 0 {
		decodedMsg, _ := url.QueryUnescape(rawResp.Msg)
		return fmt.Errorf("api error: code=%d, msg=%s", rawResp.Code, decodedMsg)
	}

	// 解析实际响应数据
	if err := json.Unmarshal(resp.Body(), respBody); err != nil {
		return fmt.Errorf("unmarshal response body failed: %w, body: %s", err, string(resp.Body()))
	}

	global.GVA_LOG.Info(fmt.Sprintf("请求 %s 成功处理", path))
	return nil
}

// 发起表单请求
func (c *MoreApiHttpClient) DoFormRequest(method, path string, formData map[string]string, header map[string]string, respBody any) (err error) {
	if formData != nil && formData["proxy"] == "" {
		formData["proxy"] = global.GVA_CONFIG.Kuaidaili.TunnelURL
	}

	url := fmt.Sprintf("%s%s", c.BaseURL, path)

	// req := fasthttp.AcquireRequest()
	// resp := fasthttp.AcquireResponse()
	// defer fasthttp.ReleaseRequest(req)
	// defer fasthttp.ReleaseResponse(resp)

	// req.Header.SetMethod(method)
	// 改为 close 连接，避免连接复用问题
	// req.Header.Set("Connection", "close")
	// req.SetRequestURI(url)

	// 创建multipart writer
	var body bytes.Buffer
	writer := multipart.NewWriter(&body)
	for k, v := range formData {
		err = writer.WriteField(k, v)
		if err != nil {
			return fmt.Errorf("error writing field %s: %s -> %v", k, v, err)
		}
	}
	// 关闭writer以完成multipart构造
	err = writer.Close()
	if err != nil {
		return fmt.Errorf("error closing writer: %v", err)
	}

	fmt.Printf("请求地址:%s,头部header: %+v\n", path, header)
	// req.Header.Set("Content-Type", writer.FormDataContentType())
	// for k, v := range header {
	// 	req.Header.Set(k, v)
	// }
	// req.SetBody(body.Bytes())

	// 动态设置超时时间，默认30秒
	// timeoutSec := 30
	// if _, ok := formData["timeoutSec"]; ok {
	// 	if timeout, parseErr := strconv.Atoi(formData["timeoutSec"]); parseErr == nil {
	// 		timeoutSec = timeout
	// 	}
	// }

	// 记录请求开始时间
	requestStartTime := time.Now()
	global.GVA_LOG.Info(fmt.Sprintf("发送表单请求到 %s", url))

	requ, err := http.NewRequest(
		method,
		url,
		&body,
	)
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}
	requ.Header.Set("Content-Type", writer.FormDataContentType())
	// 改为 close 连接，避免连接复用问题
	// requ.Header.Set("Connection", "close")
	for k, v := range header {
		requ.Header.Set(k, v)
	}

	res, err := c.Client.Do(requ)
	if err != nil {
		return fmt.Errorf("请求失败: %+v", err)
	}
	defer res.Body.Close()

	// err = c.GetClient().DoTimeout(req, resp, time.Duration(timeoutSec)*time.Second)

	// 记录请求耗时
	requestDuration := time.Since(requestStartTime)
	global.GVA_LOG.Info(fmt.Sprintf("请求完成，耗时: %v", requestDuration))

	if err != nil {
		return fmt.Errorf("url: %s 请求失败: %v", url, err)
	}

	// 检查HTTP状态码
	respBytes, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %+v", err)
	}
	if res.StatusCode != http.StatusOK {
		return fmt.Errorf("url: %s 返回非200状态码: %d, 响应: %s", url, res.StatusCode, string(respBytes))
	}

	err = json.Unmarshal(respBytes, respBody)
	if err != nil {
		return fmt.Errorf("解析响应失败: %v, 响应体: %s", err, string(respBytes))
	}

	global.GVA_LOG.Info(fmt.Sprintf("表单请求 %s 成功完成", url))
	return nil
}

// 发送HTTP请求(JSON格式)
func (c *MoreApiHttpClient) DoJsonRequest(method, path string, reqBody map[string]any, respBody interface{}) error {
	if reqBody != nil && reqBody["proxy"] == "" {
		reqBody["proxy"] = global.GVA_CONFIG.Kuaidaili.TunnelURL
	}
	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("marshal request body failed: %w", err)
	}
	req := fasthttp.AcquireRequest()
	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(req)
	defer fasthttp.ReleaseResponse(resp)

	req.SetRequestURI(fmt.Sprintf("%s%s", c.BaseURL, path))
	req.Header.SetMethod(method)
	req.Header.SetContentType("application/json")

	// 修改连接头，使用 close 而不是 keep-alive
	req.Header.Set("Connection", "close")

	req.SetBody(jsonBody)

	// 记录请求开始时间
	requestStartTime := time.Now()

	// err = c.GetClient().DoTimeout(req, resp, 60*time.Second)
	// if err != nil {
	// 	return fmt.Errorf("url: %s 请求失败 %v", path, err)
	// }

	// 记录请求结束时间和耗时
	requestDuration := time.Since(requestStartTime)
	global.GVA_LOG.Info(fmt.Sprintf("请求完成，耗时: %v", requestDuration))
	// 解析实际响应数据
	if err := json.Unmarshal(resp.Body(), respBody); err != nil {
		return fmt.Errorf("unmarshal response body failed: %w, body: %s", err, string(resp.Body()))
	}
	return nil // 请求成功，直接返回
}

// DoMultipartRequest 发送multipart/form-data请求，支持文件上传
func (c *MoreApiHttpClient) DoMultipartRequest(
	method, path string,
	formData map[string]string,
	files map[string]string,
	remoteUrlMap map[string]string,
	respBody interface{},
) error {
	if formData != nil && formData["proxy"] == "" {
		formData["proxy"] = global.GVA_CONFIG.Kuaidaili.TunnelURL
	}
	url := fmt.Sprintf("%s%s", c.BaseURL, path)
	req := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(req)

	req.Header.SetMethod(method)
	req.SetRequestURI(url)

	// 设置连接为 close，避免连接复用问题
	req.Header.Set("Connection", "close")

	// 创建multipart writer
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// 添加普通表单字段
	for k, v := range formData {
		if err := writer.WriteField(k, v); err != nil {
			return fmt.Errorf("写入表单字段失败 %s: %v", k, err)
		}
	}

	if remoteUrlMap != nil {
		// 获取当前工作目录
		tempDir := filepath.Join(global.GVA_CONFIG.Local.Path, "post_temp")
		if err := utils.CreateDir(tempDir); err != nil {
			return fmt.Errorf("创建临时目录失败: %v", err)
		}
		for k, remoteURL := range remoteUrlMap {
			// 2. 生成文件名: 时间戳+随机字符串
			timestamp := time.Now().UnixNano()
			fileName := fmt.Sprintf("%d_%s", timestamp, filepath.Base(remoteURL))

			// 3. 下载远程文件
			filePath := filepath.Join(tempDir, fileName)
			resp, err := http.Get(remoteURL)
			if err != nil {
				return fmt.Errorf("下载远程文件失败: %v", err)
			}
			defer resp.Body.Close()

			out, err := os.Create(filePath)
			if err != nil {
				return fmt.Errorf("创建本地文件失败: %v", err)
			}
			defer out.Close()

			_, err = io.Copy(out, resp.Body)
			if err != nil {
				return fmt.Errorf("保存文件内容失败: %v", err)
			}
			defer os.Remove(filePath)

			if files == nil {
				files = make(map[string]string)
			}
			files[k] = filePath
		}
	}

	// 添加文件
	for fieldName, filePath := range files {
		file, err := os.Open(filePath)
		if err != nil {
			return fmt.Errorf("打开文件失败 %s: %v", filePath, err)
		}
		defer file.Close()

		part, err := writer.CreateFormFile(fieldName, filepath.Base(filePath))
		if err != nil {
			return fmt.Errorf("创建文件表单失败: %v", err)
		}

		if _, err := io.Copy(part, file); err != nil {
			return fmt.Errorf("复制文件内容失败: %v", err)
		}
	}

	// 关闭writer
	if err := writer.Close(); err != nil {
		return fmt.Errorf("关闭writer失败: %v", err)
	}

	req.Header.SetContentType(writer.FormDataContentType())
	req.SetBody(body.Bytes())

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	// if err := c.GetClient().DoTimeout(req, resp, 60*time.Second); err != nil {
	// 	return fmt.Errorf("请求失败: %v", err)
	// }

	if err := json.Unmarshal(resp.Body(), respBody); err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	return nil
}

// 文件列表上传
func (c *MoreApiHttpClient) DoMultipartFileListRequest(
	method, path string,
	formData map[string]string,
	remoteUrlMap map[string][]string,
	fileMap map[string][]string,
	respBody interface{},
) error {
	if formData != nil && formData["proxy"] == "" {
		formData["proxy"] = global.GVA_CONFIG.Kuaidaili.TunnelURL
	}
	url := fmt.Sprintf("%s%s", c.BaseURL, path)
	req := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(req)

	req.Header.SetMethod(method)
	req.SetRequestURI(url)

	// 创建multipart writer
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// 添加普通表单字段
	for k, v := range formData {
		if err := writer.WriteField(k, v); err != nil {
			return fmt.Errorf("写入表单字段失败 %s: %v", k, err)
		}
	}

	if remoteUrlMap != nil {
		// 获取当前工作目录
		tempDir := filepath.Join(global.GVA_CONFIG.Local.Path, "multi_post_temp")
		if err := utils.CreateDir(tempDir); err != nil {
			return fmt.Errorf("创建临时目录失败: %v", err)
		}
		for k, remoteUrls := range remoteUrlMap {
			for _, remoteURL := range remoteUrls {
				// 2. 生成文件名: 时间戳+随机字符串
				timestamp := time.Now().UnixNano()
				fileName := fmt.Sprintf("%d_%s", timestamp, filepath.Base(remoteURL))

				// 3. 下载远程文件
				filePath := filepath.Join(tempDir, fileName)
				resp, err := http.Get(remoteURL)
				if err != nil {
					return fmt.Errorf("下载远程文件失败: %v", err)
				}
				defer resp.Body.Close()

				out, err := os.Create(filePath)
				if err != nil {
					return fmt.Errorf("创建本地文件失败: %v", err)
				}
				defer out.Close()

				_, err = io.Copy(out, resp.Body)
				if err != nil {
					return fmt.Errorf("保存文件内容失败: %v", err)
				}
				defer os.Remove(filePath)

				if fileMap == nil {
					fileMap = make(map[string][]string)
				}
				fileMap[k] = append(fileMap[k], filePath)
			}
		}
	}

	// 添加文件
	for fieldName, files := range fileMap {
		for _, filePath := range files {
			file, err := os.Open(filePath)
			if err != nil {
				return fmt.Errorf("打开文件失败 %s: %v", filePath, err)
			}
			defer file.Close()

			part, err := writer.CreateFormFile(fieldName, filepath.Base(filePath))
			if err != nil {
				return fmt.Errorf("创建文件表单失败: %v", err)
			}

			if _, err := io.Copy(part, file); err != nil {
				return fmt.Errorf("复制文件内容失败: %v", err)
			}
		}
	}

	// 关闭writer
	if err := writer.Close(); err != nil {
		return fmt.Errorf("关闭writer失败: %v", err)
	}

	req.Header.SetContentType(writer.FormDataContentType())
	req.SetBody(body.Bytes())

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	// if err := c.GetClient().DoTimeout(req, resp, 60*time.Second); err != nil {
	// 	return fmt.Errorf("请求失败: %v", err)
	// }

	if err := json.Unmarshal(resp.Body(), respBody); err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	return nil
}
