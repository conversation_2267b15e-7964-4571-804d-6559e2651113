package douyin

import (
	"errors"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	dyRequest "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	dyResponse "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
	"gorm.io/gorm"
)

type DyProductCategoryService struct{}

// CreateProductCategory 创建商品分类
func (s *DyProductCategoryService) CreateProductCategory(req dyRequest.DyProductCategoryRequest, userID uint) error {
	// 检查分类名称是否已存在
	var count int64
	err := global.GVA_DB.Model(&douyin.DyProductCategory{}).Where("name = ?", req.Name).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("分类名称已存在")
	}

	// 获取当前最小的排序值，新增的分类排到第一个
	var minSortOrder int
	err = global.GVA_DB.Model(&douyin.DyProductCategory{}).Select("COALESCE(MIN(sort_order), 0)").Scan(&minSortOrder).Error
	if err != nil {
		return err
	}

	// 新分类的排序值比最小值小1，确保排在第一个
	newSortOrder := minSortOrder - 1

	category := douyin.DyProductCategory{
		Name:      req.Name,
		SortOrder: newSortOrder,
		Status:    req.Status,
		CreatorID: userID,
	}

	if category.Status == 0 {
		category.Status = 1 // 默认启用
	}

	return global.GVA_DB.Create(&category).Error
}

// UpdateProductCategory 更新商品分类
func (s *DyProductCategoryService) UpdateProductCategory(id uint, req dyRequest.DyProductCategoryRequest) error {
	// 检查分类是否存在
	var category douyin.DyProductCategory
	err := global.GVA_DB.Where("id = ?", id).First(&category).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("分类不存在")
		}
		return err
	}

	// 检查分类名称是否与其他分类重复
	var count int64
	err = global.GVA_DB.Model(&douyin.DyProductCategory{}).Where("name = ? AND id != ?", req.Name, id).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("分类名称已存在")
	}

	updates := map[string]interface{}{
		"name":   req.Name,
		"status": req.Status,
	}

	return global.GVA_DB.Model(&category).Updates(updates).Error
}

// GetProductCategoryList 获取商品分类列表
func (s *DyProductCategoryService) GetProductCategoryList(req dyRequest.DyProductCategoryListRequest) (response dyResponse.DyProductCategoryListResponse, err error) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := global.GVA_DB.Model(&douyin.DyProductCategory{})

	// 构建查询条件
	if req.Name != "" {
		db = db.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.Status != nil {
		db = db.Where("status = ?", *req.Status)
	}

	var categories []douyin.DyProductCategory
	var total int64

	// 获取总数
	err = db.Count(&total).Error
	if err != nil {
		return response, err
	}

	// 获取列表（关联查询创建人信息）
	err = db.Preload("Creator").Limit(limit).Offset(offset).Order("sort_order ASC, created_at DESC").Find(&categories).Error
	if err != nil {
		return response, err
	}

	// 转换为响应结构
	var list []dyResponse.DyProductCategoryResponse
	for _, category := range categories {
		creatorName := ""
		if category.Creator.ID > 0 {
			creatorName = category.Creator.NickName
		}

		list = append(list, dyResponse.DyProductCategoryResponse{
			ID:           category.ID,
			CreatedAt:    category.CreatedAt,
			UpdatedAt:    category.UpdatedAt,
			Name:         category.Name,
			SortOrder:    category.SortOrder,
			ProductCount: category.ProductCount,
			CreatorID:    category.CreatorID,
			CreatorName:  creatorName,
			Status:       category.Status,
		})
	}

	response = dyResponse.DyProductCategoryListResponse{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	return response, nil
}

// GetProductCategoryById 根据ID获取商品分类
func (s *DyProductCategoryService) GetProductCategoryById(id uint) (category douyin.DyProductCategory, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&category).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return category, errors.New("分类不存在")
		}
		return category, err
	}
	return category, nil
}

// DeleteProductCategory 删除商品分类
func (s *DyProductCategoryService) DeleteProductCategory(id uint) error {
	// 检查分类是否存在
	var category douyin.DyProductCategory
	err := global.GVA_DB.Where("id = ?", id).First(&category).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("分类不存在")
		}
		return err
	}

	// 检查是否有商品使用此分类
	if category.ProductCount > 0 {
		return errors.New("该分类下有商品，无法删除")
	}

	return global.GVA_DB.Delete(&category).Error
}

// UpdateCategoryStatus 更新分类状态
func (s *DyProductCategoryService) UpdateCategoryStatus(id uint, status int) error {
	// 检查分类是否存在
	var category douyin.DyProductCategory
	err := global.GVA_DB.Where("id = ?", id).First(&category).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("分类不存在")
		}
		return err
	}

	return global.GVA_DB.Model(&category).Update("status", status).Error
}

// GetAllCategories 获取所有启用的分类（用于下拉选择）
func (s *DyProductCategoryService) GetAllCategories() (categories []douyin.DyProductCategory, err error) {
	err = global.GVA_DB.Where("status = ?", 1).Order("sort_order ASC, created_at DESC").Find(&categories).Error
	return categories, err
}

// UpdateCategorySort 更新分类排序
func (s *DyProductCategoryService) UpdateCategorySort(req dyRequest.UpdateCategorySortRequest) error {
	// 使用事务确保数据一致性
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		for _, item := range req.Categories {
			// 检查分类是否存在
			var category douyin.DyProductCategory
			err := tx.Where("id = ?", item.ID).First(&category).Error
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return errors.New("分类不存在")
				}
				return err
			}

			// 更新排序值
			err = tx.Model(&category).Update("sort_order", item.SortOrder).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}
