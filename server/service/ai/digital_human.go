package ai

import (
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	aiUtils "github.com/flipped-aurora/gin-vue-admin/server/utils/ai"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/video"
	"go.uber.org/zap"
)

type DigitalHumanService struct{}

// CreateDigitalHumanTask 创建数字人视频任务
func (s *DigitalHumanService) CreateDigitalHumanTask(req request.DigitalHumanTaskRequest, userId uint) (*response.DigitalHumanTaskResponse, error) {
	// 设置默认值
	if req.VideoSize == "" {
		req.VideoSize = "832x832"
	}
	if req.Fps == 0 {
		req.Fps = 16
	}

	// 解析视频尺寸
	var width, height int
	switch req.VideoSize {
	case "832x832":
		width, height = 832, 832
	case "720x1280":
		width, height = 720, 1280
	case "1280x720":
		width, height = 1280, 720
	default:
		width, height = 832, 832
	}

	// 创建RunningHub处理器并直接创建远端任务，拿到 RunningHub 的 taskId
	processor := aiUtils.NewRunningHubProcessor()

	// 默认工作流类型为1（不使用plus）
	workflowType := req.WorkflowType
	if workflowType == 0 {
		workflowType = 1
	}
	digitalHumanReq := aiUtils.DigitalHumanTaskRequest{
		Script:            req.Script,
		Action:            req.Action,
		CharacterImageUrl: req.CharacterImageUrl,
		ReferenceAudioUrl: req.ReferenceAudioUrl,
		Width:             width,
		Height:            height,
		Fps:               req.Fps,
		WorkflowType:      workflowType,
	}

	runningHubTaskId, err := processor.CreateTask(aiUtils.TaskTypeDigitalHuman, digitalHumanReq)
	if err != nil {
		global.GVA_LOG.Error("提交数字人任务到RunningHub失败", zap.Error(err))
		return nil, fmt.Errorf("提交到RunningHub失败: %v", err)
	}

	// 创建数字人任务记录（使用 RunningHub 的任务ID）
	task := &ai.DigitalHumanTask{
		TaskId:             runningHubTaskId,
		UserId:             userId,
		Script:             req.Script,
		Action:             req.Action,
		CharacterImageUrl:  req.CharacterImageUrl,
		ReferenceAudioUrl:  req.ReferenceAudioUrl,
		ReferenceAudioName: req.ReferenceAudioName,
		VideoSize:          req.VideoSize,
		Fps:                req.Fps,
		CategoryId:         req.CategoryId,
		Status:             "RUNNING",
		StartTime:          time.Now(),
	}

	if err := global.GVA_DB.Create(task).Error; err != nil {
		global.GVA_LOG.Error("保存数字人任务失败", zap.Error(err))
		return nil, fmt.Errorf("保存任务失败: %v", err)
	}

	global.GVA_LOG.Info("数字人任务已提交到RunningHub", zap.String("runningHubTaskId", runningHubTaskId))

	return &response.DigitalHumanTaskResponse{
		TaskId: runningHubTaskId,
	}, nil
}

// 删除异步提交逻辑，改为同步拿到 RunningHub 任务ID 后再创建记录

// GetDigitalHumanTaskStatus 获取数字人视频任务状态
func (s *DigitalHumanService) GetDigitalHumanTaskStatus(taskId string) (*ai.DigitalHumanTask, error) {
	var task ai.DigitalHumanTask
	if err := global.GVA_DB.Where("task_id = ?", taskId).First(&task).Error; err != nil {
		return nil, fmt.Errorf("任务不存在: %v", err)
	}

	// 如果任务还在进行中，检查RunningHub状态
	if task.Status == "RUNNING" || task.Status == "PENDING" {
		s.updateTaskStatusFromRunningHub(&task)
	}

	return &task, nil
}

// updateTaskStatusFromRunningHub 从RunningHub更新任务状态
func (s *DigitalHumanService) updateTaskStatusFromRunningHub(task *ai.DigitalHumanTask) {
	// 若任务ID为空，直接设置为失败状态
	if task.TaskId == "" {
		task.Status = "FAILED"
		task.ErrorMsg = "任务ID为空，无法查询状态"
		endTime := time.Now()
		task.EndTime = &endTime

		// 更新数据库
		updates := map[string]interface{}{
			"status":    "FAILED",
			"error_msg": "任务ID为空，无法查询状态",
			"end_time":  endTime,
		}
		if err := global.GVA_DB.Model(task).Updates(updates).Error; err != nil {
			global.GVA_LOG.Error("更新空任务ID的任务状态失败", zap.Error(err))
		}
		return
	}

	processor := aiUtils.NewRunningHubProcessor()

	updates, err := processor.GetTaskStatus(task.TaskId)
	if err != nil {
		global.GVA_LOG.Error("获取RunningHub任务状态失败", zap.Error(err))
		return
	}

	if len(updates) > 0 {
		// 过滤不在表中的字段，避免数据库更新报 Unknown column
		delete(updates, "audio_url")
		// 更新数据库中的任务状态
		if err := global.GVA_DB.Model(task).Updates(updates).Error; err != nil {
			global.GVA_LOG.Error("更新数字人任务状态失败", zap.Error(err))
		} else {
			// 更新内存中的任务对象
			if status, ok := updates["status"].(string); ok {
				task.Status = status
			}
			if videoUrl, ok := updates["video_url"].(string); ok {
				task.VideoUrl = videoUrl
				// 若任务成功，写入视频表
				if task.Status == "SUCCEEDED" && videoUrl != "" {
					s.saveSucceededVideo(task)
				}
			}
			if errorMsg, ok := updates["error_msg"].(string); ok {
				task.ErrorMsg = errorMsg
			}
			if endTime, ok := updates["end_time"].(time.Time); ok {
				task.EndTime = &endTime
			}
		}
	}
}

// saveSucceededVideo 将数字人生成成功的视频写入 video 表
func (s *DigitalHumanService) saveSucceededVideo(task *ai.DigitalHumanTask) {
	// 去重：同 task_id + url 不重复写入
	var existing creative.Video
	if err := global.GVA_DB.Where("task_id = ? AND url = ?", task.TaskId, task.VideoUrl).First(&existing).Error; err == nil {
		return
	}

	global.GVA_LOG.Info("开始处理数字人视频",
		zap.String("taskId", task.TaskId),
		zap.String("videoUrl", task.VideoUrl))

	// 使用 VideoFrameExtractor 上传到VOD并提取封面和时长
	extractor := video.NewVideoFrameExtractor()
	defer extractor.Cleanup()

	// 配置：从URL上传到VOD，提取首帧到OSS，获取时长
	result, err := extractor.
		WithVideoURL(task.VideoUrl).
		WithVodUploadFromUrl(fmt.Sprintf("数字人_%s", task.TaskId)).
		WithDuration(true).
		WithOSSUpload(true).
		ExtractWithResult()

	if err != nil {
		global.GVA_LOG.Error("视频处理失败，使用原始信息保存",
			zap.Error(err),
			zap.String("taskId", task.TaskId))
		// 处理失败时仍保存基本信息
		s.saveVideoRecord(task, "", task.VideoUrl, task.Duration)
		return
	}

	// 成功处理：使用VOD URL、封面、时长
	vodUrl := result.VodFileURL
	if vodUrl == "" {
		vodUrl = task.VideoUrl // 备用原始URL
	}

	cover := result.ImageURL
	duration := result.VideoDuration
	if duration == 0 {
		duration = task.Duration // 备用原始时长
	}

	global.GVA_LOG.Info("视频处理成功",
		zap.String("taskId", task.TaskId),
		zap.String("vodUrl", vodUrl),
		zap.String("cover", cover),
		zap.Int64("duration", duration),
		zap.String("vodMediaId", result.VodMediaId))

	// 保存到video表
	s.saveVideoRecord(task, cover, vodUrl, duration)

	// 更新任务表中的MediaId、Duration、VideoUrl
	updates := map[string]interface{}{
		"video_url": vodUrl,
		"duration":  duration,
	}
	if result.VodMediaId != "" {
		// 如果任务表有media_id字段，可以更新
		// updates["media_id"] = result.VodMediaId
	}

	if err := global.GVA_DB.Model(task).Updates(updates).Error; err != nil {
		global.GVA_LOG.Error("更新任务VOD信息失败", zap.Error(err))
	}
}

// saveVideoRecord 保存视频记录到video表
func (s *DigitalHumanService) saveVideoRecord(task *ai.DigitalHumanTask, cover, videoUrl string, duration int64) {
	title := task.Action
	if title == "" {
		title = fmt.Sprintf("数字人视频_%s", task.TaskId)
	}

	video := creative.Video{
		CategoryId:  task.CategoryId,
		Title:       title,
		Cover:       cover,
		Url:         videoUrl,
		Status:      creative.VideoStatusPending,
		CreatedBy:   task.UserId,
		Source:      creative.VideoSourceAI,
		Topic:       "",
		TaskId:      task.TaskId,
		Duration:    duration,
		ContentType: creative.ContentTypeVideo, // 视频类型
	}

	if err := global.GVA_DB.Create(&video).Error; err != nil {
		global.GVA_LOG.Error("保存数字人视频到video表失败", zap.Error(err))
	} else {
		global.GVA_LOG.Info("数字人视频保存成功",
			zap.String("taskId", task.TaskId),
			zap.String("videoUrl", videoUrl))
	}
}

// 已去除 isLikelyRunningHubTaskID，统一由上游保证 taskId 即 RunningHub 返回的ID

// GetDigitalHumanTaskList 获取数字人视频任务列表
func (s *DigitalHumanService) GetDigitalHumanTaskList(req request.DigitalHumanTaskListRequest, userId uint) ([]ai.DigitalHumanTask, int64, error) {
	var tasks []ai.DigitalHumanTask
	var total int64

	db := global.GVA_DB.Model(&ai.DigitalHumanTask{}).Where("user_id = ?", userId)

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取任务总数失败: %v", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := db.Order("created_at DESC").Offset(offset).Limit(req.PageSize).Find(&tasks).Error; err != nil {
		return nil, 0, fmt.Errorf("获取任务列表失败: %v", err)
	}

	// 批量更新任务状态
	for i := range tasks {
		if tasks[i].Status == "RUNNING" || tasks[i].Status == "PENDING" {
			s.updateTaskStatusFromRunningHub(&tasks[i])
		}
	}

	return tasks, total, nil
}

// CreateHeygemDigitalHumanTask 创建HeyGem数字人视频任务
func (s *DigitalHumanService) CreateHeygemDigitalHumanTask(req request.HeygemDigitalHumanTaskRequest, userId uint) (*response.DigitalHumanTaskResponse, error) {
	// 验证模式和对应的必需字段
	if req.Mode != 1 && req.Mode != 2 {
		return nil, fmt.Errorf("无效的模式，必须是1（音频+视频）或2（参考音频+台词+视频）")
	}

	if req.Mode == 1 && req.DirectAudioUrl == "" {
		return nil, fmt.Errorf("模式1需要提供直接音频URL")
	}

	if req.Mode == 2 && (req.ReferenceAudioUrl == "" || req.Script == "") {
		return nil, fmt.Errorf("模式2需要提供参考音频URL和台词内容")
	}

	// 创建RunningHub处理器并直接创建远端任务，拿到 RunningHub 的 taskId
	processor := aiUtils.NewRunningHubProcessor()

	// 根据模式设置不同的参数
	var audioUrl, script, action string

	if req.Mode == 1 {
		// 模式1：直接使用音频文件
		audioUrl = req.DirectAudioUrl
		script = "HeyGem极速生成-模式1"
		action = "音频驱动数字人"
	} else {
		// 模式2：使用参考音频和脚本
		audioUrl = req.ReferenceAudioUrl
		script = req.Script
		action = "语音克隆数字人"
	}

	// HeyGem工作流固定为类型3
	digitalHumanReq := aiUtils.DigitalHumanTaskRequest{
		Script:            script,
		Action:            action,
		CharacterVideoUrl: req.CharacterVideoUrl, // HeyGem使用视频文件
		ReferenceAudioUrl: audioUrl,
		Width:             832,      // HeyGem固定尺寸
		Height:            832,      // HeyGem固定尺寸
		Fps:               16,       // HeyGem固定帧率
		WorkflowType:      3,        // HeyGem工作流类型
		HeygemMode:        req.Mode, // 传递模式信息
	}

	runningHubTaskId, err := processor.CreateTask(aiUtils.TaskTypeDigitalHuman, digitalHumanReq)
	if err != nil {
		global.GVA_LOG.Error("提交HeyGem数字人任务到RunningHub失败", zap.Error(err))
		return nil, fmt.Errorf("提交到RunningHub失败: %v", err)
	}

	// 创建HeyGem数字人任务记录（使用 RunningHub 的任务ID）
	task := &ai.DigitalHumanTask{
		TaskId:             runningHubTaskId,
		UserId:             userId,
		Script:             script,
		Action:             action,
		CharacterImageUrl:  req.CharacterVideoUrl, // 兼容性字段，存储视频URL
		CharacterVideoUrl:  req.CharacterVideoUrl,
		ReferenceAudioUrl:  req.ReferenceAudioUrl,
		ReferenceAudioName: req.ReferenceAudioName,
		VideoSize:          "832x832",
		Fps:                16,
		CategoryId:         req.CategoryId,
		HeygemMode:         req.Mode,
		DirectAudioUrl:     req.DirectAudioUrl,
		DirectAudioName:    req.DirectAudioName,
		Status:             "RUNNING",
		StartTime:          time.Now(),
	}

	if err := global.GVA_DB.Create(task).Error; err != nil {
		global.GVA_LOG.Error("保存HeyGem数字人任务失败", zap.Error(err))
		return nil, fmt.Errorf("保存任务失败: %v", err)
	}

	global.GVA_LOG.Info("HeyGem数字人任务已提交到RunningHub", zap.String("runningHubTaskId", runningHubTaskId))

	return &response.DigitalHumanTaskResponse{
		TaskId: runningHubTaskId,
	}, nil
}

// GetHeygemTaskList 获取HeyGem数字人视频任务列表
func (s *DigitalHumanService) GetHeygemTaskList(req request.DigitalHumanTaskListRequest, userId uint) ([]ai.DigitalHumanTask, int64, error) {
	var tasks []ai.DigitalHumanTask
	var total int64

	// 只查询HeyGem任务（script字段为"HeyGem极速生成"的任务）
	db := global.GVA_DB.Model(&ai.DigitalHumanTask{}).Where("user_id = ? AND script = ?", userId, "HeyGem极速生成")

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取HeyGem任务总数失败: %v", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := db.Order("created_at DESC").Offset(offset).Limit(req.PageSize).Find(&tasks).Error; err != nil {
		return nil, 0, fmt.Errorf("获取HeyGem任务列表失败: %v", err)
	}

	// 批量更新任务状态
	for i := range tasks {
		if tasks[i].Status == "RUNNING" || tasks[i].Status == "PENDING" {
			s.updateTaskStatusFromRunningHub(&tasks[i])
		}
	}

	return tasks, total, nil
}
