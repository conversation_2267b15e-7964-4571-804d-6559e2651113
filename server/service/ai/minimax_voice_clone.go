package ai

import (
	"bytes"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai"
	aiRequest "github.com/flipped-aurora/gin-vue-admin/server/model/ai/request"
	aiResponse "github.com/flipped-aurora/gin-vue-admin/server/model/ai/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/media"
	utilsAi "github.com/flipped-aurora/gin-vue-admin/server/utils/ai"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/upload"
	"go.uber.org/zap"
)

// MinimaxMusicServiceInterface 音乐服务接口，避免循环依赖
type MinimaxMusicServiceInterface interface {
	SaveMinimaxVoiceCloneToLibrary(taskId uint, audioUrl string, categoryId uint, userId uint, backgroundMusicId uint) (musicUrl string, duration int64, mediaId string, err error)
}

type MinimaxVoiceCloneService struct {
	MusicService MinimaxMusicServiceInterface
}

// CreateMinimaxVoiceCloneTask 创建 MiniMax 人声克隆任务
func (s *MinimaxVoiceCloneService) CreateMinimaxVoiceCloneTask(
	req aiRequest.MinimaxVoiceCloneTaskRequest,
	userId uint,
) (aiResponse.MinimaxVoiceCloneTaskResponse, error) {
	// 验证必需参数
	if req.Text == "" {
		return aiResponse.MinimaxVoiceCloneTaskResponse{}, fmt.Errorf("文本不能为空")
	}

	// 验证音色参数：必须有官方音色ID或自定义音色ID/URL
	if req.OfficialVoiceId == "" && req.ReferenceAudioId == 0 && req.ReferenceAudioUrl == "" {
		return aiResponse.MinimaxVoiceCloneTaskResponse{}, fmt.Errorf("必须选择官方音色或自定义音色")
	}

	// 设置默认值
	if req.Model == "" {
		req.Model = "speech-02-hd"
	}
	if req.Speed == 0 {
		req.Speed = 1.0
	}
	if req.Volume == 0 {
		req.Volume = 1.0
	}
	if req.Pitch == 0 {
		req.Pitch = 0.0
	}
	if req.AudioSampleRate == 0 {
		req.AudioSampleRate = 32000
	}
	if req.BitRate == 0 {
		req.BitRate = 128000
	}
	if req.Format == "" {
		req.Format = "mp3"
	}

	var voiceId string

	// 处理音色ID
	if req.OfficialVoiceId != "" {
		// 使用官方音色ID
		voiceId = req.OfficialVoiceId
		global.GVA_LOG.Info("使用官方音色", zap.String("voiceId", voiceId))
	} else {
		// 使用自定义音色，需要先克隆
		var audioUrl string
		if req.ReferenceAudioId > 0 {
			// 如果传入了ID，需要查询音乐记录获取URL
			var musicRecord media.Music
			err := global.GVA_DB.First(&musicRecord, req.ReferenceAudioId).Error
			if err != nil {
				global.GVA_LOG.Error("查询音乐记录失败", zap.Uint("id", req.ReferenceAudioId), zap.Error(err))
				return aiResponse.MinimaxVoiceCloneTaskResponse{}, fmt.Errorf("查询音乐记录失败: %v", err)
			}
			audioUrl = musicRecord.FileURL
		} else {
			audioUrl = req.ReferenceAudioUrl
		}

		// 下载参考音频文件
		resp, err := http.Get(audioUrl)
		if err != nil {
			global.GVA_LOG.Error("下载参考音频文件失败", zap.String("url", audioUrl), zap.Error(err))
			return aiResponse.MinimaxVoiceCloneTaskResponse{}, fmt.Errorf("下载参考音频文件失败: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			return aiResponse.MinimaxVoiceCloneTaskResponse{}, fmt.Errorf("下载参考音频文件失败，状态码: %d", resp.StatusCode)
		}

		audioData, err := io.ReadAll(resp.Body)
		if err != nil {
			global.GVA_LOG.Error("读取参考音频文件失败", zap.Error(err))
			return aiResponse.MinimaxVoiceCloneTaskResponse{}, fmt.Errorf("读取参考音频文件失败: %v", err)
		}

		// 生成文件名
		fileName := fmt.Sprintf("reference_audio_%d.mp3", time.Now().Unix())

		// 检查是否已经有对应的 minimax_voice_id
		var existingMusic *media.Music
		var queryErr error

		// 优先使用ID查询，如果没有ID则使用URL查询
		if req.ReferenceAudioId > 0 {
			existingMusic = &media.Music{}
			queryErr = global.GVA_DB.Where("id = ?", req.ReferenceAudioId).First(existingMusic).Error
			if queryErr != nil {
				global.GVA_LOG.Error("查询音色记录失败", zap.Uint("id", req.ReferenceAudioId), zap.Error(queryErr))
				return aiResponse.MinimaxVoiceCloneTaskResponse{}, fmt.Errorf("查询音色记录失败: %v", queryErr)
			}
		}

		if queryErr == nil && existingMusic.MinimaxVoiceId != "" {
			// 已存在 minimax_voice_id，直接使用
			voiceId = existingMusic.MinimaxVoiceId
			global.GVA_LOG.Info("使用已存在的 minimax_voice_id", zap.String("voiceId", voiceId))
		} else {
			// 需要创建新的 minimax_voice_id
			processor := utilsAi.NewMinimaxProcessor()

			// 1. 上传音频文件
			uploadResp, err := processor.UploadAudioFile(audioData, fileName)
			if err != nil {
				global.GVA_LOG.Error("上传音频文件到 MiniMax 失败", zap.Error(err))
				return aiResponse.MinimaxVoiceCloneTaskResponse{}, fmt.Errorf("上传音频文件失败: %v", err)
			}

			// 2. 生成唯一的 minimax_voice_id
			voiceId = fmt.Sprintf("voice_%d_%d", userId, time.Now().Unix())

			// 3. 克隆音色
			cloneResp, err := processor.CloneVoice(uploadResp.File.FileId, voiceId)
			if err != nil {
				global.GVA_LOG.Error("克隆音色失败", zap.Error(err))
				return aiResponse.MinimaxVoiceCloneTaskResponse{}, fmt.Errorf("克隆音色失败: %v", err)
			}

			global.GVA_LOG.Info("音色克隆成功", zap.String("voiceId", voiceId), zap.Any("response", cloneResp))

			// 4. 更新音乐库中的 minimax_voice_id
			if existingMusic != nil {
				updateErr := global.GVA_DB.Model(existingMusic).Where("id = ?", existingMusic.ID).
					Update("minimax_voice_id", voiceId).Error
				if updateErr != nil {
					global.GVA_LOG.Error("更新音乐记录的 minimax_voice_id 失败", zap.Error(updateErr))
				}
			}
		}
	}

	// 生成任务ID
	taskId := fmt.Sprintf("minimax_%d_%d", userId, time.Now().Unix())

	// 创建任务记录
	task := ai.MinimaxVoiceCloneTask{
		TaskId:                taskId,
		UserId:                userId,
		Text:                  req.Text,
		ReferenceAudioName:    req.ReferenceAudioName,
		MinimaxVoiceId:        voiceId,
		Model:                 req.Model,
		Speed:                 req.Speed,
		Volume:                req.Volume,
		Pitch:                 req.Pitch,
		AudioSampleRate:       req.AudioSampleRate,
		BitRate:               req.BitRate,
		Format:                req.Format,
		TargetCategoryId:      req.TargetCategoryId,
		BackgroundMusicId:     req.BackgroundMusicId,
		BackgroundMusicVolume: req.BackgroundMusicVolume,
		Status:                "RUNNING",
		StartTime:             time.Now(),
	}

	err := global.GVA_DB.Create(&task).Error
	if err != nil {
		global.GVA_LOG.Error("创建 MiniMax 人声克隆任务记录失败", zap.Error(err))
		return aiResponse.MinimaxVoiceCloneTaskResponse{}, err
	}

	// 异步执行文本转语音
	go s.processTextToAudio(task.ID, voiceId, req)

	return aiResponse.MinimaxVoiceCloneTaskResponse{TaskId: taskId}, nil
}

// processTextToAudio 处理文本转语音
func (s *MinimaxVoiceCloneService) processTextToAudio(
	taskId uint, voiceId string, req aiRequest.MinimaxVoiceCloneTaskRequest,
) {
	processor := utilsAi.NewMinimaxProcessor()

	// 调用文本转语音 API
	t2aResp, err := processor.TextToAudio(
		req.Text, voiceId, req.Model,
		req.Speed, req.Volume, req.Pitch,
		req.AudioSampleRate, req.BitRate, req.Format,
	)

	updateData := map[string]interface{}{
		"end_time": time.Now(),
	}

	if err != nil {
		global.GVA_LOG.Error("文本转语音失败", zap.Uint("taskId", taskId), zap.Error(err))
		updateData["status"] = "FAILED"
		updateData["error_msg"] = err.Error()
	} else {
		global.GVA_LOG.Info("文本转语音任务完成",
			zap.Uint("taskId", taskId),
			zap.String("traceId", t2aResp.TraceId))

		// 解码hex音频数据
		audioData, err := hex.DecodeString(t2aResp.Data.Audio)
		if err != nil {
			global.GVA_LOG.Error("解码音频数据失败", zap.Uint("taskId", taskId), zap.Error(err))
			updateData["status"] = "FAILED"
			updateData["error_msg"] = fmt.Sprintf("解码音频数据失败: %v", err)
		} else {
			// 处理背景音乐混音
			finalAudioData, err := s.processBackgroundMusicMixing(audioData, req.BackgroundMusicId, req.BackgroundMusicVolume)
			if err != nil {
				global.GVA_LOG.Error("背景音乐混音失败", zap.Uint("taskId", taskId), zap.Error(err))
				// 如果混音失败，使用原始音频数据
				finalAudioData = audioData
			}

			// 上传到阿里云并获取时长信息
			audioUrl, _, err := s.uploadAudioToAliyun(finalAudioData, req.Format)
			if err != nil {
				global.GVA_LOG.Error("上传音频到阿里云失败", zap.Uint("taskId", taskId), zap.Error(err))
				updateData["status"] = "FAILED"
				updateData["error_msg"] = fmt.Sprintf("上传音频失败: %v", err)
			} else {
				updateData["status"] = "SUCCEEDED"
				updateData["audio_url"] = audioUrl
				// 使用API返回的音频时长，单位转换为秒
				updateData["duration"] = int64(t2aResp.ExtraInfo.AudioLength / 1000)

				// 保存到音乐库
				if s.MusicService != nil && req.TargetCategoryId > 0 {
					var task ai.MinimaxVoiceCloneTask
					global.GVA_DB.First(&task, taskId)

					musicUrl, musicDuration, mediaId, err := s.MusicService.SaveMinimaxVoiceCloneToLibrary(
						taskId, audioUrl, req.TargetCategoryId, task.UserId, req.BackgroundMusicId,
					)
					if err != nil {
						global.GVA_LOG.Error("保存音频到音乐库失败", zap.Uint("taskId", taskId), zap.Error(err))
					} else {
						if musicUrl != "" {
							updateData["audio_url"] = musicUrl
						}
						if musicDuration > 0 {
							updateData["duration"] = musicDuration
						}
						if mediaId != "" {
							updateData["media_id"] = mediaId
						}
					}
				}
			}
		}
	}

	// 更新任务状态
	err = global.GVA_DB.Model(&ai.MinimaxVoiceCloneTask{}).Where("id = ?", taskId).Updates(updateData).Error
	if err != nil {
		global.GVA_LOG.Error("更新 MiniMax 任务状态失败", zap.Uint("taskId", taskId), zap.Error(err))
	}
}

// GetMinimaxVoiceCloneTaskStatus 获取 MiniMax 人声克隆任务状态
func (s *MinimaxVoiceCloneService) GetMinimaxVoiceCloneTaskStatus(taskId string) (*ai.MinimaxVoiceCloneTask, error) {
	var task ai.MinimaxVoiceCloneTask
	if err := global.GVA_DB.Where("task_id = ?", taskId).First(&task).Error; err != nil {
		return nil, err
	}
	return &task, nil
}

// GetMinimaxVoiceCloneTaskList 获取 MiniMax 人声克隆任务列表
func (s *MinimaxVoiceCloneService) GetMinimaxVoiceCloneTaskList(
	req aiRequest.MinimaxVoiceCloneTaskListRequest,
	userId uint,
) ([]ai.MinimaxVoiceCloneTask, int64, error) {
	var tasks []ai.MinimaxVoiceCloneTask
	var total int64

	db := global.GVA_DB.Model(&ai.MinimaxVoiceCloneTask{}).Where("user_id = ?", userId)

	// 统计总数
	db.Count(&total)

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	err := db.Order("created_at DESC").Offset(offset).Limit(req.PageSize).Find(&tasks).Error

	return tasks, total, err
}

// GetOfficialVoices 获取MiniMax官方音色列表
func (s *MinimaxVoiceCloneService) GetOfficialVoices() ([]utilsAi.MinimaxVoiceInfo, error) {
	processor := utilsAi.NewMinimaxProcessor()

	resp, err := processor.GetOfficialVoices()
	if err != nil {
		global.GVA_LOG.Error("获取MiniMax官方音色列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取官方音色列表失败: %v", err)
	}

	// 转换为统一格式
	voices := processor.ConvertToUnifiedVoiceList(resp)
	return voices, nil
}

// uploadAudioToAliyun 上传音频到阿里云
func (s *MinimaxVoiceCloneService) uploadAudioToAliyun(audioData []byte, format string) (string, int64, error) {
	// 生成文件名
	fileName := fmt.Sprintf("minimax_voice_%d.%s", time.Now().Unix(), format)

	// 创建文件读取器
	reader := bytes.NewReader(audioData)

	// 使用阿里云OSS上传
	ossUploader := &upload.AliyunOSS{}
	audioUrl, _, err := ossUploader.UploadFileFromReader(reader, fileName, false)
	if err != nil {
		return "", 0, fmt.Errorf("上传到阿里云失败: %v", err)
	}

	return audioUrl, 0, nil
}

// processBackgroundMusicMixing 处理背景音乐混音
func (s *MinimaxVoiceCloneService) processBackgroundMusicMixing(
	voiceData []byte, backgroundMusicId uint, volume float64,
) ([]byte, error) {
	// 如果没有背景音乐，直接返回原始音频
	if backgroundMusicId == 0 {
		return voiceData, nil
	}

	// 如果音量为0，也直接返回原始音频
	if volume == 0 {
		return voiceData, nil
	}

	// 查询背景音乐信息
	var backgroundMusic struct {
		FileURL  string `json:"file_url"`
		Name     string `json:"name"`
		Duration int64  `json:"duration"`
	}

	err := global.GVA_DB.Table("media_music").
		Select("file_url, name, duration").
		Where("id = ?", backgroundMusicId).
		First(&backgroundMusic).Error

	if err != nil {
		global.GVA_LOG.Error("查询背景音乐失败", zap.Uint("backgroundMusicId", backgroundMusicId), zap.Error(err))
		return voiceData, fmt.Errorf("查询背景音乐失败: %v", err)
	}

	// 下载背景音乐
	resp, err := http.Get(backgroundMusic.FileURL)
	if err != nil {
		global.GVA_LOG.Error("下载背景音乐失败", zap.String("url", backgroundMusic.FileURL), zap.Error(err))
		return voiceData, fmt.Errorf("下载背景音乐失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return voiceData, fmt.Errorf("下载背景音乐失败，状态码: %d", resp.StatusCode)
	}

	backgroundMusicData, err := io.ReadAll(resp.Body)
	if err != nil {
		global.GVA_LOG.Error("读取背景音乐数据失败", zap.Error(err))
		return voiceData, fmt.Errorf("读取背景音乐数据失败: %v", err)
	}

	// 使用FFmpeg进行音频混音
	mixedAudioData, err := s.mixAudioWithFFmpeg(voiceData, backgroundMusicData, volume)
	if err != nil {
		global.GVA_LOG.Error("FFmpeg混音失败", zap.Error(err))
		return voiceData, fmt.Errorf("FFmpeg混音失败: %v", err)
	}

	global.GVA_LOG.Info("背景音乐混音成功",
		zap.Uint("backgroundMusicId", backgroundMusicId),
		zap.String("backgroundMusicName", backgroundMusic.Name),
		zap.Float64("volume", volume))

	return mixedAudioData, nil
}

// mixAudioWithFFmpeg 使用FFmpeg进行音频混音
func (s *MinimaxVoiceCloneService) mixAudioWithFFmpeg(
	voiceData []byte, musicData []byte, musicVolume float64,
) ([]byte, error) {
	// 创建临时文件
	tempDir := "/tmp"
	voiceFile := fmt.Sprintf("%s/voice_%d.mp3", tempDir, time.Now().UnixNano())
	musicFile := fmt.Sprintf("%s/music_%d.mp3", tempDir, time.Now().UnixNano())
	outputFile := fmt.Sprintf("%s/mixed_%d.mp3", tempDir, time.Now().UnixNano())

	// 清理临时文件
	defer func() {
		os.Remove(voiceFile)
		os.Remove(musicFile)
		os.Remove(outputFile)
	}()

	// 写入临时文件
	if err := os.WriteFile(voiceFile, voiceData, 0644); err != nil {
		return nil, fmt.Errorf("写入人声文件失败: %v", err)
	}

	if err := os.WriteFile(musicFile, musicData, 0644); err != nil {
		return nil, fmt.Errorf("写入背景音乐文件失败: %v", err)
	}

	// 构建FFmpeg命令
	// 使用aloop过滤器让背景音乐循环播放，使用amix过滤器进行混音
	// aloop=loop=-1表示无限循环，size=2e+09设置足够大的循环缓冲区
	// duration=first确保输出时长以人声（第一个输入）为准
	cmd := exec.Command("ffmpeg",
		"-i", voiceFile,
		"-i", musicFile,
		"-filter_complex", fmt.Sprintf(
			"[0:a]volume=1.0[voice];[1:a]aloop=loop=-1:size=2e+09,volume=%.2f[music];[voice][music]amix=inputs=2:duration=first:dropout_transition=0",
			musicVolume,
		),
		"-acodec", "libmp3lame",
		"-y", // 覆盖输出文件
		outputFile,
	)

	// 执行FFmpeg命令
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		global.GVA_LOG.Error("FFmpeg命令执行失败",
			zap.Error(err),
			zap.String("stderr", stderr.String()))
		return nil, fmt.Errorf("FFmpeg命令执行失败: %v, stderr: %s", err, stderr.String())
	}

	// 读取混音后的文件
	mixedData, err := os.ReadFile(outputFile)
	if err != nil {
		return nil, fmt.Errorf("读取混音后文件失败: %v", err)
	}

	return mixedData, nil
}
