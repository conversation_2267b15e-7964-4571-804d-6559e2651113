package response

import "time"

// DyProductCategoryResponse 商品分类响应
type DyProductCategoryResponse struct {
	ID           uint      `json:"id"`
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
	Name         string    `json:"name"`
	SortOrder    int       `json:"sortOrder"`
	ProductCount int       `json:"productCount"`
	CreatorID    uint      `json:"creatorId"`
	CreatorName  string    `json:"creatorName"`
	Status       int       `json:"status"`
}

// DyProductCategoryListResponse 商品分类列表响应
type DyProductCategoryListResponse struct {
	List     []DyProductCategoryResponse `json:"list"`
	Total    int64                       `json:"total"`
	Page     int                         `json:"page"`
	PageSize int                         `json:"pageSize"`
}
