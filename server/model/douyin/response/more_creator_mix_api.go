package response

type MoreCreatorCustomApiMixCreateVodResponse struct {
	Code    int    `json:"code"`
	Msg     string `json:"msg"`
	Status  int    `json:"status"`
	Message string `json:"message"`
	Data    struct {
		Extra struct {
			Logid string `json:"logid"`
			Now   int64  `json:"now"`
		} `json:"extra"`
		ItemId        string `json:"item_id"`
		StatusCode    int    `json:"status_code"` // 0表示成功
		StatusMessage string `json:"status_message"`
		StatusMsg     string `json:"status_msg"`
		EncryptUid    string `json:"encrypt_uid"`
	} `json:"data"`
}
