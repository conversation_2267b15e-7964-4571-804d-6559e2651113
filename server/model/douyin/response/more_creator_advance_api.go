package response

// 获取作品列表work_list
type MoreCreatorAdvanceApiWorkListResponse struct {
	Code    int    `json:"code"`
	Msg     string `json:"msg"`
	Status  int    `json:"status"`
	Message string `json:"message"`
	Data    *struct {
		ItemId        string                           `json:"item_id"`
		StatusCode    int                              `json:"status_code"` // 0表示成功
		StatusMessage string                           `json:"status_message"`
		AwemeList     []MoreCreatorAdvanceApiAwemeInfo `json:"aweme_list"`
		MaxCursor     int64                            `json:"max_cursor"`
	}
}

type MoreCreatorAdvanceApiAwemeInfo struct {
	Cover struct {
		UrlList []string `json:"url_list"`
	} `json:"Cover"`
	Author struct {
		Nickname    string `json:"nickname"`
		UniqueId    string `json:"unique_id"`
		AvatarThumb struct {
			UrlList []string `json:"url_list"`
		} `json:"avatar_thumb"`
	} `json:"author"`
	AwemeId    string `json:"aweme_id"`
	AwemeType  int    `json:"aweme_type"`
	Desc       string `json:"desc"`
	CreateTime int64  `json:"create_time"`
	ShareUrl   string `json:"share_url"`
	Statistics struct {
		CollectCount   int `json:"collect_count"`
		ForwardCount   int `json:"forward_count"`
		LiveWatchCount int `json:"live_watch_count"`
		CommentCount   int `json:"comment_count"`
		DiggCount      int `json:"digg_count"`
		PlayCount      int `json:"play_count"`
		ShareCount     int `json:"share_count"`
	} `json:"statistics"`
	Duration     int64 `json:"duration"`
	GroupId      int64 `json:"group_id"`
	ReviewStruct struct {
		Status     int    `json:"status"` // 3-流量减少
		StatusDesc string `json:"status_desc"`
		Extra      string `json:"extra"`
		ShouldTell bool   `json:"should_tell"`
	} `json:"review_struct"`
	Status struct {
		AllowComment    bool   `json:"allow_comment"`
		AllowShare      bool   `json:"allow_share"`
		AwemeID         string `json:"aweme_id"`
		InReviewing     bool   `json:"in_reviewing"`
		IsDelete        bool   `json:"is_delete"`
		IsPrivate       bool   `json:"is_private"`
		IsProhibited    bool   `json:"is_prohibited"`
		PrivateStatus   int    `json:"private_status"`
		Reviewed        bool   `json:"reviewed"`
		SelfSee         bool   `json:"self_see"`
		WithFusionGoods bool   `json:"with_fusion_goods"`
		WithGoods       bool   `json:"with_goods"`
	} `json:"status"`
	StatusValue int `json:"status_value"`
	Video       struct {
		DownloadAddr struct {
			UrlList []string `json:"url_list"`
		} `json:"download_addr"`
	} `json:"video"`
}
