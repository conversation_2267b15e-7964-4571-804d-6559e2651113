package request

type MoreCreatorAdvanceApiWorkListRequest struct {
	<PERSON><PERSON>    string `json:"cookie"`
	Proxy     string `json:"proxy"`
	Count     int    `json:"count"`
	Status    int    `json:"status"`
	MaxCursor string `json:"max_cursor,omitempty"`
}

// 修改作品权限
type MoreCreatorAdvanceApiWorkModifyRequest struct {
	Cookie         string `json:"cookie"`
	Proxy          string `json:"proxy"`
	ItemId         string `json:"item_id"`
	VisibilityType int    `json:"visibility_type"`
	Download       int    `json:"download"`
}

// 删除作品
type MoreCreatorAdvanceApiWorkDeleteRequest struct {
	Cookie string `json:"cookie"`
	Proxy  string `json:"proxy"`
	ItemId string `json:"item_id"`
}
