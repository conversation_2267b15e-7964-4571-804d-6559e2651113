package request

type MoreCreatorMixApiUpdatePromotionRequest struct {
	Cookie       string `json:"cookie"`
	Proxy        string `json:"proxy,omitempty"`
	PromotionId  string `json:"promotion_id"`
	ElasticTitle string `json:"elastic_title"`
	ElasticImg   string `json:"elastic_img"`
	ProductId    string `json:"product_id"`
}

type CreateVod struct {
	Description      string `json:"description"`
	UploadPosterPath string `json:"upload_poster" comment:"视频封面"`
	VisibilityType   string `json:"visibility_type" comment:"可见状态(0,：全部可见，1：自己可见，2：朋友可见)	"`
	VideoPath        string `json:"video,omitempty"`
	VideoVid         string `json:"video_vid,omitempty"`
	Download         string `json:"download,omitempty"`
	Challenges       string `json:"challenges,omitempty"`
	Timing           string `json:"timing,omitempty"`
	PoiName          string `json:"poi_name,omitempty"`
	PoiId            string `json:"poi_id,omitempty"`
	ProductUrl       string `json:"product_url,omitempty"`
	ShopDraftId      string `json:"shop_draft_id"`
}

type MoreCreatorMixApiCreateVodRequest struct {
	Cookie           string `json:"cookie"`
	Proxy            string `json:"proxy,omitempty"`
	Description      string `json:"description"`
	UploadPosterPath string `json:"upload_poster" comment:"视频封面"`
	VisibilityType   string `json:"visibility_type" comment:"可见状态(0,：全部可见，1：自己可见，2：朋友可见)	"`
	VideoPath        string `json:"video,omitempty"`
	VideoVid         string `json:"video_vid,omitempty"`
	Download         string `json:"download,omitempty"`
	Challenges       string `json:"challenges,omitempty"`
	Timing           string `json:"timing,omitempty"`
	PoiName          string `json:"poi_name,omitempty"`
	PoiId            string `json:"poi_id,omitempty"`
	ProductUrl       string `json:"product_url,omitempty"`
	ShopDraftId      string `json:"shop_draft_id"`
	MusicId          string `json:"music_id,omitempty"`
}

type MoreCreatorMixApiPromotion struct {
	Title string `json:"title"`
	Link  string `json:"link"`
}

type MoreCreatorMixApiCreateNoteRequest struct {
	Cookie         string   `json:"cookie"`
	Proxy          string   `json:"proxy"`
	Description    string   `json:"description"`
	VisibilityType string   `json:"visibility_type" comment:"可见状态(0,：全部可见，1：自己可见，2：朋友可见)	"`
	UploadPoster   string   `json:"upload_poster" comment:"视频封面"`
	Images         []string `json:"images" comment:"图文文件列表"`
}

type MoreCreatorCustomApiMixCreateVodRequest struct {
	Cookie           string `json:"cookie"`
	Proxy            string `json:"proxy"`
	Description      string `json:"description"`
	UploadPosterPath string `json:"upload_poster" comment:"视频封面"`
	VisibilityType   string `json:"visibility_type" comment:"可见状态(0,：全部可见，1：自己可见，2：朋友可见)	"`
	VideoPath        string `json:"video,omitempty"`
	VideoVid         string `json:"video_vid,omitempty"`
	Download         string `json:"download,omitempty"`
	Challenges       string `json:"challenges,omitempty"`
	Timing           string `json:"timing,omitempty"`
	PoiName          string `json:"poi_name,omitempty"`
	PoiId            string `json:"poi_id,omitempty"`
	ProductUrl       string `json:"product_url,omitempty"`
	MusicId          string `json:"music_id,omitempty"`
}
