package request

import "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

// DyProductCategoryRequest 商品分类请求
type DyProductCategoryRequest struct {
	Name   string `json:"name" form:"name" binding:"required"`
	Status int    `json:"status" form:"status"`
}

// DyProductCategoryListRequest 商品分类列表请求
type DyProductCategoryListRequest struct {
	request.PageInfo
	Name   string `json:"name" form:"name"`
	Status *int   `json:"status" form:"status"`
}

// UpdateCategoryStatusRequest 更新分类状态请求
type UpdateCategoryStatusRequest struct {
	ID     uint `json:"id" form:"id" binding:"required"`
	Status int  `json:"status" form:"status" binding:"required"`
}

// UpdateCategorySortRequest 更新分类排序请求
type UpdateCategorySortRequest struct {
	Categories []CategorySortItem `json:"categories" binding:"required"`
}

// CategorySortItem 分类排序项
type CategorySortItem struct {
	ID        uint `json:"id" binding:"required"`
	SortOrder int  `json:"sortOrder" binding:"required"`
}
