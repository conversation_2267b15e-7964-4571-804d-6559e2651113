package douyin

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

// DyProductCategory 商品分类
type DyProductCategory struct {
	global.GVA_MODEL
	Name         string         `json:"name" gorm:"comment:分类名称;not null"`
	SortOrder    int            `json:"sortOrder" gorm:"comment:排序值;default:0"`
	ProductCount int            `json:"productCount" gorm:"comment:商品数量;default:0"`
	CreatorID    uint           `json:"creatorId" gorm:"comment:创建人ID"`
	Creator      system.SysUser `json:"creator" gorm:"foreignKey:CreatorID;references:ID"`
	Status       int            `json:"status" gorm:"comment:状态;default:1"` // 1-启用 2-禁用
}

// TableName 设置表名
func (DyProductCategory) TableName() string {
	return "dy_product_category"
}
