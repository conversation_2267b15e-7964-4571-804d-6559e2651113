package ai

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// AutoVideoFavoriteFlowerFont 用户收藏花字样式模型
type AutoVideoFavoriteFlowerFont struct {
	global.GVA_MODEL
	UserID      uint   `gorm:"index;comment:用户ID"`
	FlowerStyle string `gorm:"type:varchar(100);index;comment:花字样式ID"`
}

// TableName 指定表名
func (AutoVideoFavoriteFlowerFont) TableName() string {
	return "auto_video_favorite_flower_fonts"
}
