package request

// DigitalHumanTaskRequest 创建数字人视频任务请求
type DigitalHumanTaskRequest struct {
	Script             string `json:"script" binding:"required"`            // 台词内容
	Action             string `json:"action" binding:"required"`            // 人物动作
	CharacterImageUrl  string `json:"characterImageUrl" binding:"required"` // 人物图片URL
	ReferenceAudioUrl  string `json:"referenceAudioUrl" binding:"required"` // 参考音频URL
	ReferenceAudioName string `json:"referenceAudioName"`                   // 参考音频名称
	VideoSize          string `json:"videoSize"`                            // 视频尺寸，默认"832x832"
	Fps                int    `json:"fps"`                                  // 帧率，默认16
	WorkflowType       int    `json:"workflowType"`                         // 工作流类型：1=数字人工作流1(默认，不使用plus)，2=数字人工作流2(使用plus)
	CategoryId         uint   `json:"categoryId"`                           // 视频分类ID（可选）
}

// DigitalHumanTaskListRequest 数字人视频任务列表请求
type DigitalHumanTaskListRequest struct {
	Page     int `json:"page" form:"page"`         // 页码
	PageSize int `json:"pageSize" form:"pageSize"` // 每页数量
}

// HeygemDigitalHumanTaskRequest 创建HeyGem数字人视频任务请求
type HeygemDigitalHumanTaskRequest struct {
	Mode              int    `json:"mode" binding:"required"`              // 模式：1=音频+视频，2=参考音频+台词+视频
	CharacterVideoUrl string `json:"characterVideoUrl" binding:"required"` // 人物视频URL

	// 模式1专用字段：直接音频
	DirectAudioUrl  string `json:"directAudioUrl"`  // 直接音频文件URL（模式1使用）
	DirectAudioName string `json:"directAudioName"` // 直接音频文件名称（模式1使用）

	// 模式2专用字段：参考音频+台词
	ReferenceAudioUrl  string `json:"referenceAudioUrl"`  // 参考音频URL（模式2使用）
	ReferenceAudioName string `json:"referenceAudioName"` // 参考音频名称（模式2使用）
	Script             string `json:"script"`             // 台词内容（模式2使用）

	// 公共字段
	CategoryId   uint `json:"categoryId"`   // 视频分类ID（可选）
	WorkflowType int  `json:"workflowType"` // 工作流类型：固定为3（HeyGem工作流）
}
