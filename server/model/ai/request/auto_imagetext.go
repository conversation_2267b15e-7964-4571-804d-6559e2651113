package request

import "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

// 图片素材结构
type ImageMaterial struct {
	MediaId    string          `json:"mediaId" form:"mediaId"`       // 媒体ID
	MediaUrl   string          `json:"mediaUrl" form:"mediaUrl"`     // 媒体URL
	Name       string          `json:"name" form:"name"`             // 图片名称
	ExtraMedia []ImageResource `json:"extraMedia" form:"extraMedia"` // 额外的图片资源
}

// 图片资源结构
type ImageResource struct {
	ResourceId string `json:"resourceId" form:"resourceId"` // 资源ID
	MediaId    string `json:"mediaId" form:"mediaId"`       // 媒体ID
	Name       string `json:"name" form:"name"`             // 名称
	Url        string `json:"url" form:"url"`               // URL
	ThumbUrl   string `json:"thumbUrl" form:"thumbUrl"`     // 缩略图URL
	Width      int    `json:"width" form:"width"`           // 宽度
	Height     int    `json:"height" form:"height"`         // 高度
}

// 标题设置结构
type TitleSetting struct {
	Content     string    `json:"content" form:"content"`         // 标题内容
	FontFamily  string    `json:"fontFamily" form:"fontFamily"`   // 字体
	FontSize    int       `json:"fontSize" form:"fontSize"`       // 字号
	Alignment   string    `json:"alignment" form:"alignment"`     // 对齐方式 (left/center/right)
	Height      float64   `json:"height" form:"height"`           // 文字高度比例
	FontStyle   FontStyle `json:"fontStyle" form:"fontStyle"`     // 字体样式
	FlowerStyle string    `json:"flowerStyle" form:"flowerStyle"` // 花字样式
	// 为了兼容，保留旧字段
	FontColor string `json:"fontColor" form:"fontColor"` // 字体颜色（兼容字段）
	Position  string `json:"position" form:"position"`   // 位置 (top/middle/bottom)（兼容字段）
}

// 背景音乐结构
type BackgroundMusic struct {
	MediaId  string  `json:"mediaId" form:"mediaId"`   // 音乐媒体ID
	Volume   float64 `json:"volume" form:"volume"`     // 音量
	Duration int     `json:"duration" form:"duration"` // 时长
}

// 贴纸设置结构
type StickerSetting struct {
	MediaId  string  `json:"mediaId" form:"mediaId"`   // 贴纸媒体ID
	MediaUrl string  `json:"mediaUrl" form:"mediaUrl"` // 贴纸URL
	Position string  `json:"position" form:"position"` // 位置
	Scale    float64 `json:"scale" form:"scale"`       // 缩放比例
	Rotation float64 `json:"rotation" form:"rotation"` // 旋转角度
	X        float64 `json:"x" form:"x"`               // X坐标（相对比例）
	Y        float64 `json:"y" form:"y"`               // Y坐标（相对比例）
	Opacity  float64 `json:"opacity" form:"opacity"`   // 透明度
	// 前端计算的尺寸比例（相对于预览图片的比例）
	WidthRatio  float64 `json:"widthRatio" form:"widthRatio"`   // 宽度比例（贴纸宽度/图片宽度）
	HeightRatio float64 `json:"heightRatio" form:"heightRatio"` // 高度比例（贴纸高度/图片高度）
}

// 伪原创设置结构
type PseudoOriginalSettings struct {
	BrightnessMin float64 `json:"brightnessMin" form:"brightnessMin"` // 亮度调整最小值
	BrightnessMax float64 `json:"brightnessMax" form:"brightnessMax"` // 亮度调整最大值
	ContrastMin   float64 `json:"contrastMin" form:"contrastMin"`     // 对比度调整最小值
	ContrastMax   float64 `json:"contrastMax" form:"contrastMax"`     // 对比度调整最大值
	SaturationMin float64 `json:"saturationMin" form:"saturationMin"` // 饱和度调整最小值
	SaturationMax float64 `json:"saturationMax" form:"saturationMax"` // 饱和度调整最大值
	RotationMin   float64 `json:"rotationMin" form:"rotationMin"`     // 旋转角度最小值（度）
	RotationMax   float64 `json:"rotationMax" form:"rotationMax"`     // 旋转角度最大值（度）
	SizeMin       float64 `json:"sizeMin" form:"sizeMin"`             // 尺寸调整最小值
	SizeMax       float64 `json:"sizeMax" form:"sizeMax"`             // 尺寸调整最大值
}

// 批量图文生成任务参数
type AutoImageTextTaskParams struct {
	TaskName               string                  `json:"taskName" form:"taskName"`                             // 任务名称
	ImageMaterials         []ImageMaterial         `json:"imageMaterials" form:"imageMaterials"`                 // 图片素材
	TitleSettings          []TitleSetting          `json:"titleSettings" form:"titleSettings"`                   // 标题设置
	BackgroundMusics       []BackgroundMusic       `json:"backgroundMusics" form:"backgroundMusics"`             // 背景音乐
	StickerSettings        []StickerSetting        `json:"stickerSettings" form:"stickerSettings"`               // 贴纸设置
	PseudoOriginalSettings *PseudoOriginalSettings `json:"pseudoOriginalSettings" form:"pseudoOriginalSettings"` // 伪原创设置

	// 其他设置
	GenerateCount        int    `json:"generateCount" form:"generateCount"`               // 生成数量
	MinImageCount        int    `json:"minImageCount" form:"minImageCount"`               // 最小图片数量
	MaxImageCount        int    `json:"maxImageCount" form:"maxImageCount"`               // 最大图片数量
	TitleApplyMode       int    `json:"titleApplyMode" form:"titleApplyMode"`             // 标题应用模式：1-仅第一张 2-每张都加
	StickerApplyMode     int    `json:"stickerApplyMode" form:"stickerApplyMode"`         // 贴纸应用模式：1-仅第一张 2-每张都加
	CategoryIds          []uint `json:"categoryIds" form:"categoryIds"`                   // 分类ID列表
	VideoTitle           string `json:"videoTitle" form:"videoTitle"`                     // 视频标题
	ManualProductId      uint   `json:"manualProductId" form:"manualProductId"`           // 手动录入商品ID
	Topic                string `json:"topic" form:"topic"`                               // 话题
	ImageTextDescription string `json:"imageTextDescription" form:"imageTextDescription"` // 图文描述
}

// 图文任务列表查询参数
type AutoImageTextTaskListParams struct {
	request.PageInfo
	TaskName  string `json:"taskName" form:"taskName"`   // 任务名称
	Status    *int   `json:"status" form:"status"`       // 状态
	CreatedBy *uint  `json:"createdBy" form:"createdBy"` // 创建者
}
