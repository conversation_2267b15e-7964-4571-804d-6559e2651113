package ai

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// 批量图文生成任务
type AutoImageTextTask struct {
	global.GVA_MODEL
	TaskName             string `json:"taskName" gorm:"comment:任务名称"`
	Status               int    `json:"status" gorm:"comment:任务状态,0:未处理,1:成功,2:失败,3:处理中"`
	ErrorMsg             string `json:"errorMsg" gorm:"comment:错误信息"`
	CreatedBy            uint   `json:"createdBy" gorm:"comment:创建者ID"`
	CreatedName          string `json:"createdName" gorm:"comment:创建者名称"`
	GenerateCount        int    `json:"generateCount" gorm:"comment:生成数量"`
	SuccessCount         int    `json:"successCount" gorm:"comment:成功生成数量"`
	Categories           string `json:"categories" gorm:"comment:视频分类IDs,JSON字符串"`
	VideoTitle           string `json:"videoTitle" gorm:"comment:视频标题"`
	ManualProductId      uint   `json:"manualProductId" gorm:"comment:手动录入商品ID"`
	Topic                string `json:"topic" gorm:"comment:话题"`
	ImageTextDescription string `json:"imageTextDescription" gorm:"type:text;comment:图文描述"`

	// 配置数据（JSON存储）
	ImageMaterials   string `json:"imageMaterials" gorm:"type:text;comment:图片素材配置"`
	TitleSettings    string `json:"titleSettings" gorm:"type:text;comment:标题设置配置"`
	BackgroundMusics string `json:"backgroundMusics" gorm:"type:text;comment:背景音乐配置"`
	StickerSettings  string `json:"stickerSettings" gorm:"type:text;comment:贴纸设置配置"`

	// 其他配置
	MinImageCount    int `json:"minImageCount" gorm:"comment:最小图片数量"`
	MaxImageCount    int `json:"maxImageCount" gorm:"comment:最大图片数量"`
	TitleApplyMode   int `json:"titleApplyMode" gorm:"comment:标题应用模式：1-仅第一张 2-每张都加"`
	StickerApplyMode int `json:"stickerApplyMode" gorm:"comment:贴纸应用模式：1-仅第一张 2-每张都加"`
}

// 批量图文生成任务状态
type AutoImageTextTaskStatus struct {
	Status       int     `json:"status"`       // 状态: 0-未处理, 1-成功, 2-失败, 3-处理中
	Progress     float64 `json:"progress"`     // 处理进度 0-100
	ErrorMsg     string  `json:"errorMsg"`     // 错误信息
	SuccessCount int     `json:"successCount"` // 成功生成数量
	TotalCount   int     `json:"totalCount"`   // 总生成数量
}

func (AutoImageTextTask) TableName() string {
	return "auto_imagetext_task"
}
