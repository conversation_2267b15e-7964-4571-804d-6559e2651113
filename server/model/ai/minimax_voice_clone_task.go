package ai

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// MinimaxVoiceCloneTask MiniMax人声克隆任务
type MinimaxVoiceCloneTask struct {
	global.GVA_MODEL
	TaskId             string     `json:"taskId" gorm:"column:task_id;comment:任务ID"`                                // 任务ID
	UserId             uint       `json:"userId" gorm:"column:user_id;comment:用户ID"`                                // 用户ID
	Text               string     `json:"text" gorm:"column:text;type:text;comment:要转换的文本"`                         // 要转换的文本
	ReferenceAudioName string     `json:"referenceAudioName" gorm:"column:reference_audio_name;comment:参考音频名称(声源)"` // 参考音频名称(声源)
	MinimaxVoiceId     string     `json:"minimaxVoiceId" gorm:"column:minimax_voice_id;comment:MiniMax音色ID"`        // MiniMax音色ID
	Model              string     `json:"model" gorm:"column:model;comment:模型"`                                     // 模型
	Speed              float64    `json:"speed" gorm:"column:speed;comment:语速"`                                     // 语速
	Volume             float64    `json:"volume" gorm:"column:volume;comment:音量"`                                   // 音量
	Pitch              float64    `json:"pitch" gorm:"column:pitch;comment:音调"`                                     // 音调
	AudioSampleRate    int        `json:"audioSampleRate" gorm:"column:audio_sample_rate;comment:音频采样率"`            // 音频采样率
	BitRate            int        `json:"bitRate" gorm:"column:bit_rate;comment:比特率"`                               // 比特率
	Format             string     `json:"format" gorm:"column:format;comment:音频格式"`                                 // 音频格式
	TargetCategoryId   uint       `json:"targetCategoryId" gorm:"column:target_category_id;comment:目标分类ID"`         // 目标分类ID
	Status             string     `json:"status" gorm:"column:status;comment:任务状态"`                                 // 任务状态
	AudioUrl           string     `json:"audioUrl" gorm:"column:audio_url;comment:生成的音频URL"`                        // 生成的音频URL
	ErrorMsg           string     `json:"errorMsg" gorm:"column:error_msg;comment:错误信息"`                            // 错误信息
	StartTime          time.Time  `json:"startTime" gorm:"column:start_time;comment:开始时间"`                          // 开始时间
	EndTime            *time.Time `json:"endTime" gorm:"column:end_time;comment:结束时间"`                              // 结束时间
	// 音乐库相关信息（audio_url会在保存到音乐库后更新为音乐库URL）
	Duration int64  `json:"duration" gorm:"column:duration;comment:音频时长(秒)"` // 音频时长(秒)
	MediaId  string `json:"mediaId" gorm:"column:media_id;comment:媒资ID"`     // 媒资ID
	// 背景音乐相关字段
	BackgroundMusicId     uint    `json:"backgroundMusicId" gorm:"column:background_music_id;comment:背景音乐ID"`           // 背景音乐ID
	BackgroundMusicVolume float64 `json:"backgroundMusicVolume" gorm:"column:background_music_volume;comment:背景音乐音量"` // 背景音乐音量
}

// TableName 指定表名
func (v *MinimaxVoiceCloneTask) TableName() string {
	return "ai_minimax_voice_clone_tasks"
}
