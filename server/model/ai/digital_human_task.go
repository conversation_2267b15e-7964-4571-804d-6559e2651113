package ai

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// DigitalHumanTask 数字人视频任务
type DigitalHumanTask struct {
	global.GVA_MODEL
	TaskId             string     `json:"taskId" gorm:"column:task_id;comment:任务ID"`                                  // 任务ID
	UserId             uint       `json:"userId" gorm:"column:user_id;comment:用户ID"`                                  // 用户ID
	Script             string     `json:"script" gorm:"column:script;type:text;comment:台词内容"`                         // 台词内容
	Action             string     `json:"action" gorm:"column:action;comment:人物动作"`                                   // 人物动作
	CharacterImageUrl  string     `json:"characterImageUrl" gorm:"column:character_image_url;comment:人物图片URL"`        // 人物图片URL（兼容性保留）
	CharacterVideoUrl  string     `json:"characterVideoUrl" gorm:"column:character_video_url;comment:人物视频URL"`        // 人物视频URL
	ReferenceAudioUrl  string     `json:"referenceAudioUrl" gorm:"column:reference_audio_url;comment:参考音频URL"`        // 参考音频URL
	ReferenceAudioName string     `json:"referenceAudioName" gorm:"column:reference_audio_name;comment:参考音频名称"`       // 参考音频名称
	VideoSize          string     `json:"videoSize" gorm:"column:video_size;comment:视频尺寸"`                            // 视频尺寸
	Fps                int        `json:"fps" gorm:"column:fps;comment:帧率"`                                           // 帧率
	CategoryId         uint       `json:"categoryId" gorm:"column:category_id;comment:视频分类ID"`                        // 视频分类ID
	HeygemMode         int        `json:"heygemMode" gorm:"column:heygem_mode;comment:HeyGem模式:1=音频+视频,2=参考音频+台词+视频"` // HeyGem模式
	DirectAudioUrl     string     `json:"directAudioUrl" gorm:"column:direct_audio_url;comment:直接音频URL"`              // 直接音频URL
	DirectAudioName    string     `json:"directAudioName" gorm:"column:direct_audio_name;comment:直接音频名称"`             // 直接音频名称
	Status             string     `json:"status" gorm:"column:status;comment:任务状态"`                                   // 任务状态
	VideoUrl           string     `json:"videoUrl" gorm:"column:video_url;comment:生成的视频URL"`                          // 生成的视频URL
	ErrorMsg           string     `json:"errorMsg" gorm:"column:error_msg;comment:错误信息"`                              // 错误信息
	StartTime          time.Time  `json:"startTime" gorm:"column:start_time;comment:开始时间"`                            // 开始时间
	EndTime            *time.Time `json:"endTime" gorm:"column:end_time;comment:结束时间"`                                // 结束时间
	Duration           int64      `json:"duration" gorm:"column:duration;comment:视频时长(秒)"`                            // 视频时长(秒)
	MediaId            string     `json:"mediaId" gorm:"column:media_id;comment:媒资ID"`                                // 媒资ID
}

// TableName 指定表名
func (d *DigitalHumanTask) TableName() string {
	return "ai_digital_human_tasks"
}
