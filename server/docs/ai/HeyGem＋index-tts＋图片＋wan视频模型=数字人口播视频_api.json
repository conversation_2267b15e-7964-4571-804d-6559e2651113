{"11": {"inputs": {"model_name": "umt5_xxl_fp16.safetensors", "precision": "bf16", "load_device": "offload_device", "quantization": "disabled"}, "class_type": "LoadWanVideoT5TextEncoder", "_meta": {"title": "WanVideo T5 Text Encoder Loader"}}, "16": {"inputs": {"positive_prompt": ["90", 0], "negative_prompt": "bright tones, overexposed, static, blurred details, subtitles, style, works, paintings, images, static, overall gray, worst quality, low quality, JPEG compression residue, ugly, incomplete, extra fingers, poorly drawn hands, poorly drawn faces, deformed, disfigured, misshapen limbs, fused fingers, still picture, messy background, three legs, many people in the background, walking backwards", "force_offload": true, "use_disk_cache": false, "device": "gpu", "t5": ["11", 0], "model_to_offload": ["22", 0]}, "class_type": "WanVideoTextEncode", "_meta": {"title": "WanVideo TextEncode"}}, "22": {"inputs": {"model": "wan2114BFusionx_fusionxImage2video.safetensors", "base_precision": "bf16", "quantization": "disabled", "load_device": "offload_device", "attention_mode": "sdpa", "lora": ["71", 0]}, "class_type": "WanVideoModelLoader", "_meta": {"title": "WanVideo Model Loader"}}, "27": {"inputs": {"steps": 4, "cfg": 1, "shift": 5.000000000000001, "seed": 1057359483639287, "force_offload": true, "scheduler": "lcm", "riflex_freq_index": 0, "denoise_strength": 1, "batched_cfg": "", "rope_function": "comfy", "start_step": 0, "end_step": -1, "add_noise_to_samples": false, "model": ["22", 0], "text_embeds": ["86", 0], "image_embeds": ["63", 0], "feta_args": ["55", 0], "cache_args": ["52", 0]}, "class_type": "WanVideoSampler", "_meta": {"title": "WanVide<PERSON>"}}, "28": {"inputs": {"enable_vae_tiling": false, "tile_x": 272, "tile_y": 272, "tile_stride_x": 144, "tile_stride_y": 128, "normalization": "default", "vae": ["38", 0], "samples": ["87", 0]}, "class_type": "WanVideoDecode", "_meta": {"title": "WanVideo Decode"}}, "38": {"inputs": {"model_name": "Wan2_1_VAE_bf16.safetensors", "precision": "bf16"}, "class_type": "WanVideoVAELoader", "_meta": {"title": "WanVideo VAE Loader"}}, "52": {"inputs": {"rel_l1_thresh": 0.25, "start_step": 1, "end_step": -1, "cache_device": "main_device", "use_coefficients": "true", "mode": "e"}, "class_type": "WanVideoTeaCache", "_meta": {"title": "WaWanVideo TeaCache"}}, "55": {"inputs": {"weight": 2, "start_percent": 0, "end_percent": 1}, "class_type": "WanVideoEnhanceAVideo", "_meta": {"title": "WanVideo Enhance-A-Video"}}, "58": {"inputs": {"image": "a9bd0417a4963ac0148f14598aa58ecdd8f0c79ecff68ef1beaea5771c39ed42.png"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "59": {"inputs": {"clip_name": "clip_vision_h.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "63": {"inputs": {"width": ["66", 1], "height": ["66", 2], "num_frames": 81, "noise_aug_strength": 0.030000000000000006, "start_latent_strength": 1, "end_latent_strength": 1, "force_offload": true, "fun_or_fl2v_model": false, "tiled_vae": false, "vae": ["38", 0], "clip_embeds": ["65", 0], "start_image": ["66", 0]}, "class_type": "WanVideoImageToVideoEncode", "_meta": {"title": "WanVideo ImageToVideo Encode"}}, "65": {"inputs": {"strength_1": 1, "strength_2": 1, "crop": "center", "combine_embeds": "average", "force_offload": true, "tiles": 0, "ratio": 0.20000000000000004, "clip_vision": ["59", 0], "image_1": ["66", 0]}, "class_type": "WanVideoClipVisionEncode", "_meta": {"title": "WanVideo ClipVision Encode"}}, "66": {"inputs": {"width": ["70", 0], "height": ["70", 0], "upscale_method": "lanc<PERSON>s", "keep_proportion": true, "divisible_by": 16, "crop": 0, "image": ["58", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image (deprecated)"}}, "68": {"inputs": {"images": ["66", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "70": {"inputs": {"value": 720}, "class_type": "INTConstant", "_meta": {"title": "输出分辨率设置"}}, "71": {"inputs": {"lora": "Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors", "strength": 1.2000000000000002, "low_mem_load": false, "merge_loras": true}, "class_type": "WanVideoLoraSelect", "_meta": {"title": "WanVideo Lora Select"}}, "72": {"inputs": {"text": "熏风初定月横斜，小院清幽落藕花。\n竹榻微凉消溽暑，蒲扇轻摇送流霞。\n蝉鸣渐歇栖高树，萤火时明透浅纱。\n忽有荷香随露至，一杯清茗话桑麻。"}, "class_type": "TextInput_", "_meta": {"title": "修改文本：用于数字人说话的内容"}}, "74": {"inputs": {"filename_prefix": "pl-index-tts", "audioUI": "", "audio": ["83", 0]}, "class_type": "SaveAudio", "_meta": {"title": "Save Audio (FLAC)"}}, "75": {"inputs": {"start_time": "0:0", "end_time": "0:15", "audio": ["77", 0]}, "class_type": "AudioCrop", "_meta": {"title": "AudioCrop"}}, "77": {"inputs": {"audio": "1f74e265aa9d931bb0c9c7ca3ac4efb70507a60e5b37be4f89895824e105a477.mp3", "audioUI": ""}, "class_type": "LoadAudio", "_meta": {"title": "Load Audio"}}, "78": {"inputs": {"AUDIO": ["83", 0], "VIDEO": ["79", 0]}, "class_type": "RH_HeyGemNode", "_meta": {"title": "HeyGem Video Synthesis"}}, "79": {"inputs": {"fps": ["81", 0], "images": ["28", 0], "audio": ["83", 0]}, "class_type": "CreateVideo", "_meta": {"title": "Create Video"}}, "81": {"inputs": {"value": 16}, "class_type": "PrimitiveFloat", "_meta": {"title": "Float"}}, "82": {"inputs": {"filename_prefix": "video/ComfyUI", "format": "auto", "codec": "auto", "video": ["78", 0]}, "class_type": "SaveVideo", "_meta": {"title": "输出数字人视频结果"}}, "83": {"inputs": {"version": "v1.5", "top_k": 30, "top_p": 0.8, "temperature": 1, "num_beams": 3, "max_mel_tokens": 1000, "max_text_tokens_per_sentence": 120, "sentences_bucket_max_size": 4, "fast_inference": false, "custom_cuda_kernel": false, "unload_model": true, "audio": ["75", 0], "text": ["72", 0]}, "class_type": "IndexTTSRun", "_meta": {"title": "IndexTTS Run"}}, "86": {"inputs": {"anything": ["16", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "87": {"inputs": {"anything": ["27", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "88": {"inputs": {"prompt": "请对这张图片进行详细描述。要清晰的描述内容，让画面清晰，自然。", "ref_image": ["66", 0]}, "class_type": "RH_Captioner", "_meta": {"title": "<PERSON><PERSON><PERSON>ioner"}}, "89": {"inputs": {"text": "人物面对镜头，自然的讲话交流，高清4k，人物动作表现非常自然。\n\n"}, "class_type": "TextInput_", "_meta": {"title": "Text Input ♾️Mixlab"}}, "90": {"inputs": {"delimiter": "none", "text1": ["89", 0], "text2": ["88", 0]}, "class_type": "Text Concatenate (JPS)", "_meta": {"title": "Text Concatenate (JPS)"}}}