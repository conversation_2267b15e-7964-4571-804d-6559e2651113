# LiblibAI x 星流 图像大模型 API 使用说明

Last updated: Aug 19

# LiblibAI x 星流 图像大模型 API 使用说明

Modified Today

⛱️
LiblibAI-API 产品主页和购买下单：[https://www.liblib.art/apis](https://www.liblib.art/apis)

## 产品简介

欢迎使用 LiblibAI x 星流 图像大模型 API 来进行创作！无论你是进行个人项目还是为其他终端用户提供的企业服务，我们的 API 都能满足你的需求。

全新 AI 图像模型和工作流 API，提供极致的图像质量，在输出速度、生图成本和图像卓越性之间实现平衡。

您有任何问题，可随时电话联系商务：17521599324。

📌
我们提供了工作流 API 和 5 款生图模型 API：

- **LiblibAI 工作流**：社区商用工作流和个人本地工作流均可支持调用。工作流挑选和商用查询可至[https://www.liblib.art/workflows](https://www.liblib.art/workflows)

- **F.1 Kontext**：将文本生成图像与高级图像编辑能力相结合，在真实感、风格一致性和复杂场景还原上均处于行业领先地位。

- **智能算法 IMG 1**：以超强风格一致性、Prompt 还原能力为优势。

- **LibDream**：对中文指令理解良好，出中文、海报能力最强。

- **星流 Star-3 Alpha**：搭载自带 LoRA 推荐算法，对自然语言的精准响应，能够生成具有照片级真实感的视觉效果，不能自由添加 LoRA，仅支持部分 ControlNet。

- **LiblibAI 自定义模型**：若需要特定 LoRA 和 ControlNet 只能选此模式，适合高度自由、精准控制和特定风格的场景，基于 F.1/XL/v3/v1.5 等基础算法，支持自定义调用 LiblibAI 内全量 50 万+可商用模型和任意私有模型。

API 试用计划：[https://www.liblib.art/apis](https://www.liblib.art/apis)登录后可领取 500 试用积分，限时 7 天免费测试体验。

## 文档版本更新

| 日期       | 说明                                                                                                                                                                                                                                                                       |
| ---------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 2025.8.19  | [10 增加可灵生成视频接口](https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#V6Y9dfe0yoJOJ1xT2q1c0rq2nwc)                                                                                                                                                         |
| 2025.8.19  | [9 增加 libDream&libEdit](https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#SfA8d3waPogeHNxmiw9cVa1un7d)                                                                                                                                                         |
| 2025.6.16  | [5 增加 F.1 Kontext](https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#share-CsW4dgKyXoq9RCxIWpVc2u0Nnux)                                                                                                                                                        |
| 2025.6.16  | [6 增加智能算法 IMG-1](https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#share-SAOIdcQ0HolLvfx3TrJcJvqmnFh)                                                                                                                                                      |
| 2025.4.30  | 支持图片上传： [LiblibAI-API 文件上传](https://liblibai.feishu.cn/wiki/A9M2whHxsiKtu8kpIn3cZp0PnVw)                                                                                                                                                                        |
| 2025.3.24  | [11 增加 js 的 SDK](https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#share-GlGUdjiaXowiNNxrv6zcvn54nGf)                                                                                                                                                         |
| 2025.3.18  | 增加 F.1-ControlNet（PuLID 人像换脸、主体参考）                                                                                                                                                                                                                            |
| 2025.2.5   | [11 增加 java 的 SDK](https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#share-GlGUdjiaXowiNNxrv6zcvn54nGf)                                                                                                                                                       |
| 2025.1.17  | [8 增加调用 ComfyUI 工作流](https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#share-Ip5Td5eL4oEkEuxt0Dvcw8gDnrg)                                                                                                                                                 |
| 2025.1.2   | [3.4 增加 Comfyui 接入星流 API](https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#share-WzHWdTKeaoCKmDxt1CicGe3Vnze)                                                                                                                                             |
| 2024.12.18 | 查询生图结果的返回字段，新增 pointsCost（当次任务消耗积分）和 accountBalance（账户剩余积分数）                                                                                                                                                                             |
| 2024.12.5  | 原【进阶模式】更名为【LiblibAI 自定义模型】原【简易模式-经典模型 XL】不再维护，不再支持新接入开放 LiblibAI 全网可商用模型和私有模型调用，[查询和调用模型接口详见文档 4.1.1](https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#share-BAqodDFOLo8BlexzdsGcFvefn6g) |
| 2024.11.15 | 支持 F.1 风格迁移：参考《[F.1 风格迁移参数示例](https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#share-FTAcdzv1eoTvXNxIRhmcDo4ynBg)》                                                                                                                           |

## 1. 能力地图

- [API KEY 的使用](https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#share-R4J6d3oQUoXxjRxTGrhcNm4Ineo)
- [星流 Star-3 生图](https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#share-R8XcdGy3UoggcjxH8x5cZJB2n9c)
- [自定义模型生图](https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#share-VDZjdAgYEoJzR2xIvQScSQCUnIc)
- [F.1 Kontext](https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#share-LhqzdkGIHoD8BHxicWdcLRYsnug)
- [智能算法 IMG 1](https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#share-I8qSdJTwDoa439xSTX0cgJ3Engg)
- [LibDream](https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#share-UzxQdcxUCoKtxSxjysFcKuwyndh)
- [图片上传，获取 oss 地址](https://liblibai.feishu.cn/wiki/A9M2whHxsiKtu8kpIn3cZp0PnVw)

## 2. 开始使用

在这一部分，我们将展示如何开通 API 的权益，以及如何创建你的 API 密钥。

### 2.1 访问地址

Liblib 开放平台域名：[https://openapi.liblibai.cloud](https://openapi.liblibai.cloud/)（无法直接打开，需配合密钥访问）

### 2.2 计费规则

非固定消耗，每次生图任务消耗的积分与以下参数有关：

- 选用模型
- 采样步数（steps）
- 采样方法（sampler，SDE 系列会产生额外消耗）
- 生成图片宽度
- 生成图片高度
- 生成图片张数
- 重绘幅度（denoisingStrength）
- 高分辨率修复的重绘步数和重绘幅度
- Controlnet 数量

### 2.3 并发数和 QPS

- 生图任务并发数，默认 5（因生图需要时间，指同时可进行的生图任务数）
- 发起生图任务接口，QPS 默认 1 秒 1 次，（可用每天预计生图张数/24h/60m/60s 来估算平均值）
- 查询生图结果接口，QPS 无限制

### 2.4 生成 API 密钥

在登录 Liblib 领取 API 试用积分或购买 API 积分后，Liblib 会生成开放平台访问密钥，用于后续 API 接口访问，密钥包括：

- AccessKey，API 访问凭证，唯一识别访问用户，长度通常在 20-30 位左右，如：KIQMFXjHaobx7wqo9XvYKA
- SecretKey，API 访问密钥，用于加密请求参数，避免请求参数被篡改，长度通常在 30 位以上，如：KppKsn7ezZxhi6lIDjbo7YyVYzanSu2d

#### 2.4.1 使用密钥

申请 API 密钥之后，需要在每次请求 API 接口的查询字符串中固定传递以下参数：

| 参数           | 类型   | 是否必需 | 说明                                              |
| -------------- | ------ | -------- | ------------------------------------------------- |
| AccessKey      | String | 是       | 开通开放平台授权的访问 AccessKey                  |
| Signature      | String | 是       | 加密请求参数生成的签名，签名公式见下节"生成签名"  |
| Timestamp      | String | 是       | 生成签名时的毫秒时间戳，整数字符串，有效期 5 分钟 |
| SignatureNonce | String | 是       | 生成签名时的随机字符串                            |

如请求地址：https://test.xxx.com/api/genImg?AccessKey=KIQMFXjHaobx7wqo9XvYKA&Signature=test1232132&Timestamp=1725458584000&SignatureNonce=random1232

#### 2.4.2 生成签名

签名生成公式如下：

```bash
# 1. 用"&"拼接参数
# URL地址：以上方请求地址为例，为"/api/genImg"
# 毫秒时间戳：即上节"使用密钥"中要传递的"Timestamp"
# 随机字符串：即上节"使用密钥"中要传递的"SignatureNonce"
原文 = URL地址 + "&" + 毫秒时间戳 + "&" + 随机字符串

# 2. 用SecretKey加密原文，使用hmacsha1算法
密文 = hmacSha1(原文, SecretKey)

# 3. 生成url安全的base64签名
# 注：base64编码时不要补全位数
签名 = encodeBase64URLSafeString(密文)
```

**Java 生成签名示例**，以访问上方"使用密钥"的请求地址为例：

```java
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.RandomStringUtils;

public class SignUtil {

/**
 * 生成请求签名
 * 其中相关变量均为示例，请替换为您的实际数据
 */
public static String makeSign() {

    // API访问密钥
    String secretKey = "KppKsn7ezZxhi6lIDjbo7YyVYzanSu2d";

    // 请求API接口的uri地址
    String uri = "/api/generate/webui/text2img";
    // 当前毫秒时间戳
    Long timestamp = System.currentTimeMillis();
    // 随机字符串
    String signatureNonce = RandomStringUtils.randomAlphanumeric(10);
    // 拼接请求数据
    String content = uri + "&" + timestamp + "&" + signatureNonce;

    try {
        // 生成签名
        SecretKeySpec secret = new SecretKeySpec(secretKey.getBytes(), "HmacSHA1");
        Mac mac = Mac.getInstance("HmacSHA1");
        mac.init(secret);
        return Base64.encodeBase64URLSafeString(mac.doFinal(content.getBytes()));
    } catch (NoSuchAlgorithmException e) {
        throw new RuntimeException("no such algorithm");
    } catch (InvalidKeyException e) {
        throw new RuntimeException(e);
    }
}
}
```

**Python 生成签名示例**，以访问上方"使用密钥"的请求地址为例：

```python
import hmac
from hashlib import sha1
import base64
import time
import uuid

def make_sign():
    """
    生成签名
    """

    # API访问密钥
    secret_key = 'KppKsn7ezZxhi6lIDjbo7YyVYzanSu2d'

    # 请求API接口的uri地址
    uri = "/api/genImg"
    # 当前毫秒时间戳
    timestamp = str(int(time.time() * 1000))
    # 随机字符串
    signature_nonce= str(uuid.uuid4())
    # 拼接请求数据
    content = '&'.join((uri, timestamp, signature_nonce))

    # 生成签名
    digest = hmac.new(secret_key.encode(), content.encode(), sha1).digest()
    # 移除为了补全base64位数而填充的尾部等号
    sign = base64.urlsafe_b64encode(digest).rstrip(b'=').decode()
    return sign
```

**NodeJs 生成签名示例**，以访问上方"使用密钥"的请求地址为例：

```javascript
const hmacsha1 = require("hmacsha1")
const randomString = require("string-random")
// 生成签名
const urlSignature = (url) => {
  if (!url) return
  const timestamp = Date.now() // 当前时间戳
  const signatureNonce = randomString(16) // 随机字符串，你可以任意设置，这个没有要求
  // 原文 = URl地址 + "&" + 毫秒时间戳 + "&" + 随机字符串
  const str = `${url}&${timestamp}&${signatureNonce}`
  const secretKey = "官网上的 SecretKey " // 下单后在官网中，找到自己的 SecretKey'
  const hash = hmacsha1(secretKey, str)
  // 最后一步： encodeBase64URLSafeString(密文)
  // 这一步很重要，生成安全字符串。java、Python 以外的语言，可以参考这个 JS 的处理
  let signature = hash
    .replace(/\+/g, "-")
    .replace(/\//g, "_")
    .replace(/=+$/, "")
  return {
    signature,
    timestamp,
    signatureNonce,
  }
}
// 例子：原本查询生图进度接口是 https://openapi.liblibai.cloud/api/generate/webui/status
// 加密后，url 就变更为 https://openapi.liblibai.cloud/api/generate/webui/status?AccessKey={YOUR_ACCESS_KEY}&Signature={签名}&Timestamp={时间戳}&SignatureNonce={随机字符串}
const getUrl = () => {
  const url = "/api/generate/webui/status"
  const { signature, timestamp, signatureNonce } = urlSignature(url)
  const accessKey = "替换自己的 AccessKey" // '下单后在官网中，找到自己的 AccessKey'
  return `${url}?AccessKey=${accessKey}&Signature=${signature}&Timestamp=${timestamp}&SignatureNonce=${signatureNonce}`
}
```

## 3. 星流 Star-3 Alpha

### 3.1 星流 Star-3 Alpha 生图

#### 3.1.1 星流 Star-3 Alpha 文生图

- **接口**：POST /api/generate/webui/text2img/ultra

- **headers**：

| header       | value            | 备注 |
| ------------ | ---------------- | ---- |
| Content-Type | application/json |      |

- **请求 body**：

| 参数           | 类型   | 是否必需 | 说明                                                       | 备注                                       |
| -------------- | ------ | -------- | ---------------------------------------------------------- | ------------------------------------------ |
| templateUuid   | string | 是       | 星流 Star-3 Alpha 文生图：5d7e67009b344550bc1aa6ccbfa1d7f4 |                                            |
| generateParams | object | 是       | 生图参数，json 结构                                        | 参数中的图片字段需提供可访问的完整图片地址 |

- **返回值**：

| 参数         | 类型   | 备注                                    |
| ------------ | ------ | --------------------------------------- |
| generateUuid | string | 生图任务 uuid，使用该 uuid 查询生图进度 |

- **参数说明**

| 变量名      | 格式   | 备注                                        | 数值范围                                                                       | 必填       | 示例       |
| ----------- | ------ | ------------------------------------------- | ------------------------------------------------------------------------------ | ---------- | ---------- |
| prompt      | string | 正向提示词，文本                            | 不超过 2000 字符，纯英文文本                                                   | 是         | 见下方示例 |
| aspectRatio | string | 图片宽高比预设，与 imageSize 二选一配置即可 | square：1:1 (1024*1024)，portrait：3:4 (768*1024)，landscape：16:9 (1280\*720) | 二选一配置 |            |
| imageSize   | Object | 图片具体宽高，与 aspectRatio 二选一配置即可 | width：int (512~2048)，height：int (512~2048)                                  |            |            |
| imgCount    | int    | 单次生图张数                                | 1 ~ 4                                                                          | 是         |            |
| controlnet  | Object | 构图控制                                    | controlType：line/depth/pose/IPAdapter，controlImage：参考图 URL               | 否         |            |

```json
{
  "templateUuid": "5d7e67009b344550bc1aa6ccbfa1d7f4",
  "generateParams": {
    "prompt": "1 girl,lotus leaf,masterpiece,best quality,finely detail,highres,8k,beautiful and aesthetic,no watermark,",
    "aspectRatio": "portrait",
    //或者配置imageSize设置具体宽高
    "imageSize": {
      "width": 768,
      "height": 1024
    },
    "imgCount": 1,
    "steps": 30, // 采样步数，建议30

    //高级设置，可不填写
    "controlnet": {
      "controlType": "depth",
      "controlImage": "https://liblibai-online.liblib.cloud/img/081e9f07d9bd4c2ba090efde163518f9/7c1cc38e-522c-43fe-aca9-07d5420d743e.png"
    }
  }
}
```

#### 3.1.2 星流 Star-3 Alpha 图生图

- **接口**：POST /api/generate/webui/img2img/ultra

- **headers**：

| header       | value            | 备注 |
| ------------ | ---------------- | ---- |
| Content-Type | application/json |      |

- **请求 body**：

| 参数           | 类型   | 是否必需 | 说明                                                       | 备注                                       |
| -------------- | ------ | -------- | ---------------------------------------------------------- | ------------------------------------------ |
| templateUUID   | string | 是       | 星流 Star-3 Alpha 图生图：07e00af4fc464c7ab55ff906f8acf1b7 |                                            |
| generateParams | object | 是       | 生图参数，json 结构                                        | 参数中的图片字段需提供可访问的完整图片地址 |

- **返回值**：

| 参数         | 类型   | 备注                                    |
| ------------ | ------ | --------------------------------------- |
| generateUuid | string | 生图任务 uuid，使用该 uuid 查询生图进度 |

- **参数说明**

| 变量名      | 格式   | 备注             | 数值范围                                                         | 必填 |
| ----------- | ------ | ---------------- | ---------------------------------------------------------------- | ---- |
| prompt      | string | 正向提示词，文本 | 不超过 2000 字符，纯英文文本                                     | 是   |
| sourceImage | string | 参考图 URL       | 参考图可公网访问的完整 URL                                       | 是   |
| imgCount    | int    | 单次生图张数     | 1 ~ 4                                                            | 是   |
| controlnet  | Object | 构图控制         | controlType：line/depth/pose/IPAdapter，controlImage：参考图 URL | 否   |

### 3.2 查询生图结果

- **接口**：POST /api/generate/webui/status

- **headers**：

| header       | value            | 备注 |
| ------------ | ---------------- | ---- |
| Content-Type | application/json |      |

- **请求 body**：

| 参数         | 类型   | 是否必需 | 备注                                    |
| ------------ | ------ | -------- | --------------------------------------- |
| generateUuid | string | 是       | 生图任务 uuid，发起生图任务时返回该字段 |

- **返回值**：

| 参数                 | 类型     | 备注                                        |
| -------------------- | -------- | ------------------------------------------- |
| generateUuid         | string   | 生图任务 uuid，使用该 uuid 查询生图进度     |
| generateStatus       | int      | 生图状态见下方 3.3.1 节                     |
| percentCompleted     | float    | 生图进度，0 到 1 之间的浮点数，（暂未实现） |
| generateMsg          | string   | 生图信息，提供附加信息，如生图失败信息      |
| pointsCost           | int      | 本次生图任务消耗积分数                      |
| accountBalance       | int      | 账户剩余积分数                              |
| images               | []object | 图片列表，只提供审核通过的图片              |
| images.0.imageUrl    | string   | 图片地址，可直接访问，地址有时效性：7 天    |
| images.0.seed        | int      | 随机种子值                                  |
| images.0.auditStatus | int      | 审核状态见下方 4.3.1 节                     |

示例：

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "generateUuid": "8dcbfa2997444899b71357ccb7db378b",
    "generateStatus": 5,
    "percentCompleted": 0,
    "generateMsg": "",
    "pointsCost": 10, // 本次任务消耗积分数
    "accountBalance": 1356402, // 账户剩余积分数
    "images": [
      {
        "imageUrl": "https://liblibai-online.liblib.cloud/sd-images/08efe30c1cacc4bb08df8585368db1f9c082b6904dd8150e6e0de5bc526419ee.png",
        "seed": 12345,
        "auditStatus": 3
      }
    ]
  }
}
```

### 3.3 参数模版预设

还提供了一些封装后的参数预设，您可以只提供必要的生图参数，极大简化了配置成本，欢迎体验~

#### 3.3.1 模版选择（templateUuid）

| 模板名称                 | 模板 UUID                        | 备注                                                                  |
| ------------------------ | -------------------------------- | --------------------------------------------------------------------- |
| 星流 Star-3 Alpha 文生图 | 5d7e67009b344550bc1aa6ccbfa1d7f4 | Checkpoint 默认为官方自研模型 Star-3 Alpha，支持指定的几款 Controlnet |
| 星流 Star-3 Alpha 图生图 | 07e00af4fc464c7ab55ff906f8acf1b7 | Checkpoint 默认为官方自研模型 Star-3 Alpha，支持指定的几款 Controlnet |

### 3.4 ComfyUI 接入星流 API

- 准备 Comfyui 环境，到[https://github.com/comfyanonymous/ComfyUI](https://github.com/comfyanonymous/ComfyUI)下载免安装文件，解压，有显卡点击 run_nvidia_gpu.bat 启动 Comfyui，没有显卡点击 run_cpu.bat 启动，启动后保留运行后台不关闭，在 web 进行配置操作。

- 下载星流节点文件[https://github.com/lib-teamwork/ComfyUI-liblib](https://github.com/lib-teamwork/ComfyUI-liblib)，放到./ComfyUl/custom_nodes 文件夹下。

- 重启 Comfyui 打开 workflow 文件夹，图片生成工作流文件

- 鉴权信息需要 API 密钥，appkey 对应 Accesskey，appsecret 对应 Secretkey

- 建议自己再安装一个 comfyui manager 维护各种新节点: [https://github.com/ltdrdata/ComfyUI-Manager](https://github.com/ltdrdata/ComfyUI-Manager)

## 4. LiblibAI 自定义模型

可自由调用 LiblibAI 网站内 F.1-dev/XL/v3/v1.5 全量模型（暂不支持混元和 PixArt），适合高度自由和精准控制的场景。

**调用条件：**

- 同账号下的个人主页内所有模型，本地模型可先在 LiblibAI 官网右上角"发布"上传个人模型，可按需设置"仅个人可见"，即可仅被本账号在 API 调用，不会被公开查看或调用。
- LiblibAI 官网内，模型详情页右侧，作者授权"可出售生成图片或用于商业目的"的所有模型。

### 4.1 接口文档

#### 4.1.1 查询模型版本

在 LiblibAI 网站上挑选作者授权可商用的模型，个人私有模型上传时选择"自见"的模型也可被个人 api 账号调用，获取模型链接结尾的 version_uuid，调接口进行查询。

#### 4.1.2 查询模型版本参数示例

- **接口**：POST /api/model/version/get

- **headers**：

| header       | value            | 备注 |
| ------------ | ---------------- | ---- |
| Content-Type | application/json |      |

- **请求 body**：

| 参数        | 类型   | 是否必需 | 说明                  | 备注                                                                                              |
| ----------- | ------ | -------- | --------------------- | ------------------------------------------------------------------------------------------------- |
| versionUuid | string | 是       | 要查询的模型版本 uuid | 目前 Lib 已开放全站的可商用模型供 API 使用，您可以在 Lib 站内检索可商用的 Checkpoint 和 LoRA 模型 |

##### 4.1.2.1 返回值示例

```json
{
  "version_uuid": "21df5d84cca74f7a885ba672b5a80d19", //LiblibAI官网模型链接后缀
  "model_name": "AWPortrait XL",
  "version_name": "1.1",
  "baseAlgo": "基础算法 XL",
  "show_type": "1", //公开可用的模型
  "commercial_use": "1", //可商用为1，不可商用为0
  "model_url": "https://www.liblib.art/modelinfo/f8b990b20cb943e3aa0e96f34099d794?versionUuid=21df5d84cca74f7a885ba672b5a80d19"
}
```

##### 4.1.2.2 异常情况：

未匹配到：提示"未找到与{version_uuid}对应的模型，请检查 version_uuid 是否正确，或所选模型是否为 Checkpoint 或 LoRA"；

baseAlgo 不在给定范围内的，提示"{version_uuid}不在 API 目前支持的 baseAlgo 范围内"。

#### 4.1.3 提交文生图任务

- **接口**：POST /api/generate/webui/text2img

- **headers**：

| header       | value            | 备注 |
| ------------ | ---------------- | ---- |
| Content-Type | application/json |      |

- **请求 body**：

| 参数           | 类型   | 是否必需 | 说明                | 备注                                       |
| -------------- | ------ | -------- | ------------------- | ------------------------------------------ |
| templateUuid   | string | 否       | 参数模板 uuid       |                                            |
| generateParams | object | 是       | 生图参数，json 结构 | 参数中的图片字段需提供可访问的完整图片地址 |

- **返回值**：

| 参数         | 类型   | 备注                                    |
| ------------ | ------ | --------------------------------------- |
| generateUuid | string | 生图任务 uuid，使用该 uuid 查询生图进度 |

##### 4.1.3.1 文生图参数示例

注：如果要使用如下参数示例生图，请把其中的注释删掉后再使用。

```json
{
  "templateUuid": "e10adc3949ba59abbe56e057f20f883e",
  "generateParams": {
    "checkPointId": "0ea388c7eb854be3ba3c6f65aac6bfd3", // 底模 modelVersionUUID
    "prompt": "Asian portrait,A young woman wearing a green baseball cap,covering one eye with her hand", // 选填
    "negativePrompt": "ng_deepnegative_v1_75t,(badhandv4:1.2),EasyNegative,(worst quality:2),", //选填
    "sampler": 15, // 采样方法
    "steps": 20, // 采样步数
    "cfgScale": 7, // 提示词引导系数
    "width": 768, // 宽
    "height": 1024, // 高
    "imgCount": 1, // 图片数量
    "randnSource": 0, // 随机种子生成器 0 cpu，1 Gpu
    "seed": 2228967414, // 随机种子值，-1表示随机
    "restoreFaces": 0, // 面部修复，0关闭，1开启

    // Lora添加，最多5个
    "additionalNetwork": [
      {
        "modelId": "31360f2f031b4ff6b589412a52713fcf", //LoRA的模型版本versionuuid
        "weight": 0.3 // LoRA权重
      },
      {
        "modelId": "365e700254dd40bbb90d5e78c152ec7f", //LoRA的模型版本uuid
        "weight": 0.6 // LoRA权重
      }
    ],

    // 高分辨率修复
    "hiResFixInfo": {
      "hiresSteps": 20, // 高分辨率修复的重绘步数
      "hiresDenoisingStrength": 0.75, // 高分辨率修复的重绘幅度
      "upscaler": 10, // 放大算法模型枚举
      "resizedWidth": 1024, // 放大后的宽度
      "resizedHeight": 1536 // 放大后的高度
    }
  }
}
```

##### 4.1.3.2 返回值示例

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "generateUuid": "8dcbfa2997444899b71357ccb7db378b"
  }
}
```

## 生图状态说明

### 生图状态（generateStatus）

| 状态枚举值 | 描述     | 备注                                                            |
| ---------- | -------- | --------------------------------------------------------------- |
| 1          | 等待执行 |                                                                 |
| 2          | 执行中   |                                                                 |
| 3          | 已生图   |                                                                 |
| 4          | 审核中   |                                                                 |
| 5          | 成功     |                                                                 |
| 6          | 失败     |                                                                 |
| 7          | 超时     | 任务创建 30 分钟后没有执行结果就计入 timeout 状态，并解冻积分。 |

### 审核状态（auditStatus）

| 状态枚举值 | 描述     | 备注 |
| ---------- | -------- | ---- |
| 1          | 待审核   |      |
| 2          | 审核中   |      |
| 3          | 审核通过 |      |
| 4          | 审核拦截 |      |
| 5          | 审核失败 |      |

## 5. F.1 Kontext

单次调用消耗 api 积分：

- pro 版本: 29 积分，原价 0.29 元/张
- max 版本: 58 积分，原价 0.58 元/张

### 5.1 F.1 Kontext - 文生图

#### 5.1.1 接口定义

- **请求地址**：

```javascript
POST / api / generate / kontext / text2img
```

- **headers**：

| header       | value            | 备注 |
| ------------ | ---------------- | ---- |
| Content-Type | application/json |      |

- **请求 body**：

| 参数           | 类型   | 是否必需 | 说明                             | 备注                                       |
| -------------- | ------ | -------- | -------------------------------- | ------------------------------------------ |
| templateUuid   | string | 是       | fe9928fde1b4491c9b360dd24aa2b115 |                                            |
| generateParams | object | 是       | 生图参数，json 结构              | 参数中的图片字段需提供可访问的完整图片地址 |

#### 5.1.2 参数说明

| 变量名         | 格式   | 备注             | 数值范围                                                       | 必填 | 示例 |
| -------------- | ------ | ---------------- | -------------------------------------------------------------- | ---- | ---- |
| model          | enums  | 模型             | pro / max：默认                                                | 否   |      |
| prompt         | string | 正向提示词，文本 | 不超过 2000 字符                                               | 是   |      |
| aspectRatio    | enums  | 图片宽高比       | 1:1 - 默认 / 2:3 / 3:2 / 3:4 / 4:3 / 9:16 / 16:9 / 9:21 / 21:9 | 否   |      |
| imgCount       | int    | 单次生图张数     | 默认值：1，阈值范围：1 ~ 4                                     | 否   |      |
| guidance_scale | double | 提示词引导系数   | 默认值：3.5，阈值范围：1.0 ~ 20.0                              | 否   |      |

```json
{
  "templateUuid": "fe9928fde1b4491c9b360dd24aa2b115",
  "generateParams": {
    "model": "pro",
    "prompt": "画一个LibLib公司的品牌海报",
    "aspectRatio": "3:4",
    "guidance_scale": 3.5,
    "imgCount": 1
  }
}
```

#### 5.1.3 返回值

| 参数         | 类型   | 备注                                    |
| ------------ | ------ | --------------------------------------- |
| generateUuid | string | 生图任务 uuid，使用该 uuid 查询生图进度 |

### 5.2 F.1 Kontext - 图生图（指令编辑&多图参考）

#### 5.2.1 接口定义

- **请求地址**：

```javascript
POST / api / generate / kontext / img2img
```

- **headers**：

| header       | value            |
| ------------ | ---------------- |
| Content-Type | application/json |

- **请求 body**：

| 参数           | 类型   | 是否必需 | 说明                             | 备注                                       |
| -------------- | ------ | -------- | -------------------------------- | ------------------------------------------ |
| templateUuid   | string | 是       | 1c0a9712b3d84e1b8a9f49514a46d88c |                                            |
| generateParams | object | 是       | 生图参数，json 结构              | 参数中的图片字段需提供可访问的完整图片地址 |

#### 5.2.2 参数说明

| 变量名         | 格式   | 备注             | 数值范围                                                                                           | 必填 | 示例 |
| -------------- | ------ | ---------------- | -------------------------------------------------------------------------------------------------- | ---- | ---- |
| model          | enums  | 模型             | pro：暂不支持多图参考 / max：默认                                                                  | 否   |      |
| prompt         | string | 正向提示词，文本 | 不超过 2000 字符                                                                                   | 是   |      |
| aspectRatio    | enums  | 图片宽高比       | 1:1 - 默认 / 2:3 / 3:2 / 3:4 / 4:3 / 9:16 / 16:9 / 9:21 / 21:9                                     | 否   |      |
| imgCount       | int    | 单次生图张数     | 默认值：1，阈值范围：1 ~ 4                                                                         | 否   |      |
| guidance_scale | double | 提示词引导系数   | 默认值：3.5，阈值范围：1.0 ~ 20.0                                                                  | 否   |      |
| image_list     | Array  | 参考图           | 图片数量：1~4，可公网访问的图片地址，图片格式：PNG, JPG, JPEG, WEBP，图片大小：每张图都不超过 10MB | 是   |      |

```json
{
  "templateUuid": "1c0a9712b3d84e1b8a9f49514a46d88c",
  "generateParams": {
    "prompt": "Turn this image into a Ghibli-style, a traditional Japanese anime aesthetics.",
    "aspectRatio": "2:3",
    "guidance_scale": 3.5,
    "imgCount": 1,
    "image_list": [
      "https://liblibai-online.liblib.cloud/img/081e9f07d9bd4c2ba090efde163518f9/3c65a38d7df2589c4bf834740385192128cf035c7c779ae2bbbc354bf0efcfcb.png"
    ]
  }
}
```

#### 5.2.3 返回值

| 参数         | 类型   | 备注                                    |
| ------------ | ------ | --------------------------------------- |
| generateUuid | string | 生图任务 uuid，使用该 uuid 查询生图进度 |

### 5.3 查询任务结果

#### 5.3.1 接口定义

|          | 说明                            |
| -------- | ------------------------------- |
| 接口定义 | 接口：POST /api/generate/status |

- **请求 body**：

| 参数         | 类型   | 是否必需 | 备注                                    |
| ------------ | ------ | -------- | --------------------------------------- |
| generateUuid | string | 是       | 生图任务 uuid，发起生图任务时返回该字段 |

#### 5.3.2 返回值

| 参数                 | 类型     | 备注                                     |
| -------------------- | -------- | ---------------------------------------- |
| generateUuid         | string   | 生图任务 uuid，使用该 uuid 查询生图进度  |
| generateStatus       | int      | 生图状态见下方 3.3.1 节                  |
| percentCompleted     | float    | 生图进度（智能算法 IMG1 不支持）         |
| generateMsg          | string   | 生图信息，提供附加信息，如生图失败信息   |
| pointsCost           | int      | 本次生图任务消耗积分数                   |
| accountBalance       | int      | 账户剩余积分数                           |
| images               | []object | 图片列表，只提供审核通过的图片           |
| images.0.imageUrl    | string   | 图片地址，可直接访问，地址有时效性：7 天 |
| images.0.seed        | int      | 随机种子值（智能算法 IMG1 不支持）       |
| images.0.auditStatus | int      | 审核状态说明                             |

## 6. 智能算法 IMG1

### 6.1 智能算法 IMG1 - 生图

#### 6.1.1 接口定义

- **请求地址**：

```javascript
POST / api / generate / smart - img1 / generate
```

- **headers**：

| header       | value            | 备注 |
| ------------ | ---------------- | ---- |
| Content-Type | application/json |      |

- **请求 body**：

| 参数           | 类型   | 是否必需 | 说明                             | 备注                                       |
| -------------- | ------ | -------- | -------------------------------- | ------------------------------------------ |
| templateUuid   | string | 是       | 86c58ea26e9a45bd9f562c6306c17c0f |                                            |
| generateParams | object | 是       | 生图参数，json 结构              | 参数中的图片字段需提供可访问的完整图片地址 |

#### 6.1.2 参数说明

| 变量名      | 格式   | 备注             | 数值范围                                                                                                      | 必填 | 示例 |
| ----------- | ------ | ---------------- | ------------------------------------------------------------------------------------------------------------- | ---- | ---- |
| prompt      | string | 正向提示词，文本 | 不超过 2000 字符                                                                                              | 是   |      |
| aspectRatio | string | 宽高比           | auto：自适应，square：1:1 宽高比 通用，portrait：3:4 宽高比 适合人物肖像，landscape：16:9 宽高比 适合影视画幅 |      |      |
| quality     | string | 质量             | normal：普通，high：高质量                                                                                    |      |      |
| imgCount    | int    | 单次生图张数     | 1 ~ 4                                                                                                         |      |      |
| image_list  | Array  | 参考图           | 图片数量：1~4，可公网访问的图片地址                                                                           |      |      |

```json
{
  "templateUuid": "86c58ea26e9a45bd9f562c6306c17c0f",
  "generateParams": {
    "prompt": "参考以下两张图，让黄猫坐在椅子上，画一张海报",
    "aspectRatio": "auto",
    "quality": "normal",
    "imgCount": 1,
    "image_list": [
      "https://liblibai-online.liblib.cloud/img/081e9f07d9bd4c2ba090efde163518f9/3c65a38d7df2589c4bf834740385192128cf035c7c779ae2bbbc354bf0efcfcb.png",
      "https://liblibai-online.liblib.cloud/img/081e9f07d9bd4c2ba090efde163518f9/92cc6b39931ed0932dfe49a7b354ce1a8f6ede819ccbf8a9f3a2fc315b0be42a.png"
    ]
  }
}
```

---

本文档为 LiblibAI x 星流 图像大模型 API 的完整使用说明，涵盖了从基础配置到高级功能的全部内容。如有疑问，请联系技术支持。
