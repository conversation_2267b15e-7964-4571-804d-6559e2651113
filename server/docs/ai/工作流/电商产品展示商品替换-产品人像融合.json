{"id": "9cf793d9-41d0-4cc2-bae9-00b95a231ba9", "revision": 0, "last_node_id": 17, "last_link_id": 7, "nodes": [{"id": 14, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [585.3818969726562, 39.3580436706543], "size": [315, 330], "flags": {}, "order": 4, "mode": 0, "inputs": [{"label": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 5}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [6]}, {"label": "mask", "name": "mask", "type": "MASK"}, {"label": "original_size", "name": "original_size", "type": "BOX"}, {"label": "width", "name": "width", "type": "INT"}, {"label": "height", "name": "height", "type": "INT"}], "properties": {"Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2", "widget_ue_connectable": {}}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "8", "None", 1024, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 13, "type": "ImageConcatMulti", "pos": [221.6285400390625, 45.9323616027832], "size": [315, 150], "flags": {}, "order": 3, "mode": 0, "inputs": [{"label": "image_1", "name": "image_1", "type": "IMAGE", "link": 3}, {"label": "image_2", "name": "image_2", "shape": 7, "type": "IMAGE", "link": 4}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": [5]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": [2, "right", true, null]}, {"id": 17, "type": "SaveImage", "pos": [1848.645751953125, 40.8674430847168], "size": [315, 270], "flags": {}, "order": 6, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 7}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI"]}, {"id": 2, "type": "LoadImage", "pos": [-156.65902709960938, 44.44119644165039], "size": [315, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [3]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "widget_ue_connectable": {}}, "widgets_values": ["a839672c17e538a86b6a921d4868bc8991560045ef070c064a2551a8947f377c.jpg", "image"]}, {"id": 4, "type": "LoadImage", "pos": [-159.15194702148438, 429.5657043457031], "size": [315, 314], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [4]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "widget_ue_connectable": {}}, "widgets_values": ["79bb0fbd27aa5d42244372b44a9346dbb7b03ff35dd0940ae268b4ff801b2fff.jpg", "image"]}, {"id": 9, "type": "RH_Translator", "pos": [964.8306884765625, 41.607906341552734], "size": [400, 200], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "translation", "name": "translation", "type": "STRING", "links": [2]}], "properties": {"Node name for S&R": "RH_Translator", "widget_ue_connectable": {}}, "widgets_values": ["女性手拿着小风扇贴着脸颊，保持女生原有的发型跟五官，小风扇形状保持不变，注意大小比例不要太大，处理好光影，", 1782, "randomize", "force_english"]}, {"id": 16, "type": "RH_ComfyFluxKontext", "pos": [1409.3714599609375, 41.53677749633789], "size": [400, 420], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "input_image", "name": "input_image", "shape": 7, "type": "IMAGE", "link": 6}, {"label": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": 2}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [7]}, {"label": "images_urls", "name": "images_urls", "type": "STRING"}], "properties": {"Node name for S&R": "RH_ComfyFluxKontext", "widget_ue_connectable": {}}, "widgets_values": ["", "flux-kontext-pro", "", "3:4", 1, 1671931033, "randomize", true, 3.5, "ai.t8star.cn", "png", false, 2, false]}], "links": [[2, 9, 0, 16, 1, "STRING"], [3, 2, 0, 13, 0, "IMAGE"], [4, 4, 0, 13, 1, "IMAGE"], [5, 13, 0, 14, 0, "IMAGE"], [6, 14, 0, 16, 0, "IMAGE"], [7, 16, 0, 17, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "VHS_latentpreview": false, "ds": {"scale": 0.9849732675807628, "offset": [231.66226798153804, 86.62239241209119]}, "frontendVersion": "1.23.0"}, "version": 0.4}