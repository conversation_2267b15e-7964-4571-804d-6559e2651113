{"id": "206247b6-9fec-4ed2-8927-e4f388c674d4", "revision": 0, "last_node_id": 90, "last_link_id": 113, "nodes": [{"id": 75, "type": "AudioCrop", "pos": [100.79749298095703, -1469.225341796875], "size": [315, 82], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "audio", "name": "audio", "type": "AUDIO", "link": 93}, {"label": "start_time", "name": "start_time", "type": "STRING", "widget": {"name": "start_time"}}, {"label": "end_time", "name": "end_time", "type": "STRING", "widget": {"name": "end_time"}}], "outputs": [{"label": "AUDIO", "name": "AUDIO", "type": "AUDIO", "links": [94, 101]}], "properties": {"Node name for S&R": "AudioCrop", "cnr_id": "audio-separation-nodes-comfyui", "ver": "1.4.0", "widget_ue_connectable": {}}, "widgets_values": ["0:0", "0:15"]}, {"id": 76, "type": "SaveAudio", "pos": [455.8834533691406, -1471.682373046875], "size": [315, 112], "flags": {}, "order": 15, "mode": 4, "inputs": [{"label": "audio", "name": "audio", "type": "AUDIO", "link": 94}, {"label": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}, {"label": "audioUI", "name": "audioUI", "type": "AUDIO_UI", "widget": {"name": "audioUI"}}], "outputs": [], "properties": {"Node name for S&R": "SaveAudio", "cnr_id": "comfy-core", "ver": "0.3.35", "widget_ue_connectable": {}}, "widgets_values": ["pl-index-tts"]}, {"id": 72, "type": "TextInput_", "pos": [-352.014404296875, -1244.88525390625], "size": [400, 200], "flags": {}, "order": 0, "mode": 0, "inputs": [{"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}}], "outputs": [{"label": "STRING", "name": "STRING", "type": "STRING", "links": [102]}], "title": "修改文本：用于数字人说话的内容", "properties": {"Node name for S&R": "TextInput_", "cnr_id": "comfyui-mixlab-nodes", "ver": "b2bb1876def6330fccf1e03cc69d2166cae7bedb", "widget_ue_connectable": {}}, "widgets_values": ["熏风初定月横斜，小院清幽落藕花。\n竹榻微凉消溽暑，蒲扇轻摇送流霞。\n蝉鸣渐歇栖高树，萤火时明透浅纱。\n忽有荷香随露至，一杯清茗话桑麻。"]}, {"id": 86, "type": "easy cleanGpuUsed", "pos": [570.1157836914062, -750.910400390625], "size": [210, 26], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 106}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [107]}], "properties": {"Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 78, "type": "RH_HeyGemNode", "pos": [1239.6490478515625, -936.9403076171875], "size": [357.4807434082031, 77.53885650634766], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "AUDIO", "name": "AUDIO", "type": "AUDIO", "link": 105}, {"label": "VIDEO", "name": "VIDEO", "type": "VIDEO", "link": 95}], "outputs": [{"label": "VIDEO", "name": "VIDEO", "type": "VIDEO", "links": [100]}], "properties": {"Node name for S&R": "RH_HeyGemNode", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 79, "type": "CreateVideo", "pos": [1253.0205078125, -803.481689453125], "size": [343.671875, 95.91987609863281], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 96}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO", "link": 104}, {"label": "fps", "name": "fps", "type": "FLOAT", "widget": {"name": "fps"}, "link": 99}], "outputs": [{"label": "VIDEO", "name": "VIDEO", "type": "VIDEO", "links": [95]}], "properties": {"Node name for S&R": "CreateVideo", "widget_ue_connectable": {"fps": true}}, "widgets_values": [30]}, {"id": 81, "type": "PrimitiveFloat", "pos": [1252.50048828125, -648.8109741210938], "size": [345.10528564453125, 86.67173767089844], "flags": {}, "order": 1, "mode": 0, "inputs": [{"label": "value", "name": "value", "type": "FLOAT", "widget": {"name": "value"}}], "outputs": [{"label": "FLOAT", "name": "FLOAT", "type": "FLOAT", "links": [99]}], "properties": {"Node name for S&R": "PrimitiveFloat", "widget_ue_connectable": {}}, "widgets_values": [16]}, {"id": 77, "type": "LoadAudio", "pos": [-319.4711608886719, -1468.376953125], "size": [315, 136], "flags": {}, "order": 2, "mode": 0, "inputs": [{"label": "audio", "name": "audio", "type": "COMBO", "widget": {"name": "audio"}}, {"label": "audioUI", "name": "audioUI", "type": "AUDIO_UI", "widget": {"name": "audioUI"}}, {"label": "upload", "name": "upload", "type": "AUDIOUPLOAD", "widget": {"name": "upload"}}], "outputs": [{"label": "AUDIO", "name": "AUDIO", "type": "AUDIO", "links": [93]}], "properties": {"Node name for S&R": "LoadAudio", "cnr_id": "comfy-core", "ver": "0.3.35", "widget_ue_connectable": {}}, "widgets_values": ["1f74e265aa9d931bb0c9c7ca3ac4efb70507a60e5b37be4f89895824e105a477.mp3", null, null]}, {"id": 83, "type": "IndexTTSRun", "pos": [100.74958038330078, -1297.1612548828125], "size": [315, 342], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "audio", "name": "audio", "type": "AUDIO", "link": 101}, {"label": "text", "name": "text", "type": "STRING", "link": 102}, {"label": "dialogue_audio_s2", "name": "dialogue_audio_s2", "shape": 7, "type": "AUDIO"}, {"label": "version", "name": "version", "type": "COMBO", "widget": {"name": "version"}}, {"label": "top_k", "name": "top_k", "type": "INT", "widget": {"name": "top_k"}}, {"label": "top_p", "name": "top_p", "type": "FLOAT", "widget": {"name": "top_p"}}, {"label": "temperature", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}}, {"label": "num_beams", "name": "num_beams", "type": "INT", "widget": {"name": "num_beams"}}, {"label": "max_mel_tokens", "name": "max_mel_tokens", "type": "INT", "widget": {"name": "max_mel_tokens"}}, {"label": "max_text_tokens_per_sentence", "name": "max_text_tokens_per_sentence", "type": "INT", "widget": {"name": "max_text_tokens_per_sentence"}}, {"label": "sentences_bucket_max_size", "name": "sentences_bucket_max_size", "type": "INT", "widget": {"name": "sentences_bucket_max_size"}}, {"label": "fast_inference", "name": "fast_inference", "type": "BOOLEAN", "widget": {"name": "fast_inference"}}, {"label": "custom_cuda_kernel", "name": "custom_cuda_kernel", "type": "BOOLEAN", "widget": {"name": "custom_cuda_kernel"}}, {"label": "unload_model", "name": "unload_model", "type": "BOOLEAN", "widget": {"name": "unload_model"}}], "outputs": [{"label": "audio", "name": "audio", "type": "AUDIO", "links": [103, 104, 105]}], "properties": {"Node name for S&R": "IndexTTSRun", "widget_ue_connectable": {}}, "widgets_values": ["v1.5", 30, 0.8, 1, 3, 1000, 120, 4, false, false, true]}, {"id": 74, "type": "SaveAudio", "pos": [444.3232727050781, -1161.190673828125], "size": [315, 112], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "audio", "name": "audio", "type": "AUDIO", "link": 103}, {"label": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}, {"label": "audioUI", "name": "audioUI", "type": "AUDIO_UI", "widget": {"name": "audioUI"}}], "outputs": [], "properties": {"Node name for S&R": "SaveAudio", "cnr_id": "comfy-core", "ver": "0.3.35", "widget_ue_connectable": {}}, "widgets_values": ["pl-index-tts"]}, {"id": 71, "type": "WanVideoLoraSelect", "pos": [-317.7796936035156, -251.96298217773438], "size": [503.4073486328125, 150], "flags": {}, "order": 3, "mode": 0, "inputs": [{"label": "prev_lora", "name": "prev_lora", "shape": 7, "type": "WANVIDLORA"}, {"label": "blocks", "name": "blocks", "shape": 7, "type": "SELECTEDBLOCKS"}, {"label": "lora", "name": "lora", "type": "COMBO", "widget": {"name": "lora"}}, {"label": "strength", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}}, {"label": "low_mem_load", "name": "low_mem_load", "shape": 7, "type": "BOOLEAN", "widget": {"name": "low_mem_load"}}, {"label": "merge_loras", "name": "merge_loras", "shape": 7, "type": "BOOLEAN", "widget": {"name": "merge_loras"}}], "outputs": [{"label": "lora", "name": "lora", "type": "WANVIDLORA", "links": [89]}], "properties": {"Node name for S&R": "WanVideoLoraSelect", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "058286fc0f3b0651a2f6b68309df3f06e8332cc0", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors", 1.2000000000000002, false, true]}, {"id": 22, "type": "WanVideoModelLoader", "pos": [-308.1202392578125, -564.7424926757812], "size": [477.4410095214844, 294], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS"}, {"label": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS"}, {"label": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA", "link": 89}, {"label": "vram_management_args", "name": "vram_management_args", "shape": 7, "type": "VRAM_MANAGEMENTARGS"}, {"label": "vace_model", "name": "vace_model", "shape": 7, "type": "VACEPATH"}, {"label": "fantasytalking_model", "name": "fantasytalking_model", "shape": 7, "type": "FANTASYTALKINGMODEL"}, {"label": "multitalk_model", "name": "multitalk_model", "shape": 7, "type": "MULTITALKMODEL"}, {"label": "fantasyportrait_model", "name": "fantasyportrait_model", "shape": 7, "type": "FANTASYPORTRAITMODEL"}, {"label": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}}, {"label": "base_precision", "name": "base_precision", "type": "COMBO", "widget": {"name": "base_precision"}}, {"label": "quantization", "name": "quantization", "type": "COMBO", "widget": {"name": "quantization"}}, {"label": "load_device", "name": "load_device", "type": "COMBO", "widget": {"name": "load_device"}}, {"label": "attention_mode", "name": "attention_mode", "shape": 7, "type": "COMBO", "widget": {"name": "attention_mode"}}], "outputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "slot_index": 0, "links": [29, 79]}], "properties": {"Node name for S&R": "WanVideoModelLoader", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": ["wan2114BFusionx_fusionxImage2video.safetensors", "bf16", "disabled", "offload_device", "sdpa"], "color": "#223", "bgcolor": "#335"}, {"id": 11, "type": "LoadWanVideoT5TextEncoder", "pos": [-278.8022155761719, -731.3123168945312], "size": [377.1661376953125, 130], "flags": {}, "order": 4, "mode": 0, "inputs": [{"label": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}}, {"label": "precision", "name": "precision", "type": "COMBO", "widget": {"name": "precision"}}, {"label": "load_device", "name": "load_device", "shape": 7, "type": "COMBO", "widget": {"name": "load_device"}}, {"label": "quantization", "name": "quantization", "shape": 7, "type": "COMBO", "widget": {"name": "quantization"}}], "outputs": [{"label": "wan_t5_model", "name": "wan_t5_model", "type": "WANTEXTENCODER", "slot_index": 0, "links": [15]}], "properties": {"Node name for S&R": "LoadWanVideoT5TextEncoder", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": ["umt5_xxl_fp16.safetensors", "bf16", "offload_device", "disabled"], "color": "#332922", "bgcolor": "#593930"}, {"id": 16, "type": "WanVideoTextEncode", "pos": [264.2879943847656, -550.897705078125], "size": [420.30511474609375, 261.5306701660156], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "t5", "name": "t5", "shape": 7, "type": "WANTEXTENCODER", "link": 15}, {"label": "model_to_offload", "name": "model_to_offload", "shape": 7, "type": "WANVIDEOMODEL", "link": 79}, {"label": "positive_prompt", "name": "positive_prompt", "type": "STRING", "widget": {"name": "positive_prompt"}, "link": 113}, {"label": "negative_prompt", "name": "negative_prompt", "type": "STRING", "widget": {"name": "negative_prompt"}}, {"label": "force_offload", "name": "force_offload", "shape": 7, "type": "BOOLEAN", "widget": {"name": "force_offload"}}, {"label": "use_disk_cache", "name": "use_disk_cache", "shape": 7, "type": "BOOLEAN", "widget": {"name": "use_disk_cache"}}, {"label": "device", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}}], "outputs": [{"label": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "slot_index": 0, "links": [106]}], "properties": {"Node name for S&R": "WanVideoTextEncode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {"positive_prompt": true}}, "widgets_values": ["", "bright tones, overexposed, static, blurred details, subtitles, style, works, paintings, images, static, overall gray, worst quality, low quality, JPEG compression residue, ugly, incomplete, extra fingers, poorly drawn hands, poorly drawn faces, deformed, disfigured, misshapen limbs, fused fingers, still picture, messy background, three legs, many people in the background, walking backwards", true, false, "gpu"], "color": "#332922", "bgcolor": "#593930"}, {"id": 38, "type": "WanVideoVAELoader", "pos": [-290.1136169433594, 83.22710418701172], "size": [338.8450012207031, 88.59707641601562], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS"}, {"label": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}}, {"label": "precision", "name": "precision", "shape": 7, "type": "COMBO", "widget": {"name": "precision"}}], "outputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "slot_index": 0, "links": [43, 81]}], "properties": {"Node name for S&R": "WanVideoVAELoader", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": ["Wan2_1_VAE_bf16.safetensors", "bf16"], "color": "#322", "bgcolor": "#533"}, {"id": 59, "type": "CLIPVisionLoader", "pos": [-281.6732482910156, 234.5884552001953], "size": [315, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [{"label": "clip_name", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}}], "outputs": [{"label": "CLIP_VISION", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [70]}], "properties": {"Node name for S&R": "CLIPVisionLoader", "cnr_id": "comfy-core", "ver": "0.3.26", "widget_ue_connectable": {}}, "widgets_values": ["clip_vision_h.safetensors"], "color": "#233", "bgcolor": "#355"}, {"id": 65, "type": "WanVideoClipVisionEncode", "pos": [59.29046630859375, 243.9368133544922], "size": [327.5999755859375, 262], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "clip_vision", "name": "clip_vision", "type": "CLIP_VISION", "link": 70}, {"label": "image_1", "name": "image_1", "type": "IMAGE", "link": 73}, {"label": "image_2", "name": "image_2", "shape": 7, "type": "IMAGE"}, {"label": "negative_image", "name": "negative_image", "shape": 7, "type": "IMAGE"}, {"label": "strength_1", "name": "strength_1", "type": "FLOAT", "widget": {"name": "strength_1"}}, {"label": "strength_2", "name": "strength_2", "type": "FLOAT", "widget": {"name": "strength_2"}}, {"label": "crop", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}}, {"label": "combine_embeds", "name": "combine_embeds", "type": "COMBO", "widget": {"name": "combine_embeds"}}, {"label": "force_offload", "name": "force_offload", "type": "BOOLEAN", "widget": {"name": "force_offload"}}, {"label": "tiles", "name": "tiles", "shape": 7, "type": "INT", "widget": {"name": "tiles"}}, {"label": "ratio", "name": "ratio", "shape": 7, "type": "FLOAT", "widget": {"name": "ratio"}}], "outputs": [{"label": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_CLIPEMBEDS", "links": [82]}], "properties": {"Node name for S&R": "WanVideoClipVisionEncode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": [1, 1, "center", "average", true, 0, 0.20000000000000004], "color": "#233", "bgcolor": "#355"}, {"id": 55, "type": "WanVideoEnhanceAVideo", "pos": [59.7093505859375, 84.90789031982422], "size": [315, 106], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "weight", "name": "weight", "type": "FLOAT", "widget": {"name": "weight"}}, {"label": "start_percent", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}}, {"label": "end_percent", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}}], "outputs": [{"label": "feta_args", "name": "feta_args", "type": "FETAARGS", "links": [80]}], "properties": {"Node name for S&R": "WanVideoEnhanceAVideo", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": [2, 0, 1]}, {"id": 28, "type": "WanVideoDecode", "pos": [414.39019775390625, 70.85263061523438], "size": [315, 198], "flags": {}, "order": 27, "mode": 0, "inputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "link": 43}, {"label": "samples", "name": "samples", "type": "LATENT", "link": 109}, {"label": "enable_vae_tiling", "name": "enable_vae_tiling", "type": "BOOLEAN", "widget": {"name": "enable_vae_tiling"}}, {"label": "tile_x", "name": "tile_x", "type": "INT", "widget": {"name": "tile_x"}}, {"label": "tile_y", "name": "tile_y", "type": "INT", "widget": {"name": "tile_y"}}, {"label": "tile_stride_x", "name": "tile_stride_x", "type": "INT", "widget": {"name": "tile_stride_x"}}, {"label": "tile_stride_y", "name": "tile_stride_y", "type": "INT", "widget": {"name": "tile_stride_y"}}, {"label": "normalization", "name": "normalization", "shape": 7, "type": "COMBO", "widget": {"name": "normalization"}}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "slot_index": 0, "links": [96]}], "properties": {"Node name for S&R": "WanVideoDecode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": [false, 272, 272, 144, 128, "default"], "color": "#322", "bgcolor": "#533"}, {"id": 63, "type": "WanVideoImageToVideoEncode", "pos": [-323.36181640625, 355.61224365234375], "size": [352.79998779296875, 390], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "vae", "name": "vae", "shape": 7, "type": "WANVAE", "link": 81}, {"label": "clip_embeds", "name": "clip_embeds", "shape": 7, "type": "WANVIDIMAGE_CLIPEMBEDS", "link": 82}, {"label": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": 83}, {"label": "end_image", "name": "end_image", "shape": 7, "type": "IMAGE"}, {"label": "control_embeds", "name": "control_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS"}, {"label": "temporal_mask", "name": "temporal_mask", "shape": 7, "type": "MASK"}, {"label": "extra_latents", "name": "extra_latents", "shape": 7, "type": "LATENT"}, {"label": "add_cond_latents", "name": "add_cond_latents", "shape": 7, "type": "ADD_COND_LATENTS"}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 75}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 76}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}}, {"label": "noise_aug_strength", "name": "noise_aug_strength", "type": "FLOAT", "widget": {"name": "noise_aug_strength"}}, {"label": "start_latent_strength", "name": "start_latent_strength", "type": "FLOAT", "widget": {"name": "start_latent_strength"}}, {"label": "end_latent_strength", "name": "end_latent_strength", "type": "FLOAT", "widget": {"name": "end_latent_strength"}}, {"label": "force_offload", "name": "force_offload", "type": "BOOLEAN", "widget": {"name": "force_offload"}}, {"label": "fun_or_fl2v_model", "name": "fun_or_fl2v_model", "shape": 7, "type": "BOOLEAN", "widget": {"name": "fun_or_fl2v_model"}}, {"label": "tiled_vae", "name": "tiled_vae", "shape": 7, "type": "BOOLEAN", "widget": {"name": "tiled_vae"}}], "outputs": [{"label": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "links": [66]}], "properties": {"Node name for S&R": "WanVideoImageToVideoEncode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {"width": true, "height": true}}, "widgets_values": [832, 480, 81, 0.030000000000000006, 1, 1, true, false, false], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 27, "type": "WanVideoSampler", "pos": [405.18963623046875, 328.9990234375], "size": [481.87451171875, 690], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 29}, {"label": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "link": 107}, {"label": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "link": 66}, {"label": "samples", "name": "samples", "shape": 7, "type": "LATENT"}, {"label": "feta_args", "name": "feta_args", "shape": 7, "type": "FETAARGS", "link": 80}, {"label": "context_options", "name": "context_options", "shape": 7, "type": "WANVIDCONTEXT"}, {"label": "cache_args", "name": "cache_args", "shape": 7, "type": "CACHEARGS", "link": 56}, {"label": "flowedit_args", "name": "flowedit_args", "shape": 7, "type": "FLOWEDITARGS"}, {"label": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS"}, {"label": "loop_args", "name": "loop_args", "shape": 7, "type": "LOOPARGS"}, {"label": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS"}, {"label": "sigmas", "name": "sigmas", "shape": 7, "type": "SIGMAS"}, {"label": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE"}, {"label": "fantasytalking_embeds", "name": "fantasytalking_embeds", "shape": 7, "type": "FANTASYTALKING_EMBEDS"}, {"label": "uni3c_embeds", "name": "uni3c_embeds", "shape": 7, "type": "UNI3C_EMBEDS"}, {"label": "multitalk_embeds", "name": "multitalk_embeds", "shape": 7, "type": "MULTITALK_EMBEDS"}, {"label": "freeinit_args", "name": "freeinit_args", "shape": 7, "type": "FREEINITARGS"}, {"label": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}}, {"label": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}}, {"label": "shift", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}}, {"label": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}}, {"label": "force_offload", "name": "force_offload", "type": "BOOLEAN", "widget": {"name": "force_offload"}}, {"label": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}}, {"label": "riflex_freq_index", "name": "riflex_freq_index", "type": "INT", "widget": {"name": "riflex_freq_index"}}, {"label": "denoise_strength", "name": "denoise_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "denoise_strength"}}, {"label": "batched_cfg", "name": "batched_cfg", "shape": 7, "type": "BOOLEAN", "widget": {"name": "batched_cfg"}}, {"label": "rope_function", "name": "rope_function", "shape": 7, "type": "COMBO", "widget": {"name": "rope_function"}}, {"label": "start_step", "name": "start_step", "shape": 7, "type": "INT", "widget": {"name": "start_step"}}, {"label": "end_step", "name": "end_step", "shape": 7, "type": "INT", "widget": {"name": "end_step"}}, {"label": "add_noise_to_samples", "name": "add_noise_to_samples", "shape": 7, "type": "BOOLEAN", "widget": {"name": "add_noise_to_samples"}}], "outputs": [{"label": "samples", "name": "samples", "type": "LATENT", "slot_index": 0, "links": [108]}, {"label": "denoised_samples", "name": "denoised_samples", "type": "LATENT"}], "properties": {"Node name for S&R": "WanVideoSampler", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": [4, 1, 5.000000000000001, 1057359483639287, "fixed", true, "lcm", 0, 1, "", "comfy", 0, -1, false]}, {"id": 52, "type": "WanVideoTeaCache", "pos": [748.2283935546875, 78.58673095703125], "size": [315, 178], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "rel_l1_thresh", "name": "rel_l1_thresh", "type": "FLOAT", "widget": {"name": "rel_l1_thresh"}}, {"label": "start_step", "name": "start_step", "type": "INT", "widget": {"name": "start_step"}}, {"label": "end_step", "name": "end_step", "type": "INT", "widget": {"name": "end_step"}}, {"label": "cache_device", "name": "cache_device", "type": "COMBO", "widget": {"name": "cache_device"}}, {"label": "use_coefficients", "name": "use_coefficients", "type": "BOOLEAN", "widget": {"name": "use_coefficients"}}, {"label": "mode", "name": "mode", "shape": 7, "type": "COMBO", "widget": {"name": "mode"}}], "outputs": [{"label": "cache_args", "name": "cache_args", "type": "CACHEARGS", "links": [56]}], "properties": {"Node name for S&R": "WanVideoTeaCache", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": [0.25, 1, -1, "main_device", "true", "e"]}, {"id": 87, "type": "easy cleanGpuUsed", "pos": [70.31824493408203, 571.8202514648438], "size": [210, 26], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 108}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [109]}], "properties": {"Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 66, "type": "ImageResizeKJ", "pos": [-1375.9002685546875, -1035.1533203125], "size": [315, 266], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 72}, {"label": "get_image_size", "name": "get_image_size", "shape": 7, "type": "IMAGE"}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 87}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 88}, {"label": "upscale_method", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}}, {"label": "keep_proportion", "name": "keep_proportion", "type": "BOOLEAN", "widget": {"name": "keep_proportion"}}, {"label": "divisible_by", "name": "divisible_by", "type": "INT", "widget": {"name": "divisible_by"}}, {"label": "crop", "name": "crop", "shape": 7, "type": "COMBO", "widget": {"name": "crop"}}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [73, 83, 84, 110]}, {"label": "width", "name": "width", "type": "INT", "links": [75]}, {"label": "height", "name": "height", "type": "INT", "links": [76]}], "properties": {"Node name for S&R": "ImageResizeKJ", "cnr_id": "comfyui-kjnodes", "ver": "52c2e31a903fec2dd654fb614ea82ba2757d5028", "widget_ue_connectable": {"width": true, "height": true}}, "widgets_values": [720, 720, "lanc<PERSON>s", true, 16, 0]}, {"id": 70, "type": "INTConstant", "pos": [-1323.3897705078125, -718.5357666015625], "size": [210, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "value", "name": "value", "type": "INT", "widget": {"name": "value"}}], "outputs": [{"label": "value", "name": "value", "type": "INT", "links": [87, 88]}], "title": "输出分辨率设置", "properties": {"Node name for S&R": "INTConstant", "widget_ue_connectable": {}}, "widgets_values": [720], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 90, "type": "Text Concatenate (JPS)", "pos": [-941.1005249023438, -1489.922119140625], "size": [315, 178], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "text1", "name": "text1", "shape": 7, "type": "STRING", "link": 111}, {"label": "text2", "name": "text2", "shape": 7, "type": "STRING", "link": 112}, {"label": "text3", "name": "text3", "shape": 7, "type": "STRING"}, {"label": "text4", "name": "text4", "shape": 7, "type": "STRING"}, {"label": "text5", "name": "text5", "shape": 7, "type": "STRING"}, {"label": "delimiter", "name": "delimiter", "type": "COMBO", "widget": {"name": "delimiter"}}], "outputs": [{"label": "text", "name": "text", "type": "STRING", "links": [113]}], "properties": {"Node name for S&R": "Text Concatenate (JPS)", "widget_ue_connectable": {}}, "widgets_values": ["none"]}, {"id": 89, "type": "TextInput_", "pos": [-1380.547607421875, -1555.451171875], "size": [400, 200], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}}], "outputs": [{"label": "STRING", "name": "STRING", "type": "STRING", "links": [111]}], "properties": {"Node name for S&R": "TextInput_", "widget_ue_connectable": {}}, "widgets_values": ["人物面对镜头，自然的讲话交流，高清4k，人物动作表现非常自然。\n\n"]}, {"id": 88, "type": "RH_Captioner", "pos": [-1366.941162109375, -1296.1141357421875], "size": [402.0733642578125, 89.07491302490234], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "ref_image", "name": "ref_image", "type": "IMAGE", "link": 110}, {"label": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}}], "outputs": [{"label": "describe", "name": "describe", "type": "STRING", "links": [112]}], "properties": {"Node name for S&R": "RH_Captioner", "widget_ue_connectable": {}}, "widgets_values": ["请对这张图片进行详细描述。要清晰的描述内容，让画面清晰，自然。"]}, {"id": 82, "type": "SaveVideo", "pos": [-1416.7034912109375, 495.71514892578125], "size": [912.4972534179688, 1154.7672119140625], "flags": {}, "order": 30, "mode": 0, "inputs": [{"label": "video", "name": "video", "type": "VIDEO", "link": 100}, {"label": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}, {"label": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}}, {"label": "codec", "name": "codec", "type": "COMBO", "widget": {"name": "codec"}}], "outputs": [], "title": "输出数字人视频结果", "properties": {"Node name for S&R": "SaveVideo", "widget_ue_connectable": {}}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 58, "type": "LoadImage", "pos": [-1292.632568359375, -577.0117797851562], "size": [707.23046875, 937.8099975585938], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "COMBO", "widget": {"name": "image"}}, {"label": "upload", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [72]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.26", "widget_ue_connectable": {}}, "widgets_values": ["a9bd0417a4963ac0148f14598aa58ecdd8f0c79ecff68ef1beaea5771c39ed42.png", "image"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 68, "type": "PreviewImage", "pos": [-976.1905517578125, -1050.965576171875], "size": [370.1436462402344, 395.2823486328125], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 84}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.40", "widget_ue_connectable": {}}, "widgets_values": []}], "links": [[15, 11, 0, 16, 0, "WANTEXTENCODER"], [29, 22, 0, 27, 0, "WANVIDEOMODEL"], [43, 38, 0, 28, 0, "VAE"], [56, 52, 0, 27, 6, "TEACACHEARGS"], [66, 63, 0, 27, 2, "WANVIDIMAGE_EMBEDS"], [70, 59, 0, 65, 0, "CLIP_VISION"], [72, 58, 0, 66, 0, "IMAGE"], [73, 66, 0, 65, 1, "IMAGE"], [75, 66, 1, 63, 8, "INT"], [76, 66, 2, 63, 9, "INT"], [79, 22, 0, 16, 1, "WANVIDEOMODEL"], [80, 55, 0, 27, 4, "FETAARGS"], [81, 38, 0, 63, 0, "WANVAE"], [82, 65, 0, 63, 1, "WANVIDIMAGE_CLIPEMBEDS"], [83, 66, 0, 63, 2, "IMAGE"], [84, 66, 0, 68, 0, "IMAGE"], [87, 70, 0, 66, 2, "INT"], [88, 70, 0, 66, 3, "INT"], [89, 71, 0, 22, 2, "WANVIDLORA"], [93, 77, 0, 75, 0, "AUDIO"], [94, 75, 0, 76, 0, "AUDIO"], [95, 79, 0, 78, 1, "VIDEO"], [96, 28, 0, 79, 0, "IMAGE"], [99, 81, 0, 79, 2, "FLOAT"], [100, 78, 0, 82, 0, "VIDEO"], [101, 75, 0, 83, 0, "AUDIO"], [102, 72, 0, 83, 1, "STRING"], [103, 83, 0, 74, 0, "AUDIO"], [104, 83, 0, 79, 1, "AUDIO"], [105, 83, 0, 78, 0, "AUDIO"], [106, 16, 0, 86, 0, "*"], [107, 86, 0, 27, 1, "WANVIDEOTEXTEMBEDS"], [108, 27, 0, 87, 0, "*"], [109, 87, 0, 28, 1, "LATENT"], [110, 66, 0, 88, 0, "IMAGE"], [111, 89, 0, 90, 0, "STRING"], [112, 88, 0, 90, 1, "STRING"], [113, 90, 0, 16, 2, "STRING"]], "groups": [{"id": 8, "title": "文字驱动生成音频-index-tts", "bounding": [-386.552978515625, -1636.4256591796875, 1211.269775390625, 718.3143310546875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "截取参考音频片段", "bounding": [80.79754638671875, -1549.225341796875, 708.5062866210938, 195.59999084472656], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "TTS (文本转语音)", "bounding": [97.01319885253906, -1295.7109375, 707.7037963867188, 357.6000061035156], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "参考音频输入", "bounding": [-324.3630065917969, -1546.16162109375, 335, 219.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "Index TTS 参考指定音频，文本转成语音wav", "bounding": [70.79756164550781, -1592.825439453125, 743.91943359375, 664.7144775390625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 9, "title": "HeyGem数字人处理节点", "bounding": [1214.5460205078125, -1025.59326171875, 402.8988037109375, 510.0097351074219], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 10, "title": "wan-1", "bounding": [-367.4180603027344, -808.9730224609375, 1211.6044921875, 744.4666137695312], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 11, "title": "wan-2", "bounding": [-368.3126525878906, -16.271692276000977, 1485.418701171875, 996.0604858398438], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 12, "title": "上传一张人像图片", "bounding": [-1425.05810546875, -1125.033447265625, 915.7578735351562, 1527.619384765625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 13, "title": "对图片进行文字描述 优化", "bounding": [-1405.42822265625, -1641.183349609375, 835.4661865234375, 483.4179992675781], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.23.0", "VHS_latentpreview": false, "node_versions": {"ComfyUI-WanVideoWrapper": "5a2383621a05825d0d0437781afcb8552d9590fd", "ComfyUI-VideoHelperSuite": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "ComfyUI-KJNodes": "a5bd3c86c8ed6b83c55c2d0e7a59515b15a0137f", "comfy-core": "0.3.26"}, "ds": {"scale": 0.7972024500000012, "offset": [1867.1967891730085, 1817.813463260133]}}, "version": 0.4}