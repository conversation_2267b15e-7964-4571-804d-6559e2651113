{"id": "integrated-product-fusion-digital-human-workflow", "revision": 0, "last_node_id": 120, "last_link_id": 150, "nodes": [{"id": 1, "type": "LoadImage", "pos": [-2000, -500], "size": [315, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "widget_ue_connectable": {}}, "widgets_values": ["product_image.jpg", "image"], "title": "加载商品图片"}, {"id": 2, "type": "LoadImage", "pos": [-2000, -150], "size": [315, 314], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [2]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "widget_ue_connectable": {}}, "widgets_values": ["person_image.jpg", "image"], "title": "加载人像图片"}, {"id": 3, "type": "ImageConcatMulti", "pos": [-1600, -350], "size": [315, 150], "flags": {}, "order": 2, "mode": 0, "inputs": [{"label": "image_1", "name": "image_1", "type": "IMAGE", "link": 1}, {"label": "image_2", "name": "image_2", "shape": 7, "type": "IMAGE", "link": 2}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": [3]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": [2, "right", true, null], "title": "图片拼接"}, {"id": 4, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [-1200, -350], "size": [315, 330], "flags": {}, "order": 3, "mode": 0, "inputs": [{"label": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 3}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [4]}, {"label": "mask", "name": "mask", "type": "MASK"}, {"label": "original_size", "name": "original_size", "type": "BOX"}, {"label": "width", "name": "width", "type": "INT"}, {"label": "height", "name": "height", "type": "INT"}], "properties": {"Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2", "widget_ue_connectable": {}}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "8", "None", 1024, "#000000"], "title": "图片缩放处理", "color": "rgba(38, 73, 116, 0.7)"}, {"id": 5, "type": "RH_Translator", "pos": [-1600, -650], "size": [400, 200], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "translation", "name": "translation", "type": "STRING", "links": [5]}], "properties": {"Node name for S&R": "RH_Translator", "widget_ue_connectable": {}}, "widgets_values": ["女性手拿着小风扇贴着脸颊，保持女生原有的发型跟五官，小风扇形状保持不变，注意大小比例不要太大，处理好光影，", 1782, "randomize", "force_english"], "title": "产品融合描述翻译"}, {"id": 6, "type": "RH_ComfyFluxKontext", "pos": [-800, -500], "size": [400, 420], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "input_image", "name": "input_image", "shape": 7, "type": "IMAGE", "link": 4}, {"label": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": 5}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [6, 120]}, {"label": "images_urls", "name": "images_urls", "type": "STRING"}], "properties": {"Node name for S&R": "RH_ComfyFluxKontext", "widget_ue_connectable": {}}, "widgets_values": ["", "flux-kontext-pro", "", "3:4", 1, 1671931033, "randomize", true, 3.5, "ai.t8star.cn", "png", false, 2, false], "title": "产品人像融合生成"}, {"id": 7, "type": "SaveImage", "pos": [-300, -500], "size": [315, 270], "flags": {}, "order": 6, "mode": 4, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 6}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["FusedProduct"], "title": "保存融合图片（预览）"}, {"id": 100, "type": "LoadAudio", "pos": [-2000, 800], "size": [315, 136], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "audio", "name": "audio", "type": "COMBO", "widget": {"name": "audio"}}, {"label": "audioUI", "name": "audioUI", "type": "AUDIO_UI", "widget": {"name": "audioUI"}}, {"label": "upload", "name": "upload", "type": "AUDIOUPLOAD", "widget": {"name": "upload"}}], "outputs": [{"label": "AUDIO", "name": "AUDIO", "type": "AUDIO", "links": [100]}], "properties": {"Node name for S&R": "LoadAudio", "cnr_id": "comfy-core", "ver": "0.3.35", "widget_ue_connectable": {}}, "widgets_values": ["reference_audio.mp3", null, null], "title": "加载参考音频"}, {"id": 101, "type": "TextInput_", "pos": [-2000, 1000], "size": [400, 200], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}}], "outputs": [{"label": "STRING", "name": "STRING", "type": "STRING", "links": [101]}], "title": "修改文本：用于数字人说话的内容", "properties": {"Node name for S&R": "TextInput_", "cnr_id": "comfyui-mixlab-nodes", "ver": "b2bb1876def6330fccf1e03cc69d2166cae7bedb", "widget_ue_connectable": {}}, "widgets_values": ["熏风初定月横斜，小院清幽落藕花。\n竹榻微凉消溽暑，蒲扇轻摇送流霞。\n蝉鸣渐歇栖高树，萤火时明透浅纱。\n忽有荷香随露至，一杯清茗话桑麻。"]}, {"id": 102, "type": "AudioCrop", "pos": [-1600, 800], "size": [315, 82], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "audio", "name": "audio", "type": "AUDIO", "link": 100}, {"label": "start_time", "name": "start_time", "type": "STRING", "widget": {"name": "start_time"}}, {"label": "end_time", "name": "end_time", "type": "STRING", "widget": {"name": "end_time"}}], "outputs": [{"label": "AUDIO", "name": "AUDIO", "type": "AUDIO", "links": [102, 103]}], "properties": {"Node name for S&R": "AudioCrop", "cnr_id": "audio-separation-nodes-comfyui", "ver": "1.4.0", "widget_ue_connectable": {}}, "widgets_values": ["0:0", "0:15"], "title": "音频裁剪"}, {"id": 103, "type": "SaveAudio", "pos": [-1200, 800], "size": [315, 112], "flags": {}, "order": 10, "mode": 4, "inputs": [{"label": "audio", "name": "audio", "type": "AUDIO", "link": 102}, {"label": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}, {"label": "audioUI", "name": "audioUI", "type": "AUDIO_UI", "widget": {"name": "audioUI"}}], "outputs": [], "properties": {"Node name for S&R": "SaveAudio", "cnr_id": "comfy-core", "ver": "0.3.35", "widget_ue_connectable": {}}, "widgets_values": ["cropped-audio"], "title": "保存裁剪音频（预览）"}, {"id": 104, "type": "IndexTTSRun", "pos": [-1600, 1000], "size": [315, 342], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "audio", "name": "audio", "type": "AUDIO", "link": 103}, {"label": "text", "name": "text", "type": "STRING", "link": 101}, {"label": "dialogue_audio_s2", "name": "dialogue_audio_s2", "shape": 7, "type": "AUDIO"}, {"label": "version", "name": "version", "type": "COMBO", "widget": {"name": "version"}}, {"label": "top_k", "name": "top_k", "type": "INT", "widget": {"name": "top_k"}}, {"label": "top_p", "name": "top_p", "type": "FLOAT", "widget": {"name": "top_p"}}, {"label": "temperature", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}}, {"label": "num_beams", "name": "num_beams", "type": "INT", "widget": {"name": "num_beams"}}, {"label": "max_mel_tokens", "name": "max_mel_tokens", "type": "INT", "widget": {"name": "max_mel_tokens"}}, {"label": "max_text_tokens_per_sentence", "name": "max_text_tokens_per_sentence", "type": "INT", "widget": {"name": "max_text_tokens_per_sentence"}}, {"label": "sentences_bucket_max_size", "name": "sentences_bucket_max_size", "type": "INT", "widget": {"name": "sentences_bucket_max_size"}}, {"label": "fast_inference", "name": "fast_inference", "type": "BOOLEAN", "widget": {"name": "fast_inference"}}, {"label": "custom_cuda_kernel", "name": "custom_cuda_kernel", "type": "BOOLEAN", "widget": {"name": "custom_cuda_kernel"}}, {"label": "unload_model", "name": "unload_model", "type": "BOOLEAN", "widget": {"name": "unload_model"}}], "outputs": [{"label": "audio", "name": "audio", "type": "AUDIO", "links": [104, 105, 106]}], "properties": {"Node name for S&R": "IndexTTSRun", "widget_ue_connectable": {}}, "widgets_values": ["v1.5", 30, 0.8, 1, 1, 1000, 120, 1, false, false, true], "title": "IndexTTS语音合成"}, {"id": 105, "type": "SaveAudio", "pos": [-1200, 1000], "size": [315, 112], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "audio", "name": "audio", "type": "AUDIO", "link": 104}, {"label": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}, {"label": "audioUI", "name": "audioUI", "type": "AUDIO_UI", "widget": {"name": "audioUI"}}], "outputs": [], "properties": {"Node name for S&R": "SaveAudio", "cnr_id": "comfy-core", "ver": "0.3.35", "widget_ue_connectable": {}}, "widgets_values": ["generated-speech"], "title": "保存生成语音"}, {"id": 110, "type": "INTConstant", "pos": [200, 300], "size": [210, 58], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "value", "name": "value", "type": "INT", "widget": {"name": "value"}}], "outputs": [{"label": "value", "name": "value", "type": "INT", "links": [110, 111]}], "title": "输出分辨率设置", "properties": {"Node name for S&R": "INTConstant", "widget_ue_connectable": {}}, "widgets_values": [720], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 111, "type": "ImageResizeKJ", "pos": [-300, -100], "size": [315, 266], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 120}, {"label": "get_image_size", "name": "get_image_size", "shape": 7, "type": "IMAGE"}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 110}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 111}, {"label": "upscale_method", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}}, {"label": "keep_proportion", "name": "keep_proportion", "type": "BOOLEAN", "widget": {"name": "keep_proportion"}}, {"label": "divisible_by", "name": "divisible_by", "type": "INT", "widget": {"name": "divisible_by"}}, {"label": "crop", "name": "crop", "shape": 7, "type": "COMBO", "widget": {"name": "crop"}}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [112, 113, 114, 115]}, {"label": "width", "name": "width", "type": "INT", "links": [116]}, {"label": "height", "name": "height", "type": "INT", "links": [117]}], "properties": {"Node name for S&R": "ImageResizeKJ", "cnr_id": "comfyui-kjnodes", "ver": "52c2e31a903fec2dd654fb614ea82ba2757d5028", "widget_ue_connectable": {"width": true, "height": true}}, "widgets_values": [720, 720, "lanc<PERSON>s", true, 16, 0], "title": "图像尺寸调整"}, {"id": 112, "type": "TextInput_", "pos": [200, -500], "size": [400, 200], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}}], "outputs": [{"label": "STRING", "name": "STRING", "type": "STRING", "links": [121]}], "properties": {"Node name for S&R": "TextInput_", "widget_ue_connectable": {}}, "widgets_values": ["人物面对镜头，自然的讲话交流，高清4k，人物动作表现非常自然。\n\n"], "title": "数字人描述前缀"}, {"id": 113, "type": "RH_Captioner", "pos": [200, -250], "size": [402.0733642578125, 89.07491302490234], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "ref_image", "name": "ref_image", "type": "IMAGE", "link": 115}, {"label": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}}], "outputs": [{"label": "describe", "name": "describe", "type": "STRING", "links": [122]}], "properties": {"Node name for S&R": "RH_Captioner", "widget_ue_connectable": {}}, "widgets_values": ["请对这张图片进行详细描述。要清晰的描述内容，让画面清晰，自然。"], "title": "图片描述生成"}, {"id": 114, "type": "Text Concatenate (JPS)", "pos": [700, -400], "size": [315, 178], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "text1", "name": "text1", "shape": 7, "type": "STRING", "link": 121}, {"label": "text2", "name": "text2", "shape": 7, "type": "STRING", "link": 122}, {"label": "text3", "name": "text3", "shape": 7, "type": "STRING"}, {"label": "text4", "name": "text4", "shape": 7, "type": "STRING"}, {"label": "text5", "name": "text5", "shape": 7, "type": "STRING"}, {"label": "delimiter", "name": "delimiter", "type": "COMBO", "widget": {"name": "delimiter"}}], "outputs": [{"label": "text", "name": "text", "type": "STRING", "links": [123]}], "properties": {"Node name for S&R": "Text Concatenate (JPS)", "widget_ue_connectable": {}}, "widgets_values": ["none"], "title": "文本拼接"}, {"id": 115, "type": "WanVideoLoraSelect", "pos": [1100, -500], "size": [503.4073486328125, 150], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "prev_lora", "name": "prev_lora", "shape": 7, "type": "WANVIDLORA"}, {"label": "blocks", "name": "blocks", "shape": 7, "type": "SELECTEDBLOCKS"}, {"label": "lora", "name": "lora", "type": "COMBO", "widget": {"name": "lora"}}, {"label": "strength", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}}, {"label": "low_mem_load", "name": "low_mem_load", "shape": 7, "type": "BOOLEAN", "widget": {"name": "low_mem_load"}}, {"label": "merge_loras", "name": "merge_loras", "shape": 7, "type": "BOOLEAN", "widget": {"name": "merge_loras"}}], "outputs": [{"label": "lora", "name": "lora", "type": "WANVIDLORA", "links": [124]}], "properties": {"Node name for S&R": "WanVideoLoraSelect", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "058286fc0f3b0651a2f6b68309df3f06e8332cc0", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors", 1.2000000000000002, false, true], "title": "Wan视频LoRA选择"}, {"id": 116, "type": "LoadWanVideoT5TextEncoder", "pos": [1100, -300], "size": [377.1661376953125, 130], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}}, {"label": "precision", "name": "precision", "type": "COMBO", "widget": {"name": "precision"}}, {"label": "load_device", "name": "load_device", "shape": 7, "type": "COMBO", "widget": {"name": "load_device"}}, {"label": "quantization", "name": "quantization", "shape": 7, "type": "COMBO", "widget": {"name": "quantization"}}], "outputs": [{"label": "wan_t5_model", "name": "wan_t5_model", "type": "WANTEXTENCODER", "slot_index": 0, "links": [125]}], "properties": {"Node name for S&R": "LoadWanVideoT5TextEncoder", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": ["umt5_xxl_fp16.safetensors", "bf16", "offload_device", "disabled"], "title": "加载Wan T5文本编码器", "color": "#332922", "bgcolor": "#593930"}, {"id": 117, "type": "WanVideoModelLoader", "pos": [1100, -100], "size": [477.4410095214844, 294], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS"}, {"label": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS"}, {"label": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA", "link": 124}, {"label": "vram_management_args", "name": "vram_management_args", "shape": 7, "type": "VRAM_MANAGEMENTARGS"}, {"label": "vace_model", "name": "vace_model", "shape": 7, "type": "VACEPATH"}, {"label": "fantasytalking_model", "name": "fantasytalking_model", "shape": 7, "type": "FANTASYTALKINGMODEL"}, {"label": "multitalk_model", "name": "multitalk_model", "shape": 7, "type": "MULTITALKMODEL"}, {"label": "fantasyportrait_model", "name": "fantasyportrait_model", "shape": 7, "type": "FANTASYPORTRAITMODEL"}, {"label": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}}, {"label": "base_precision", "name": "base_precision", "type": "COMBO", "widget": {"name": "base_precision"}}, {"label": "quantization", "name": "quantization", "type": "COMBO", "widget": {"name": "quantization"}}, {"label": "load_device", "name": "load_device", "type": "COMBO", "widget": {"name": "load_device"}}, {"label": "attention_mode", "name": "attention_mode", "shape": 7, "type": "COMBO", "widget": {"name": "attention_mode"}}], "outputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "slot_index": 0, "links": [126, 127]}], "properties": {"Node name for S&R": "WanVideoModelLoader", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": ["wan2114BFusionx_fusionxImage2video.safetensors", "bf16", "disabled", "offload_device", "sdpa"], "title": "Wan视频模型加载器", "color": "#223", "bgcolor": "#335"}, {"id": 118, "type": "WanVideoTextEncode", "pos": [1700, 0], "size": [420.30511474609375, 261.5306701660156], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "t5", "name": "t5", "shape": 7, "type": "WANTEXTENCODER", "link": 125}, {"label": "model_to_offload", "name": "model_to_offload", "shape": 7, "type": "WANVIDEOMODEL", "link": 127}, {"label": "positive_prompt", "name": "positive_prompt", "type": "STRING", "widget": {"name": "positive_prompt"}, "link": 123}, {"label": "negative_prompt", "name": "negative_prompt", "type": "STRING", "widget": {"name": "negative_prompt"}}, {"label": "force_offload", "name": "force_offload", "shape": 7, "type": "BOOLEAN", "widget": {"name": "force_offload"}}, {"label": "use_disk_cache", "name": "use_disk_cache", "shape": 7, "type": "BOOLEAN", "widget": {"name": "use_disk_cache"}}, {"label": "device", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}}], "outputs": [{"label": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "slot_index": 0, "links": [128]}], "properties": {"Node name for S&R": "WanVideoTextEncode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {"positive_prompt": true}}, "widgets_values": ["", "bright tones, overexposed, static, blurred details, subtitles, style, works, paintings, images, static, overall gray, worst quality, low quality, JPEG compression residue, ugly, incomplete, extra fingers, poorly drawn hands, poorly drawn faces, deformed, disfigured, misshapen limbs, fused fingers, still picture, messy background, three legs, many people in the background, walking backwards", true, false, "gpu"], "title": "Wan视频文本编码", "color": "#332922", "bgcolor": "#593930"}, {"id": 119, "type": "easy cleanGpuUsed", "pos": [2200, 0], "size": [210, 26], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 128}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [129]}], "properties": {"Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": [], "title": "清理GPU1"}, {"id": 120, "type": "WanVideoVAELoader", "pos": [1100, 300], "size": [338.8450012207031, 88.59707641601562], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS"}, {"label": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}}, {"label": "precision", "name": "precision", "shape": 7, "type": "COMBO", "widget": {"name": "precision"}}], "outputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "slot_index": 0, "links": [130, 131]}], "properties": {"Node name for S&R": "WanVideoVAELoader", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": ["Wan2_1_VAE_bf16.safetensors", "bf16"], "title": "Wan视频VAE加载器", "color": "#322", "bgcolor": "#533"}, {"id": 121, "type": "CLIPVisionLoader", "pos": [1100, 450], "size": [315, 58], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "clip_name", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}}], "outputs": [{"label": "CLIP_VISION", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [132]}], "properties": {"Node name for S&R": "CLIPVisionLoader", "cnr_id": "comfy-core", "ver": "0.3.26", "widget_ue_connectable": {}}, "widgets_values": ["clip_vision_h.safetensors"], "title": "CLIP视觉模型加载器", "color": "#233", "bgcolor": "#355"}, {"id": 122, "type": "WanVideoClipVisionEncode", "pos": [1500, 450], "size": [327.5999755859375, 262], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "clip_vision", "name": "clip_vision", "type": "CLIP_VISION", "link": 132}, {"label": "image_1", "name": "image_1", "type": "IMAGE", "link": 112}, {"label": "image_2", "name": "image_2", "shape": 7, "type": "IMAGE"}, {"label": "negative_image", "name": "negative_image", "shape": 7, "type": "IMAGE"}, {"label": "strength_1", "name": "strength_1", "type": "FLOAT", "widget": {"name": "strength_1"}}, {"label": "strength_2", "name": "strength_2", "type": "FLOAT", "widget": {"name": "strength_2"}}, {"label": "crop", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}}, {"label": "combine_embeds", "name": "combine_embeds", "type": "COMBO", "widget": {"name": "combine_embeds"}}, {"label": "force_offload", "name": "force_offload", "type": "BOOLEAN", "widget": {"name": "force_offload"}}, {"label": "tiles", "name": "tiles", "shape": 7, "type": "INT", "widget": {"name": "tiles"}}, {"label": "ratio", "name": "ratio", "shape": 7, "type": "FLOAT", "widget": {"name": "ratio"}}], "outputs": [{"label": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_CLIPEMBEDS", "links": [133]}], "properties": {"Node name for S&R": "WanVideoClipVisionEncode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": [1, 1, "center", "average", true, 0, 0.20000000000000004], "title": "Wan视频CLIP视觉编码", "color": "#233", "bgcolor": "#355"}, {"id": 123, "type": "WanVideoEnhanceAVideo", "pos": [1500, 300], "size": [315, 106], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "weight", "name": "weight", "type": "FLOAT", "widget": {"name": "weight"}}, {"label": "start_percent", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}}, {"label": "end_percent", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}}], "outputs": [{"label": "feta_args", "name": "feta_args", "type": "FETAARGS", "links": [134]}], "properties": {"Node name for S&R": "WanVideoEnhanceAVideo", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": [2, 0, 1], "title": "Wan视频增强设置"}, {"id": 124, "type": "WanVideoImageToVideoEncode", "pos": [1900, 450], "size": [352.79998779296875, 390], "flags": {}, "order": 27, "mode": 0, "inputs": [{"label": "vae", "name": "vae", "shape": 7, "type": "WANVAE", "link": 131}, {"label": "clip_embeds", "name": "clip_embeds", "shape": 7, "type": "WANVIDIMAGE_CLIPEMBEDS", "link": 133}, {"label": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": 113}, {"label": "end_image", "name": "end_image", "shape": 7, "type": "IMAGE"}, {"label": "control_embeds", "name": "control_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS"}, {"label": "temporal_mask", "name": "temporal_mask", "shape": 7, "type": "MASK"}, {"label": "extra_latents", "name": "extra_latents", "shape": 7, "type": "LATENT"}, {"label": "add_cond_latents", "name": "add_cond_latents", "shape": 7, "type": "ADD_COND_LATENTS"}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 116}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 117}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}}, {"label": "noise_aug_strength", "name": "noise_aug_strength", "type": "FLOAT", "widget": {"name": "noise_aug_strength"}}, {"label": "start_latent_strength", "name": "start_latent_strength", "type": "FLOAT", "widget": {"name": "start_latent_strength"}}, {"label": "end_latent_strength", "name": "end_latent_strength", "type": "FLOAT", "widget": {"name": "end_latent_strength"}}, {"label": "force_offload", "name": "force_offload", "type": "BOOLEAN", "widget": {"name": "force_offload"}}, {"label": "fun_or_fl2v_model", "name": "fun_or_fl2v_model", "shape": 7, "type": "BOOLEAN", "widget": {"name": "fun_or_fl2v_model"}}, {"label": "tiled_vae", "name": "tiled_vae", "shape": 7, "type": "BOOLEAN", "widget": {"name": "tiled_vae"}}], "outputs": [{"label": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "links": [135]}], "properties": {"Node name for S&R": "WanVideoImageToVideoEncode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {"width": true, "height": true}}, "widgets_values": [832, 480, 81, 0.030000000000000006, 1, 1, true, false, false], "title": "Wan图片转视频编码", "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 125, "type": "WanVideoTeaCache", "pos": [2300, 300], "size": [315, 178], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "rel_l1_thresh", "name": "rel_l1_thresh", "type": "FLOAT", "widget": {"name": "rel_l1_thresh"}}, {"label": "start_step", "name": "start_step", "type": "INT", "widget": {"name": "start_step"}}, {"label": "end_step", "name": "end_step", "type": "INT", "widget": {"name": "end_step"}}, {"label": "cache_device", "name": "cache_device", "type": "COMBO", "widget": {"name": "cache_device"}}, {"label": "use_coefficients", "name": "use_coefficients", "type": "BOOLEAN", "widget": {"name": "use_coefficients"}}, {"label": "mode", "name": "mode", "shape": 7, "type": "COMBO", "widget": {"name": "mode"}}], "outputs": [{"label": "cache_args", "name": "cache_args", "type": "CACHEARGS", "links": [136]}], "properties": {"Node name for S&R": "WanVideoTeaCache", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": [0.25, 1, -1, "main_device", "true", "e"], "title": "Wan视频Tea缓存"}, {"id": 126, "type": "WanVideoSampler", "pos": [2700, 450], "size": [481.87451171875, 690], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 126}, {"label": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "link": 129}, {"label": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "link": 135}, {"label": "samples", "name": "samples", "shape": 7, "type": "LATENT"}, {"label": "feta_args", "name": "feta_args", "shape": 7, "type": "FETAARGS", "link": 134}, {"label": "context_options", "name": "context_options", "shape": 7, "type": "WANVIDCONTEXT"}, {"label": "cache_args", "name": "cache_args", "shape": 7, "type": "CACHEARGS", "link": 136}, {"label": "flowedit_args", "name": "flowedit_args", "shape": 7, "type": "FLOWEDITARGS"}, {"label": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS"}, {"label": "loop_args", "name": "loop_args", "shape": 7, "type": "LOOPARGS"}, {"label": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS"}, {"label": "sigmas", "name": "sigmas", "shape": 7, "type": "SIGMAS"}, {"label": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE"}, {"label": "fantasytalking_embeds", "name": "fantasytalking_embeds", "shape": 7, "type": "FANTASYTALKING_EMBEDS"}, {"label": "uni3c_embeds", "name": "uni3c_embeds", "shape": 7, "type": "UNI3C_EMBEDS"}, {"label": "multitalk_embeds", "name": "multitalk_embeds", "shape": 7, "type": "MULTITALK_EMBEDS"}, {"label": "freeinit_args", "name": "freeinit_args", "shape": 7, "type": "FREEINITARGS"}, {"label": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}}, {"label": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}}, {"label": "shift", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}}, {"label": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}}, {"label": "force_offload", "name": "force_offload", "type": "BOOLEAN", "widget": {"name": "force_offload"}}, {"label": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}}, {"label": "riflex_freq_index", "name": "riflex_freq_index", "type": "INT", "widget": {"name": "riflex_freq_index"}}, {"label": "denoise_strength", "name": "denoise_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "denoise_strength"}}, {"label": "batched_cfg", "name": "batched_cfg", "shape": 7, "type": "BOOLEAN", "widget": {"name": "batched_cfg"}}, {"label": "rope_function", "name": "rope_function", "shape": 7, "type": "COMBO", "widget": {"name": "rope_function"}}, {"label": "start_step", "name": "start_step", "shape": 7, "type": "INT", "widget": {"name": "start_step"}}, {"label": "end_step", "name": "end_step", "shape": 7, "type": "INT", "widget": {"name": "end_step"}}, {"label": "add_noise_to_samples", "name": "add_noise_to_samples", "shape": 7, "type": "BOOLEAN", "widget": {"name": "add_noise_to_samples"}}], "outputs": [{"label": "samples", "name": "samples", "type": "LATENT", "slot_index": 0, "links": [137]}, {"label": "denoised_samples", "name": "denoised_samples", "type": "LATENT"}], "properties": {"Node name for S&R": "WanVideoSampler", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": [4, 1, 5.000000000000001, 1057359483639287, "fixed", true, "lcm", 0, 1, "", "comfy", 0, -1, false], "title": "Wan视频采样器"}, {"id": 127, "type": "easy cleanGpuUsed", "pos": [3300, 600], "size": [210, 26], "flags": {}, "order": 30, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 137}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [138]}], "properties": {"Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": [], "title": "清理GPU2"}, {"id": 128, "type": "WanVideoDecode", "pos": [3300, 700], "size": [315, 198], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "link": 130}, {"label": "samples", "name": "samples", "type": "LATENT", "link": 138}, {"label": "enable_vae_tiling", "name": "enable_vae_tiling", "type": "BOOLEAN", "widget": {"name": "enable_vae_tiling"}}, {"label": "tile_x", "name": "tile_x", "type": "INT", "widget": {"name": "tile_x"}}, {"label": "tile_y", "name": "tile_y", "type": "INT", "widget": {"name": "tile_y"}}, {"label": "tile_stride_x", "name": "tile_stride_x", "type": "INT", "widget": {"name": "tile_stride_x"}}, {"label": "tile_stride_y", "name": "tile_stride_y", "type": "INT", "widget": {"name": "tile_stride_y"}}, {"label": "normalization", "name": "normalization", "shape": 7, "type": "COMBO", "widget": {"name": "normalization"}}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "slot_index": 0, "links": [139]}], "properties": {"Node name for S&R": "WanVideoDecode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": [false, 272, 272, 144, 128, "default"], "title": "Wan视频解码", "color": "#322", "bgcolor": "#533"}, {"id": 129, "type": "CreateVideo", "pos": [3700, 700], "size": [343.671875, 95.91987609863281], "flags": {}, "order": 32, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 139}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO", "link": 105}, {"label": "fps", "name": "fps", "type": "FLOAT", "widget": {"name": "fps"}, "link": 140}], "outputs": [{"label": "VIDEO", "name": "VIDEO", "type": "VIDEO", "links": [141]}], "properties": {"Node name for S&R": "CreateVideo", "widget_ue_connectable": {"fps": true}}, "widgets_values": [30], "title": "创建视频"}, {"id": 130, "type": "PrimitiveFloat", "pos": [3700, 550], "size": [345.10528564453125, 86.67173767089844], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "value", "name": "value", "type": "FLOAT", "widget": {"name": "value"}}], "outputs": [{"label": "FLOAT", "name": "FLOAT", "type": "FLOAT", "links": [140]}], "properties": {"Node name for S&R": "PrimitiveFloat", "widget_ue_connectable": {}}, "widgets_values": [16], "title": "视频帧率设置"}, {"id": 131, "type": "RH_HeyGemNode", "pos": [4100, 700], "size": [357.4807434082031, 77.53885650634766], "flags": {}, "order": 34, "mode": 0, "inputs": [{"label": "AUDIO", "name": "AUDIO", "type": "AUDIO", "link": 106}, {"label": "VIDEO", "name": "VIDEO", "type": "VIDEO", "link": 141}], "outputs": [{"label": "VIDEO", "name": "VIDEO", "type": "VIDEO", "links": [142]}], "properties": {"Node name for S&R": "RH_HeyGemNode", "widget_ue_connectable": {}}, "widgets_values": [], "title": "HeyGem数字人处理"}, {"id": 132, "type": "SaveVideo", "pos": [4500, 500], "size": [912.4972534179688, 400], "flags": {}, "order": 35, "mode": 0, "inputs": [{"label": "video", "name": "video", "type": "VIDEO", "link": 142}, {"label": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}, {"label": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}}, {"label": "codec", "name": "codec", "type": "COMBO", "widget": {"name": "codec"}}], "outputs": [], "title": "输出最终数字人视频", "properties": {"Node name for S&R": "SaveVideo", "widget_ue_connectable": {}}, "widgets_values": ["IntegratedDigitalHuman", "auto", "auto"]}], "links": [[1, 1, 0, 3, 0, "IMAGE"], [2, 2, 0, 3, 1, "IMAGE"], [3, 3, 0, 4, 0, "IMAGE"], [4, 4, 0, 6, 0, "IMAGE"], [5, 5, 0, 6, 1, "STRING"], [6, 6, 0, 7, 0, "IMAGE"], [100, 100, 0, 102, 0, "AUDIO"], [101, 101, 0, 104, 1, "STRING"], [102, 102, 0, 103, 0, "AUDIO"], [103, 102, 0, 104, 0, "AUDIO"], [104, 104, 0, 105, 0, "AUDIO"], [105, 104, 0, 129, 1, "AUDIO"], [106, 104, 0, 131, 0, "AUDIO"], [110, 110, 0, 111, 2, "INT"], [111, 110, 0, 111, 3, "INT"], [112, 111, 0, 122, 1, "IMAGE"], [113, 111, 0, 124, 2, "IMAGE"], [114, 111, 0, 7, 0, "IMAGE"], [115, 111, 0, 113, 0, "IMAGE"], [116, 111, 1, 124, 8, "INT"], [117, 111, 2, 124, 9, "INT"], [120, 6, 0, 111, 0, "IMAGE"], [121, 112, 0, 114, 0, "STRING"], [122, 113, 0, 114, 1, "STRING"], [123, 114, 0, 118, 2, "STRING"], [124, 115, 0, 117, 2, "WANVIDLORA"], [125, 116, 0, 118, 0, "WANTEXTENCODER"], [126, 117, 0, 126, 0, "WANVIDEOMODEL"], [127, 117, 0, 118, 1, "WANVIDEOMODEL"], [128, 118, 0, 119, 0, "*"], [129, 119, 0, 126, 1, "WANVIDEOTEXTEMBEDS"], [130, 120, 0, 128, 0, "WANVAE"], [131, 120, 0, 124, 0, "WANVAE"], [132, 121, 0, 122, 0, "CLIP_VISION"], [133, 122, 0, 124, 1, "WANVIDIMAGE_CLIPEMBEDS"], [134, 123, 0, 126, 4, "FETAARGS"], [135, 124, 0, 126, 2, "WANVIDIMAGE_EMBEDS"], [136, 125, 0, 126, 6, "CACHEARGS"], [137, 126, 0, 127, 0, "*"], [138, 127, 0, 128, 1, "LATENT"], [139, 128, 0, 129, 0, "IMAGE"], [140, 130, 0, 129, 2, "FLOAT"], [141, 129, 0, 131, 1, "VIDEO"], [142, 131, 0, 132, 0, "VIDEO"]], "groups": [{"id": 1, "title": "第一阶段：产品人像融合", "bounding": [-2100, -750, 1000, 500], "color": "#8A2BE2", "font_size": 24, "flags": {}}, {"id": 2, "title": "第二阶段：音频处理与TTS", "bounding": [-2100, 700, 1000, 700], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "第三阶段：图像处理与描述", "bounding": [-400, -600, 1200, 800], "color": "#228B22", "font_size": 24, "flags": {}}, {"id": 4, "title": "第四阶段：Wan视频模型处理", "bounding": [1000, -600, 1400, 1200], "color": "#FF6347", "font_size": 24, "flags": {}}, {"id": 5, "title": "第五阶段：数字人生成与输出", "bounding": [2600, 400, 2000, 800], "color": "#FF1493", "font_size": 24, "flags": {}}], "config": {}, "extra": {"VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.23.0", "VHS_latentpreview": false, "node_versions": {"ComfyUI-WanVideoWrapper": "5a2383621a05825d0d0437781afcb8552d9590fd", "ComfyUI-VideoHelperSuite": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "ComfyUI-KJNodes": "a5bd3c86c8ed6b83c55c2d0e7a59515b15a0137f", "comfy-core": "0.3.26"}, "ds": {"scale": 0.5, "offset": [1000, 500]}}, "version": 0.4}