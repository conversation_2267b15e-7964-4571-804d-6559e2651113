{"1": {"inputs": {"file": "523f0b9e5bbd80fb0f9fe79b93646ed4b75300cc861267eed5d965db5e186197.mp4", "video-preview": ""}, "class_type": "LoadVideo", "_meta": {"title": "Load Video"}}, "2": {"inputs": {"filename_prefix": "video/ComfyUI", "format": "auto", "codec": "auto", "video": ["3", 0]}, "class_type": "SaveVideo", "_meta": {"title": "Save Video"}}, "3": {"inputs": {"AUDIO": ["14", 0], "VIDEO": ["1", 0]}, "class_type": "RH_HeyGemNode", "_meta": {"title": "HeyGem Video Synthesis"}}, "4": {"inputs": {"audio": "69f836b46299093a0d6d2dbd2d7a96027bdb1750d72ca0debedf6897de2cd072.mp3", "audioUI": ""}, "class_type": "LoadAudio", "_meta": {"title": "Load Audio"}}, "7": {"inputs": {"audio": "46ff7a71d4b4c726d53d56322f35b86ed55ff6cd08a3a65264b0b283d192ea7a.wav", "audioUI": ""}, "class_type": "LoadAudio", "_meta": {"title": "Load Audio"}}, "9": {"inputs": {"filename_prefix": "audio/ComfyUI", "audioUI": "", "audio": ["17", 0]}, "class_type": "SaveAudio", "_meta": {"title": "Save Audio (FLAC)"}}, "11": {"inputs": {"purge_cache": true, "purge_models": true, "anything": ["17", 0]}, "class_type": "LayerUtility: PurgeVRAM", "_meta": {"title": "LayerUtility: Purge VRAM"}}, "12": {"inputs": {"prompt": "大家好，今天贞贞休息，我来给大家介绍，RunningHub上最新在线项目，Heygem，一个超快的语音项目，强烈推荐，1分钟的语音只需要一会就能跑出来哦！还可以搭配IndexTTS，效果更佳！"}, "class_type": "CR Prompt Text", "_meta": {"title": "⚙️ CR Prompt Text"}}, "14": {"inputs": {"select": 1, "sel_mode": false, "input1": ["4", 0], "input2": ["17", 0]}, "class_type": "ImpactSwitch", "_meta": {"title": "Switch (Any)"}}, "17": {"inputs": {"text": ["12", 0], "model_version": "IndexTTS-1.5", "language": "auto", "speed": 1, "seed": 1242131133, "temperature": 1, "top_p": 0.8, "top_k": 30, "repetition_penalty": 10, "length_penalty": 0, "num_beams": 3, "max_mel_tokens": 600, "sentence_split": "auto", "reference_audio": ["7", 0]}, "class_type": "IndexTTSNode", "_meta": {"title": "Index TTS"}}}