## PHP 示例

```php
<?php
function process($token, $request, $audioFile)
 {
/**
* 读取音频文件
*/
$audioContent = file_get_contents($audioFile);
if ($audioContent == FALSE) {
print "The audio file is not exist!\n";
return;
}

$curl = curl_init();
curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($curl, CURLOPT_TIMEOUT, 120);

/**
* 设置HTTP请求行
*/
curl_setopt($curl, CURLOPT_URL, $request);
curl_setopt($curl, CURLOPT_POST,TRUE);

/**
* 设置HTTP请求头部
*/
$contentType = "application/octet-stream";
$contentLength = strlen($audioContent);
$headers = array(
// "X-NLS-Token:" . $token,
"Content-type:" . $contentType,
"Content-Length:" . strval($contentLength)
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

/**
* 设置HTTP请求数据
*/
curl_setopt($curl, CURLOPT_POSTFIELDS, $audioContent);
curl_setopt($curl, CURLOPT_NOBODY, FALSE);

/**
* 发送HTTP请求
*/
$returnData = curl_exec($curl);
curl_close($curl);
if ($returnData == FALSE) {
print "curl_exec failed!\n";
return;
}
print $returnData . "\n";
$resultArr = json_decode($returnData, true);
$status = $resultArr["status"];
if ($status == 20000000) {
$result = $resultArr["result"];
print "The audio file recognized result: " . $result . "\n";
}
else {
print "The audio file recognized failed.\n";
}
}
$appkey = "您的appkey";
$token = "您的token";
$url = "https://nls-gateway-cn-shanghai.aliyuncs.com/stream/v1/FlashRecognizer";
//$audioFile = "/path/to/nls-sample-16k.wav";
$audioFile = "./nls-sample-16k.wav";
$format = "wav";
$sampleRate = 16000;

/**
* 设置RESTful请求参数
*/
$request = $url;
$request = $request . "?appkey=" . $appkey;
$request = $request . "&token=" . $token;
$request = $request . "&format=" . $format;
$request = $request . "&sample_rate=" . strval($sampleRate);
print "Request: " . $request . "\n";
process($token, $request, $audioFile);
?>
```