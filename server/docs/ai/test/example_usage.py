#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LiblibAI F.1 Kontext 图生图 使用示例

展示如何使用 liblib_f1_kontext_img2img.py 进行图像生成
"""

import sys
from liblib_f1_kontext_img2img import LiblibF1KontextImg2Img
from config import get_config, validate_config


def create_client():
    """创建客户端，包含配置验证"""
    config = get_config()

    # 验证配置
    errors = validate_config(config)
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"   - {error}")
        print("请检查 config.py 中的API密钥配置")
        return None, None

    # 创建客户端
    client = LiblibF1KontextImg2Img(config["ACCESS_KEY"], config["SECRET_KEY"])
    return client, config


def example_basic_img2img():
    """基础图生图示例"""
    # 创建客户端和获取配置
    client, config = create_client()
    if client is None:
        return

    # 基础参数
    prompt = "图1中的女生戴上图2中的饰品，保持女生原有的发型跟五官，处理好光影，女生要微笑"
    image_list = [
        "https://outin-61a2226203a811f083c300163e18773e.oss-cn-shenzhen.aliyuncs.com/image/default/63F2A12C00E64B2AA3B6E0437281C74B-6-2.png",
        "https://outin-61a2226203a811f083c300163e18773e.oss-cn-shenzhen.aliyuncs.com/image/default/AC012D175511455F868EB08CA7A2487E-6-2.png",  # 替换为您的参考图片URL
    ]

    try:
        print("=== 基础图生图示例 ===")
        print(f"提示词: {prompt}")

        # 发起生图任务
        result = client.generate_img2img(
            prompt=prompt,
            image_list=image_list,
            model=config["DEFAULT_PARAMS"]["model"],
            aspect_ratio=config["DEFAULT_PARAMS"]["aspect_ratio"],
            img_count=config["DEFAULT_PARAMS"]["img_count"],
            guidance_scale=config["DEFAULT_PARAMS"]["guidance_scale"],
        )

        if result.get("code") == 0:
            generate_uuid = result["data"]["generateUuid"]
            print(f"✓ 任务提交成功，UUID: {generate_uuid}")

            # 等待完成
            final_result = client.wait_for_completion(
                generate_uuid, max_wait_time=config["WAIT_CONFIG"]["max_wait_time"]
            )

            # 输出结果
            data = final_result["data"]
            print(f"✓ 生图完成！")
            print(f"  消耗积分: {data.get('pointsCost', 0)}")
            print(f"  剩余积分: {data.get('accountBalance', 0)}")

            images = data.get("images", [])
            for i, img in enumerate(images):
                print(f"  生成图片 {i+1}: {img['imageUrl']}")

        else:
            print(f"✗ 请求失败: {result}")

    except Exception as e:
        print(f"✗ 错误: {e}")


def example_multi_image_reference():
    """多图参考示例"""
    # 创建客户端和获取配置
    client, config = create_client()
    if client is None:
        return

    # 多图参考参数
    prompt = "结合这些图片的元素，创作一个科幻风格的城市场景"
    image_list = [
        "https://example.com/reference1.jpg",  # 第一张参考图
        "https://example.com/reference2.jpg",  # 第二张参考图
        "https://example.com/reference3.jpg",  # 第三张参考图
    ]

    try:
        print("\n=== 多图参考示例 ===")
        print(f"提示词: {prompt}")
        print(f"参考图数量: {len(image_list)}")

        result = client.generate_img2img(
            prompt=prompt,
            image_list=image_list,
            model="max",  # max模型支持多图参考
            aspect_ratio="16:9",  # 横版构图
            img_count=2,  # 生成2张图
            guidance_scale=5.0,  # 更强的提示词引导
        )

        if result.get("code") == 0:
            generate_uuid = result["data"]["generateUuid"]
            print(f"✓ 多图任务提交成功，UUID: {generate_uuid}")

            final_result = client.wait_for_completion(generate_uuid)
            data = final_result["data"]

            print(f"✓ 多图生成完成！")
            print(f"  消耗积分: {data.get('pointsCost', 0)}")

            images = data.get("images", [])
            for i, img in enumerate(images):
                print(f"  生成图片 {i+1}: {img['imageUrl']}")

    except Exception as e:
        print(f"✗ 多图参考错误: {e}")


def example_style_transfer():
    """风格迁移示例"""
    # 创建客户端和获取配置
    client, config = create_client()
    if client is None:
        return

    # 风格迁移参数
    style_prompts = [
        "转换为水彩画风格，柔和的色彩和模糊的边缘",
        "转换为油画风格，厚重的笔触和丰富的色彩",
        "转换为素描风格，黑白线条艺术",
        "转换为赛博朋克风格，霓虹色彩和未来科技感",
    ]

    image_url = "https://example.com/original-photo.jpg"

    try:
        print("\n=== 风格迁移示例 ===")

        for i, prompt in enumerate(style_prompts, 1):
            print(f"\n--- 风格 {i}: {prompt} ---")

            result = client.generate_img2img(
                prompt=prompt,
                image_list=[image_url],
                model="max",
                aspect_ratio="3:4",  # 竖版构图
                img_count=1,
                guidance_scale=4.0,
            )

            if result.get("code") == 0:
                generate_uuid = result["data"]["generateUuid"]
                print(f"✓ 风格迁移任务 {i} 提交成功")

                # 注意：实际使用时可能需要分别等待每个任务
                # 这里为了示例简化，只提交任务
                print(f"  任务UUID: {generate_uuid}")

    except Exception as e:
        print(f"✗ 风格迁移错误: {e}")


def example_batch_processing():
    """批量处理示例"""
    # 创建客户端和获取配置
    client, config = create_client()
    if client is None:
        return

    # 批量任务配置
    tasks = [
        {
            "prompt": "转换为动漫风格",
            "image": "https://example.com/photo1.jpg",
            "aspect_ratio": "1:1",
        },
        {
            "prompt": "转换为素描风格",
            "image": "https://example.com/photo2.jpg",
            "aspect_ratio": "3:4",
        },
        {
            "prompt": "转换为油画风格",
            "image": "https://example.com/photo3.jpg",
            "aspect_ratio": "16:9",
        },
    ]

    try:
        print("\n=== 批量处理示例 ===")
        task_uuids = []

        # 提交所有任务
        for i, task in enumerate(tasks, 1):
            print(f"提交任务 {i}: {task['prompt']}")

            result = client.generate_img2img(
                prompt=task["prompt"],
                image_list=[task["image"]],
                model="max",
                aspect_ratio=task["aspect_ratio"],
                img_count=1,
                guidance_scale=3.5,
            )

            if result.get("code") == 0:
                uuid = result["data"]["generateUuid"]
                task_uuids.append((i, uuid))
                print(f"✓ 任务 {i} 提交成功: {uuid}")
            else:
                print(f"✗ 任务 {i} 提交失败")

        # 等待所有任务完成
        print(f"\n等待 {len(task_uuids)} 个任务完成...")
        for task_id, uuid in task_uuids:
            try:
                print(f"等待任务 {task_id} 完成...")
                result = client.wait_for_completion(
                    uuid, max_wait_time=config["WAIT_CONFIG"]["max_wait_time"]
                )

                data = result["data"]
                images = data.get("images", [])
                if images:
                    print(f"✓ 任务 {task_id} 完成: {images[0]['imageUrl']}")
                else:
                    print(f"✗ 任务 {task_id} 未生成图片")

            except Exception as e:
                print(f"✗ 任务 {task_id} 失败: {e}")

    except Exception as e:
        print(f"✗ 批量处理错误: {e}")


if __name__ == "__main__":
    print("LiblibAI F.1 Kontext 图生图 使用示例")
    print("=" * 50)

    # 检查配置
    config = get_config()
    errors = validate_config(config)

    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"   - {error}")
        print("\n请先在 config.py 中配置正确的API密钥:")
        print("1. 访问 https://www.liblib.art/apis 获取API密钥")
        print("2. 编辑 config.py 文件，填入您的 ACCESS_KEY 和 SECRET_KEY")
        print("3. 重新运行此脚本")
        sys.exit(1)

    print("✅ 配置验证通过，开始运行示例...")
    print(f"使用模型: {config['DEFAULT_PARAMS']['model']}")
    print(f"默认宽高比: {config['DEFAULT_PARAMS']['aspect_ratio']}")
    print()

    # 运行示例
    example_basic_img2img()

    # 取消注释以下行来运行其他示例
    # example_multi_image_reference()
    # example_style_transfer()
    # example_batch_processing()
