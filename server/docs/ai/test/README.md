# LiblibAI F.1 Kontext 图生图 Python 实现

基于 LiblibAI x 星流 图像大模型 API 的 F.1 Kontext 图生图功能的 Python 实现，支持指令编辑和多图参考。

## 文件说明

- `liblib_f1_kontext_img2img.py` - 核心实现类，包含完整的 API 调用逻辑
- `example_usage.py` - 使用示例，展示各种应用场景
- `config.py` - 配置文件，包含 API 密钥和参数设置
- `README.md` - 本说明文档

## 功能特性

### 🎨 F.1 Kontext 图生图

- **指令编辑**: 通过文字描述对图片进行编辑和变换
- **多图参考**: 支持 1-4 张参考图片的组合处理
- **风格迁移**: 将图片转换为不同的艺术风格
- **高质量输出**: 基于 F.1 Kontext 先进算法，输出高质量图像

### 🔧 技术实现

- **签名认证**: 完整的 HMAC-SHA1 签名实现
- **异步等待**: 自动等待任务完成并返回结果
- **错误处理**: 完善的异常处理和状态监控
- **参数验证**: 全面的输入参数验证

## 快速开始

### 1. 安装依赖

```bash
pip install requests
```

### 2. 配置 API 密钥

编辑 `config.py` 文件，填入您的 API 密钥：

```python
LIBLIB_CONFIG = {
    "ACCESS_KEY": "your_actual_access_key",    # 替换为您的AccessKey
    "SECRET_KEY": "your_actual_secret_key",    # 替换为您的SecretKey
    # ... 其他配置
}
```

> 💡 **获取 API 密钥**:
>
> 1. 访问 [LiblibAI API 官网](https://www.liblib.art/apis)
> 2. 注册登录后领取 500 试用积分
> 3. 在个人中心获取 AccessKey 和 SecretKey

### 3. 基础使用

```python
from liblib_f1_kontext_img2img import LiblibF1KontextImg2Img

# 创建客户端
client = LiblibF1KontextImg2Img("your_access_key", "your_secret_key")

# 发起图生图任务
result = client.generate_img2img(
    prompt="将这张图片转换为吉卜力动画风格",
    image_list=["https://example.com/your-image.jpg"],
    model="max",
    aspect_ratio="1:1",
    img_count=1
)

# 等待完成并获取结果
if result.get("code") == 0:
    generate_uuid = result["data"]["generateUuid"]
    final_result = client.wait_for_completion(generate_uuid)

    # 获取生成的图片URL
    images = final_result["data"]["images"]
    for img in images:
        print(f"生成图片: {img['imageUrl']}")
```

## 使用示例

### 🖼️ 单图风格转换

```python
# 将照片转换为水彩画风格
result = client.generate_img2img(
    prompt="转换为水彩画风格，柔和的色彩和模糊的边缘",
    image_list=["https://example.com/photo.jpg"],
    model="max",
    aspect_ratio="3:4",
    guidance_scale=4.0
)
```

### 🎭 多图参考创作

```python
# 结合多张参考图创作新作品
result = client.generate_img2img(
    prompt="结合这些图片元素，创作一个未来科幻城市场景",
    image_list=[
        "https://example.com/cityscape.jpg",
        "https://example.com/neon-lights.jpg",
        "https://example.com/architecture.jpg"
    ],
    model="max",  # max模型支持多图参考
    aspect_ratio="16:9",
    img_count=2
)
```

### 🎨 艺术风格迁移

```python
# 转换为不同艺术风格
styles = [
    "转换为梵高星空风格的油画",
    "转换为日本浮世绘风格",
    "转换为现代抽象艺术风格",
    "转换为中国水墨画风格"
]

for style in styles:
    result = client.generate_img2img(
        prompt=style,
        image_list=["https://example.com/original.jpg"],
        model="max",
        aspect_ratio="1:1"
    )
```

## API 参数说明

### generate_img2img 方法参数

| 参数             | 类型      | 必填 | 说明                         | 默认值 |
| ---------------- | --------- | ---- | ---------------------------- | ------ |
| `prompt`         | str       | ✅   | 正向提示词，不超过 2000 字符 | -      |
| `image_list`     | List[str] | ✅   | 参考图 URL 列表，1-4 张      | -      |
| `model`          | str       | ❌   | 模型类型：pro/max            | "max"  |
| `aspect_ratio`   | str       | ❌   | 宽高比                       | "1:1"  |
| `img_count`      | int       | ❌   | 生图数量，1-4                | 1      |
| `guidance_scale` | float     | ❌   | 提示词引导系数，1.0-20.0     | 3.5    |

### 支持的宽高比

| 比例   | 说明   | 适用场景             |
| ------ | ------ | -------------------- |
| `1:1`  | 正方形 | 头像、Logo           |
| `3:4`  | 竖版   | 海报、书籍封面       |
| `4:3`  | 横版   | 传统照片             |
| `16:9` | 宽屏   | 横屏壁纸、视频缩略图 |
| `9:16` | 竖屏   | 手机壁纸、短视频     |

### 模型对比

| 模型  | 积分消耗 | 多图参考 | 特点                     |
| ----- | -------- | -------- | ------------------------ |
| `pro` | 29 积分  | ❌       | 高性价比，适合单图处理   |
| `max` | 58 积分  | ✅       | 高质量，支持复杂多图参考 |

## 任务状态说明

### 生图状态 (generateStatus)

| 状态码 | 状态     | 说明                    |
| ------ | -------- | ----------------------- |
| 1      | 等待执行 | 任务已提交，等待处理    |
| 2      | 执行中   | 正在生成图片            |
| 3      | 已生图   | 图片生成完成，等待审核  |
| 4      | 审核中   | 内容安全审核中          |
| 5      | 成功     | ✅ 任务完成，可获取图片 |
| 6      | 失败     | ❌ 生图失败             |
| 7      | 超时     | ⏰ 任务超时（30 分钟）  |

### 审核状态 (auditStatus)

| 状态码 | 状态     | 说明                    |
| ------ | -------- | ----------------------- |
| 1      | 待审核   | 等待内容审核            |
| 2      | 审核中   | 正在进行内容审核        |
| 3      | 审核通过 | ✅ 内容安全，可正常使用 |
| 4      | 审核拦截 | ❌ 内容不合规，被拦截   |
| 5      | 审核失败 | ❌ 审核系统错误         |

## 最佳实践

### 🎯 提示词优化

1. **明确具体**: 使用具体的描述而非抽象概念
2. **风格关键词**: 包含明确的风格指示词
3. **质量提升**: 添加"高质量"、"精细"等质量关键词
4. **避免冲突**: 避免相互矛盾的描述

**示例**:

```python
# ✅ 好的提示词
"将这张人物照片转换为吉卜力工作室动画风格，柔和的色彩，手绘质感，温暖的光线，高质量"

# ❌ 不好的提示词
"动画风格"
```

### 🖼️ 参考图选择

1. **图片质量**: 使用高分辨率、清晰的参考图
2. **内容相关**: 确保参考图与期望结果相关
3. **多图平衡**: 多图参考时注意风格和内容的平衡
4. **公网访问**: 确保图片 URL 可以公网访问

### ⚙️ 参数调优

1. **guidance_scale**:

   - 低值(1.0-3.0): 更自由的创作
   - 高值(5.0-10.0): 更严格按照提示词

2. **aspect_ratio**: 根据用途选择合适比例
3. **img_count**: 建议单次 1-2 张，提高成功率

### 🔄 错误处理

```python
import time

def robust_generation(client, prompt, image_list, max_retries=3):
    """带重试机制的生图函数"""

    for attempt in range(max_retries):
        try:
            result = client.generate_img2img(
                prompt=prompt,
                image_list=image_list,
                model="max"
            )

            if result.get("code") == 0:
                generate_uuid = result["data"]["generateUuid"]
                return client.wait_for_completion(generate_uuid)

        except Exception as e:
            print(f"尝试 {attempt + 1} 失败: {e}")
            if attempt < max_retries - 1:
                time.sleep(5)  # 等待5秒后重试
                continue
            raise

    raise Exception("所有重试均失败")
```

## 注意事项

### ⚠️ 重要提醒

1. **API 密钥安全**: 不要在代码中硬编码密钥，不要提交到版本控制系统
2. **积分消耗**: 每次生图都会消耗积分，请合理使用
3. **并发限制**: 默认并发数为 5，请勿超过限制
4. **QPS 限制**: 提交任务的 QPS 为 1 秒 1 次
5. **图片时效**: 生成的图片 URL 有效期为 7 天

### 🛡️ 内容规范

- 遵守相关法律法规和平台规范
- 不生成违法、色情、暴力等不当内容
- 尊重知识产权，避免侵权内容

### 💰 费用说明

- F.1 Kontext Pro: 29 积分/张 (约 0.29 元)
- F.1 Kontext Max: 58 积分/张 (约 0.58 元)
- 新用户可获得 500 试用积分
- 任务失败或超时会退还积分

## 技术支持

如有问题，请联系：

- 商务电话: 17521599324
- 官方网站: [https://www.liblib.art/apis](https://www.liblib.art/apis)
- API 文档: [LiblibAI x 星流 图像大模型 API 使用说明](../LiblibAI/LiblibAI%20x%20星流%20图像大模型API%20使用说明.md)

## 更新日志

- **2025-01-06**: 初始版本发布
  - 实现 F.1 Kontext 图生图 API
  - 支持单图和多图参考
  - 完整的示例和文档

---

_基于 LiblibAI x 星流 图像大模型 API 实现 | 开发者: Assistant_
