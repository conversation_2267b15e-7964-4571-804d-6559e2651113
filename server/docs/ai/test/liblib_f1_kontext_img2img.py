#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LiblibAI F.1 Kontext 图生图 API 实现

基于 LiblibAI x 星流 图像大模型API 使用说明.md 中的接口文档实现
支持指令编辑和多图参考功能

Author: Assistant
Date: 2025-01-06
"""

import hashlib
import hmac
import base64
import time
import uuid
import json
import requests
from typing import List, Optional, Dict, Any


class LiblibF1KontextImg2Img:
    """LiblibAI F.1 Kontext 图生图客户端"""

    def __init__(self, access_key: str, secret_key: str):
        """
        初始化客户端

        Args:
            access_key: API访问凭证
            secret_key: API访问密钥
        """
        self.access_key = access_key
        self.secret_key = secret_key
        self.base_url = "https://openapi.liblibai.cloud"
        self.template_uuid = (
            "1c0a9712b3d84e1b8a9f49514a46d88c"  # F.1 Kontext 图生图模板UUID
        )

    def _generate_signature(
        self, uri: str, timestamp: str, signature_nonce: str
    ) -> str:
        """
        生成请求签名

        Args:
            uri: 请求API接口的URI地址
            timestamp: 毫秒时间戳
            signature_nonce: 随机字符串

        Returns:
            生成的签名字符串
        """
        # 拼接请求数据：URI + "&" + 时间戳 + "&" + 随机字符串
        content = f"{uri}&{timestamp}&{signature_nonce}"

        # 使用HMAC-SHA1加密
        digest = hmac.new(
            self.secret_key.encode(), content.encode(), hashlib.sha1
        ).digest()

        # 生成URL安全的Base64签名（移除填充的等号）
        signature = base64.urlsafe_b64encode(digest).rstrip(b"=").decode()
        return signature

    def _build_request_url(self, uri: str) -> tuple[str, Dict[str, str]]:
        """
        构建请求URL和参数

        Args:
            uri: 请求API接口的URI地址

        Returns:
            完整的请求URL和查询参数
        """
        # 生成时间戳和随机字符串
        timestamp = str(int(time.time() * 1000))
        signature_nonce = str(uuid.uuid4())

        # 生成签名
        signature = self._generate_signature(uri, timestamp, signature_nonce)

        # 构建查询参数
        params = {
            "AccessKey": self.access_key,
            "Signature": signature,
            "Timestamp": timestamp,
            "SignatureNonce": signature_nonce,
        }

        return f"{self.base_url}{uri}", params

    def generate_img2img(
        self,
        prompt: str,
        image_list: List[str],
        model: str = "max",
        aspect_ratio: str = "1:1",
        img_count: int = 1,
        guidance_scale: float = 3.5,
    ) -> Dict[str, Any]:
        """
        F.1 Kontext 图生图（指令编辑&多图参考）

        Args:
            prompt: 正向提示词，不超过2000字符
            image_list: 参考图列表，1-4张图片的URL
            model: 模型类型，pro（暂不支持多图参考）或 max（默认）
            aspect_ratio: 图片宽高比，支持: 1:1(默认), 2:3, 3:2, 3:4, 4:3, 9:16, 16:9, 9:21, 21:9
            img_count: 单次生图张数，1-4
            guidance_scale: 提示词引导系数，1.0-20.0，默认3.5

        Returns:
            包含generateUuid的响应数据
        """
        # 参数验证
        if not prompt or len(prompt) > 2000:
            raise ValueError("prompt不能为空且不能超过2000字符")

        if not image_list or len(image_list) < 1 or len(image_list) > 4:
            raise ValueError("image_list必须包含1-4张图片URL")

        if model not in ["pro", "max"]:
            raise ValueError("model必须是 'pro' 或 'max'")

        if aspect_ratio not in [
            "1:1",
            "2:3",
            "3:2",
            "3:4",
            "4:3",
            "9:16",
            "16:9",
            "9:21",
            "21:9",
        ]:
            raise ValueError("aspect_ratio不在支持范围内")

        if img_count < 1 or img_count > 4:
            raise ValueError("img_count必须在1-4之间")

        if guidance_scale < 1.0 or guidance_scale > 20.0:
            raise ValueError("guidance_scale必须在1.0-20.0之间")

        # 构建请求
        uri = "/api/generate/kontext/img2img"
        url, params = self._build_request_url(uri)

        # 请求体
        request_body = {
            "templateUuid": self.template_uuid,
            "generateParams": {
                "model": model,
                "prompt": prompt,
                "aspectRatio": aspect_ratio,
                "guidance_scale": guidance_scale,
                "imgCount": img_count,
                "image_list": image_list,
            },
        }

        # 发送请求
        headers = {"Content-Type": "application/json"}

        try:
            response = requests.post(
                url, params=params, headers=headers, json=request_body, timeout=30
            )
            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            raise Exception(f"请求失败: {e}")

    def check_generation_status(self, generate_uuid: str) -> Dict[str, Any]:
        """
        查询生图任务结果

        Args:
            generate_uuid: 生图任务UUID

        Returns:
            包含生图状态和结果的响应数据
        """
        if not generate_uuid:
            raise ValueError("generate_uuid不能为空")

        # 构建请求
        uri = "/api/generate/status"
        url, params = self._build_request_url(uri)

        # 请求体
        request_body = {"generateUuid": generate_uuid}

        # 发送请求
        headers = {"Content-Type": "application/json"}

        try:
            response = requests.post(
                url, params=params, headers=headers, json=request_body, timeout=30
            )
            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            raise Exception(f"查询状态失败: {e}")

    def wait_for_completion(
        self, generate_uuid: str, max_wait_time: int = 300, check_interval: int = 5
    ) -> Dict[str, Any]:
        """
        等待生图任务完成

        Args:
            generate_uuid: 生图任务UUID
            max_wait_time: 最大等待时间（秒），默认300秒
            check_interval: 检查间隔（秒），默认5秒

        Returns:
            完成后的响应数据
        """
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                result = self.check_generation_status(generate_uuid)

                if result.get("code") == 0:
                    data = result.get("data", {})
                    status = data.get("generateStatus")

                    # 状态说明：
                    # 1: 等待执行, 2: 执行中, 3: 已生图, 4: 审核中
                    # 5: 成功, 6: 失败, 7: 超时

                    if status == 5:  # 成功
                        return result
                    elif status in [6, 7]:  # 失败或超时
                        raise Exception(
                            f"生图任务失败，状态: {status}, 信息: {data.get('generateMsg', '')}"
                        )

                    print(f"任务状态: {status}, 等待中...")

                time.sleep(check_interval)

            except Exception as e:
                if "生图任务失败" in str(e):
                    raise
                print(f"检查状态时出错: {e}")
                time.sleep(check_interval)

        raise Exception(f"等待超时（{max_wait_time}秒）")


def main():
    """示例用法"""
    # 配置API密钥（请替换为您的实际密钥）
    ACCESS_KEY = "your_access_key_here"
    SECRET_KEY = "your_secret_key_here"

    # 创建客户端
    client = LiblibF1KontextImg2Img(ACCESS_KEY, SECRET_KEY)

    try:
        # 示例参数
        prompt = "Turn this image into a Ghibli-style, a traditional Japanese anime aesthetics."
        image_list = [
            "https://liblibai-online.liblib.cloud/img/081e9f07d9bd4c2ba090efde163518f9/3c65a38d7df2589c4bf834740385192128cf035c7c779ae2bbbc354bf0efcfcb.png"
        ]

        print("开始生成图片...")
        print(f"提示词: {prompt}")
        print(f"参考图数量: {len(image_list)}")

        # 发起生图任务
        result = client.generate_img2img(
            prompt=prompt,
            image_list=image_list,
            model="max",
            aspect_ratio="2:3",
            img_count=1,
            guidance_scale=3.5,
        )

        if result.get("code") == 0:
            generate_uuid = result["data"]["generateUuid"]
            print(f"任务已提交，UUID: {generate_uuid}")

            # 等待完成
            print("等待生图完成...")
            final_result = client.wait_for_completion(generate_uuid)

            # 处理结果
            data = final_result["data"]
            print(f"生图完成！")
            print(f"消耗积分: {data.get('pointsCost', 0)}")
            print(f"剩余积分: {data.get('accountBalance', 0)}")

            images = data.get("images", [])
            if images:
                print(f"生成了 {len(images)} 张图片:")
                for i, img in enumerate(images):
                    print(f"  图片 {i+1}: {img['imageUrl']}")
                    print(f"  审核状态: {img['auditStatus']}")
            else:
                print("未生成图片或图片未通过审核")

        else:
            print(f"请求失败: {result}")

    except Exception as e:
        print(f"错误: {e}")


if __name__ == "__main__":
    main()
