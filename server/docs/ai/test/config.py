#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LiblibAI F.1 Kontext 配置文件

请在这里配置您的API密钥和相关参数
"""

# API配置
LIBLIB_CONFIG = {
    # API密钥配置（请替换为您的实际密钥）
    "ACCESS_KEY": "Y3o3LNzNcut5QhmlC7nGHw",  # 从LiblibAI官网获取
    "SECRET_KEY": "69QLasO6gqsFeheJYoMGXkF9-1E9EdBd",  # 从LiblibAI官网获取
    # API服务配置
    "BASE_URL": "https://openapi.liblibai.cloud",
    "REQUEST_TIMEOUT": 30,  # 请求超时时间（秒）
    # F.1 Kontext 模板UUID
    "F1_KONTEXT_IMG2IMG_TEMPLATE_UUID": "1c0a9712b3d84e1b8a9f49514a46d88c",
    # 默认生图参数
    "DEFAULT_PARAMS": {
        "model": "max",  # pro 或 max
        "aspect_ratio": "1:1",  # 支持的宽高比
        "img_count": 1,  # 生图数量
        "guidance_scale": 3.5,  # 提示词引导系数
    },
    # 任务等待配置
    "WAIT_CONFIG": {
        "max_wait_time": 300,  # 最大等待时间（秒）
        "check_interval": 5,  # 检查间隔（秒）
    },
}

# 支持的宽高比选项
ASPECT_RATIOS = {
    "1:1": "正方形（1024x1024）",
    "2:3": "竖版（适合手机壁纸）",
    "3:2": "横版（适合电脑壁纸）",
    "3:4": "竖版（适合海报）",
    "4:3": "横版（传统照片比例）",
    "9:16": "竖版（短视频比例）",
    "16:9": "横版（视频比例）",
    "9:21": "超长竖版",
    "21:9": "超宽横版",
}

# 模型说明
MODELS = {
    "pro": "F.1 Kontext Pro版本 - 暂不支持多图参考，消耗29积分",
    "max": "F.1 Kontext Max版本 - 支持多图参考，消耗58积分",
}

# 生图状态说明
GENERATION_STATUS = {
    1: "等待执行",
    2: "执行中",
    3: "已生图",
    4: "审核中",
    5: "成功",
    6: "失败",
    7: "超时",
}

# 审核状态说明
AUDIT_STATUS = {1: "待审核", 2: "审核中", 3: "审核通过", 4: "审核拦截", 5: "审核失败"}


def get_config():
    """获取配置"""
    return LIBLIB_CONFIG


def validate_config(config=None):
    """验证配置是否有效"""
    if config is None:
        config = LIBLIB_CONFIG

    errors = []

    # 检查必要的API密钥
    if not config.get("ACCESS_KEY"):
        errors.append("请配置有效的ACCESS_KEY")

    if not config.get("SECRET_KEY"):
        errors.append("请配置有效的SECRET_KEY")

    # 检查URL配置
    if not config.get("BASE_URL"):
        errors.append("BASE_URL不能为空")

    return errors


def print_config_info():
    """打印配置信息"""
    print("LiblibAI F.1 Kontext 配置信息")
    print("=" * 40)

    config = get_config()

    print(f"API服务地址: {config['BASE_URL']}")
    print(f"请求超时: {config['REQUEST_TIMEOUT']}秒")
    print(f"模板UUID: {config['F1_KONTEXT_IMG2IMG_TEMPLATE_UUID']}")

    print("\n支持的模型:")
    for model, desc in MODELS.items():
        print(f"  {model}: {desc}")

    print("\n支持的宽高比:")
    for ratio, desc in ASPECT_RATIOS.items():
        print(f"  {ratio}: {desc}")

    print("\n默认参数:")
    for key, value in config["DEFAULT_PARAMS"].items():
        print(f"  {key}: {value}")

    # 验证配置
    errors = validate_config(config)
    if errors:
        print("\n⚠️  配置问题:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("\n✅ 配置验证通过")


if __name__ == "__main__":
    print_config_info()
