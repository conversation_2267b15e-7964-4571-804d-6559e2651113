{"11": {"inputs": {"model_name": "umt5_xxl_fp16.safetensors", "precision": "bf16", "load_device": "offload_device", "quantization": "disabled"}, "class_type": "LoadWanVideoT5TextEncoder", "_meta": {"title": "WanVideo T5 Text Encoder Loader"}}, "16": {"inputs": {"positive_prompt": ["100", 0], "negative_prompt": "bright tones, overexposed, static, blurred details, subtitles, style, works, paintings, images, static, overall gray, worst quality, low quality, JPEG compression residue, ugly, incomplete, extra fingers, poorly drawn hands, poorly drawn faces, deformed, disfigured, misshapen limbs, fused fingers, still picture, messy background, three legs, many people in the background, walking backwards", "force_offload": true, "use_disk_cache": false, "device": "gpu", "t5": ["11", 0], "model_to_offload": ["22", 0]}, "class_type": "WanVideoTextEncode", "_meta": {"title": "WanVideo TextEncode"}}, "22": {"inputs": {"model": "wan2114BFusionx_fusionxImage2video.safetensors", "base_precision": "bf16", "quantization": "disabled", "load_device": "offload_device", "attention_mode": "sdpa", "lora": ["71", 0]}, "class_type": "WanVideoModelLoader", "_meta": {"title": "WanVideo Model Loader"}}, "27": {"inputs": {"steps": 4, "cfg": 1, "shift": 5.000000000000001, "seed": 1057359483639287, "force_offload": true, "scheduler": "lcm", "riflex_freq_index": 0, "denoise_strength": 1, "batched_cfg": "", "rope_function": "comfy", "start_step": 0, "end_step": -1, "add_noise_to_samples": false, "model": ["22", 0], "text_embeds": ["16", 0], "image_embeds": ["63", 0], "feta_args": ["55", 0], "cache_args": ["52", 0]}, "class_type": "WanVideoSampler", "_meta": {"title": "WanVide<PERSON>"}}, "28": {"inputs": {"enable_vae_tiling": false, "tile_x": 272, "tile_y": 272, "tile_stride_x": 144, "tile_stride_y": 128, "normalization": "default", "vae": ["38", 0], "samples": ["27", 0]}, "class_type": "WanVideoDecode", "_meta": {"title": "WanVideo Decode"}}, "38": {"inputs": {"model_name": "Wan2_1_VAE_bf16.safetensors", "precision": "bf16"}, "class_type": "WanVideoVAELoader", "_meta": {"title": "WanVideo VAE Loader"}}, "52": {"inputs": {"rel_l1_thresh": 0.25, "start_step": 1, "end_step": -1, "cache_device": "main_device", "use_coefficients": "true", "mode": "e"}, "class_type": "WanVideoTeaCache", "_meta": {"title": "WaWanVideo TeaCache"}}, "55": {"inputs": {"weight": 2, "start_percent": 0, "end_percent": 1}, "class_type": "WanVideoEnhanceAVideo", "_meta": {"title": "WanVideo Enhance-A-Video"}}, "59": {"inputs": {"clip_name": "clip_vision_h.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "63": {"inputs": {"width": ["91", 1], "height": ["91", 2], "num_frames": 65, "noise_aug_strength": 0.030000000000000006, "start_latent_strength": 1, "end_latent_strength": 1, "force_offload": true, "fun_or_fl2v_model": false, "tiled_vae": false, "vae": ["38", 0], "clip_embeds": ["65", 0], "start_image": ["91", 0]}, "class_type": "WanVideoImageToVideoEncode", "_meta": {"title": "WanVideo ImageToVideo Encode"}}, "65": {"inputs": {"strength_1": 1, "strength_2": 1, "crop": "center", "combine_embeds": "average", "force_offload": true, "tiles": 0, "ratio": 0.20000000000000004, "clip_vision": ["59", 0], "image_1": ["91", 0]}, "class_type": "WanVideoClipVisionEncode", "_meta": {"title": "WanVideo ClipVision Encode"}}, "71": {"inputs": {"lora": "Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors", "strength": 1.2000000000000002, "low_mem_load": false, "merge_loras": true}, "class_type": "WanVideoLoraSelect", "_meta": {"title": "WanVideo Lora Select"}}, "81": {"inputs": {"value": 16}, "class_type": "PrimitiveFloat", "_meta": {"title": "Float"}}, "82": {"inputs": {"filename_prefix": "Heygem", "format": "auto", "codec": "auto", "video": ["97", 0]}, "class_type": "SaveVideo", "_meta": {"title": "Save Video"}}, "83": {"inputs": {"version": "v1.5", "top_k": 30, "top_p": 0.8, "temperature": 1, "num_beams": 3, "max_mel_tokens": 1000, "max_text_tokens_per_sentence": 120, "sentences_bucket_max_size": 4, "fast_inference": false, "custom_cuda_kernel": false, "unload_model": true, "audio": ["93", 0], "text": ["98", 0]}, "class_type": "IndexTTSRun", "_meta": {"title": "IndexTTS Run"}}, "91": {"inputs": {"width": 832, "height": 832, "upscale_method": "lanc<PERSON>s", "keep_proportion": true, "divisible_by": 16, "crop": "disabled", "image": ["92", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image (deprecated)"}}, "92": {"inputs": {"image": "6a2d85d2cda3499d493702f9749751c41d7bc650fe3f8046861f576e9cddeb1a.jpeg"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "93": {"inputs": {"audio": "811addc0e964b84db20efcc034daf81979ea098626796b16ed2826f049cac909.mp3", "start_time": 0, "duration": 15.000000000000004}, "class_type": "VHS_LoadAudioUpload", "_meta": {"title": "Load Audio (Upload)🎥🅥🅗🅢"}}, "96": {"inputs": {"fps": ["81", 0], "images": ["103", 0], "audio": ["103", 1]}, "class_type": "CreateVideo", "_meta": {"title": "Create Video"}}, "97": {"inputs": {"AUDIO": ["83", 0], "VIDEO": ["96", 0]}, "class_type": "RH_HeyGemNode", "_meta": {"title": "HeyGem Video Synthesis"}}, "98": {"inputs": {"value": "AI优化师，running hub 是最棒的！一起来玩啊"}, "class_type": "PrimitiveStringMultiline", "_meta": {"title": "台词"}}, "99": {"inputs": {"filename_prefix": "audio/ComfyUI", "audioUI": "", "audio": ["83", 0]}, "class_type": "SaveAudio", "_meta": {"title": "Save Audio (FLAC)"}}, "100": {"inputs": {"value": "美女在说话"}, "class_type": "PrimitiveStringMultiline", "_meta": {"title": "动作提示"}}, "102": {"inputs": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "no_preview": false, "images": ["28", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "103": {"inputs": {"mode": "pingpong", "images": ["28", 0], "audio": ["83", 0]}, "class_type": "D_VideoLengthAdjuster", "_meta": {"title": "Video Length Adjuster"}}}