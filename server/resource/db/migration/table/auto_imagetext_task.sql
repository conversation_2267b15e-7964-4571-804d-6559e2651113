CREATE TABLE
  IF NOT EXISTS `auto_imagetext_task` (
    `id` bigint (20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `created_at` datetime DEFAULT NULL COMMENT '创建时间',
    `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    `task_name` varchar(255) NOT NULL COMMENT '任务名称',
    `status` tinyint (4) NOT NULL DEFAULT '0' COMMENT '任务状态,0:未处理,1:成功,2:失败,3:处理中',
    `error_msg` text COMMENT '错误信息',
    `created_by` bigint (20) unsigned NOT NULL COMMENT '创建者ID',
    `created_name` varchar(100) DEFAULT NULL COMMENT '创建者名称',
    `generate_count` int (11) NOT NULL COMMENT '生成数量',
    `success_count` int (11) DEFAULT '0' COMMENT '成功生成数量',
    `categories` text COMMENT '视频分类IDs,JSON字符串',
    `video_title` varchar(255) DEFAULT NULL COMMENT '视频标题',
    `manual_product_id` bigint (20) unsigned DEFAULT '0' COMMENT '手动录入商品ID',
    `topic` varchar(255) DEFAULT NULL COMMENT '话题',
    `image_materials` text COMMENT '图片素材配置',
    `title_settings` text COMMENT '标题设置配置',
    `background_musics` text COMMENT '背景音乐配置',
    `sticker_settings` text COMMENT '贴纸设置配置',
    `min_image_count` int (11) DEFAULT '3' COMMENT '最小图片数量',
    `max_image_count` int (11) DEFAULT '9' COMMENT '最大图片数量',
    `title_apply_mode` tinyint (4) DEFAULT '1' COMMENT '标题应用模式：1-仅第一张 2-每张都加',
    `sticker_apply_mode` tinyint (4) DEFAULT '1' COMMENT '贴纸应用模式：1-仅第一张 2-每张都加',
    PRIMARY KEY (`id`),
    KEY `idx_auto_imagetext_task_deleted_at` (`deleted_at`),
    KEY `idx_created_by` (`created_by`),
    KEY `idx_status` (`status`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '批量图文生成任务表';