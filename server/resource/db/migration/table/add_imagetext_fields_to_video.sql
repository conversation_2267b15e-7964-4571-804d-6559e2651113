-- 为video表添加图文相关字段
ALTER TABLE `video`
ADD COLUMN `content_type` TINYINT DEFAULT 1 COMMENT '内容类型：1-视频 2-图文' AFTER `duration`,
ADD COLUMN `image_urls` TEXT COMMENT '图片链接列表（JSON格式）' AFTER `content_type`,
ADD COLUMN `min_image_count` INT DEFAULT 3 COMMENT '最小图片数量' AFTER `image_urls`,
ADD COLUMN `max_image_count` INT DEFAULT 9 COMMENT '最大图片数量' AFTER `min_image_count`,
ADD COLUMN `title_apply_mode` TINYINT DEFAULT 1 COMMENT '标题应用模式：1-仅第一张 2-每张都加' AFTER `max_image_count`,
ADD COLUMN `sticker_apply_mode` TINYINT DEFAULT 1 COMMENT '贴纸应用模式：1-仅第一张 2-每张都加' AFTER `title_apply_mode`;