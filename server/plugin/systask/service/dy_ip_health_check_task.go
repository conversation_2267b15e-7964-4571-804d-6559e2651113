package service

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	douyin "github.com/flipped-aurora/gin-vue-admin/server/service/douyin"
	"go.uber.org/zap"
)

// DyIPHealthCheckTask IP健康检查任务
type DyIPHealthCheckTask struct {
}

// Exec 执行任务
func (t DyIPHealthCheckTask) Exec(arg interface{}) error {
	global.GVA_LOG.Info("开始执行IP健康检查任务")

	// 创建健康检查服务实例
	healthCheckService := &douyin.IPHealthCheckService{}

	// 执行所有启用IP的健康检查
	err := healthCheckService.CheckAllIPs()
	if err != nil {
		global.GVA_LOG.Error("IP健康检查任务执行失败", zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("IP健康检查任务执行完成")
	return nil
}
