package service

import (
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai"
	aiService "github.com/flipped-aurora/gin-vue-admin/server/service/ai"
	"go.uber.org/zap"
)

// DigitalHumanStatusTask 数字人任务状态检查
type DigitalHumanStatusTask struct{}

// Exec 执行任务：扫描 RUNNING/PENDING 的数字人任务，查询状态并在成功后入库视频
func (s DigitalHumanStatusTask) Exec(arg any) (err error) {
	taskTitle := "数字人任务状态检查"
	now := time.Now()
	global.GVA_LOG.Info(fmt.Sprintf("%s开始执行，时间: %s", taskTitle, now.Format(time.DateTime)))

	// 查询进行中的任务
	var tasks []ai.DigitalHumanTask
	if err := global.GVA_DB.Where("status IN (?)", []string{"RUNNING", "PENDING"}).Find(&tasks).Error; err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("%s: 查询任务失败", taskTitle), zap.Error(err))
		return err
	}

	if len(tasks) == 0 {
		global.GVA_LOG.Info(fmt.Sprintf("%s: 没有需要处理的任务", taskTitle))
		return nil
	}

	service := &aiService.DigitalHumanService{}
	for i := range tasks {
		service.GetDigitalHumanTaskStatus(tasks[i].TaskId)
	}

	global.GVA_LOG.Info(fmt.Sprintf("%s执行完毕，时间: %s", taskTitle, time.Now().Format(time.DateTime)))
	return nil
}
