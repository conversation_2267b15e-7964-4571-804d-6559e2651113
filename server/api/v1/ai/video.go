package ai

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	utilsAi "github.com/flipped-aurora/gin-vue-admin/server/utils/ai"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type VideoApi struct{}

// CreateVideoTask 创建视频生成任务
// @Tags      Video
// @Summary   创建视频生成任务
// @Security  ApiKeyAuth
// @accept    multipart/form-data
// @Produce   application/json
// @Param     modelName    formData  string  false  "模型选择"
// @Param     description  formData  string  false  "创意描述"
// @Param     image        formData  file    true   "图片文件"
// @Success   200          {object}  response.Response{data=aiResponse.VideoTaskResponse}
// @Router    /ai/video/create [post]
func (v *VideoApi) CreateVideoTask(c *gin.Context) {
	// 获取当前用户ID
	userId := utils.GetUserID(c)

	// 获取上传的文件
	file, err := c.FormFile("image")
	if err != nil {
		global.GVA_LOG.Error("获取上传文件失败", zap.Error(err))
		response.FailWithMessage("获取上传文件失败", c)
		return
	}

	// 获取表单参数
	var req utilsAi.ImageToVideoTaskRequest
	req.ModelName = c.PostForm("modelName")
	req.Description = c.PostForm("description")

	// 固定使用5秒视频
	req.Duration = 5

	// 调用服务创建任务
	taskResponse, err := videoService.CreateVideoTask(file, req, userId)
	if err != nil {
		global.GVA_LOG.Error("创建视频任务失败", zap.Error(err))
		response.FailWithMessage("创建视频任务失败: "+err.Error(), c)
		return
	}

	response.OkWithData(taskResponse, c)
}

// GetVideoTaskStatus 获取视频任务状态
// @Tags      Video
// @Summary   获取视频任务状态
// @Security  ApiKeyAuth
// @Produce   application/json
// @Param     taskId  query  string  true  "任务ID"
// @Success   200     {object}  response.Response{data=aiResponse.VideoTaskStatusResponse}
// @Router    /ai/video/status [get]
func (v *VideoApi) GetVideoTaskStatus(c *gin.Context) {
	// 获取请求参数
	var req request.VideoTaskStatusRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	// 参数验证
	if req.TaskId == "" {
		response.FailWithMessage("缺少任务ID", c)
		return
	}

	// 调用服务获取任务状态
	statusResponse, err := videoService.GetVideoTaskStatus(req)
	if err != nil {
		global.GVA_LOG.Error("获取视频任务状态失败", zap.Error(err))
		response.FailWithMessage("获取视频任务状态失败: "+err.Error(), c)
		return
	}

	response.OkWithData(statusResponse, c)
}

// GetVideoTaskList 获取视频任务列表
// @Tags      Video
// @Summary   获取视频任务列表
// @Security  ApiKeyAuth
// @Produce   application/json
// @Param     status    query  string  false  "任务状态"
// @Param     page      query  int     true   "页码"
// @Param     pageSize  query  int     true   "每页数量"
// @Success   200       {object}  response.Response{data=aiResponse.VideoTaskListResponse}
// @Router    /ai/video/list [get]
func (v *VideoApi) GetVideoTaskList(c *gin.Context) {
	// 获取当前用户ID
	userId := utils.GetUserID(c)

	// 获取请求参数
	var req request.VideoTaskListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	// 参数验证
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 调用服务获取任务列表
	listResponse, err := videoService.GetVideoTaskList(req, userId)
	if err != nil {
		global.GVA_LOG.Error("获取视频任务列表失败", zap.Error(err))
		response.FailWithMessage("获取视频任务列表失败: "+err.Error(), c)
		return
	}

	response.OkWithData(listResponse, c)
}

// DeleteVideoTask 删除视频任务
// @Tags      Video
// @Summary   删除视频任务
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body  request.VideoTaskStatusRequest  true  "任务ID"
// @Success   200   {object}  response.Response
// @Router    /ai/video/delete [delete]
func (v *VideoApi) DeleteVideoTask(c *gin.Context) {
	// 获取当前用户ID
	userId := utils.GetUserID(c)

	// 获取请求参数
	var req request.VideoTaskStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	// 参数验证
	if req.TaskId == "" {
		response.FailWithMessage("缺少任务ID", c)
		return
	}

	// 调用服务删除任务
	err := videoService.DeleteVideoTask(req.TaskId, userId)
	if err != nil {
		global.GVA_LOG.Error("删除视频任务失败", zap.Error(err))
		response.FailWithMessage("删除视频任务失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// RefreshProcessingTasks 刷新所有处理中的任务状态
// @Tags      Video
// @Summary   刷新所有处理中的任务状态
// @Security  ApiKeyAuth
// @Produce   application/json
// @Success   200  {object}  response.Response
// @Router    /ai/video/refresh [get]
func (v *VideoApi) RefreshProcessingTasks(c *gin.Context) {
	// 调用服务刷新所有处理中的任务状态
	count, err := videoService.RefreshProcessingTasks()
	if err != nil {
		global.GVA_LOG.Error("刷新任务状态失败", zap.Error(err))
		response.FailWithMessage("刷新任务状态失败: "+err.Error(), c)
		return
	}

	response.OkWithData(map[string]interface{}{
		"count": count,
		"msg":   "刷新成功",
	}, c)
}
