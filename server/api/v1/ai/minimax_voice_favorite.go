package ai

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MinimaxVoiceFavoriteApi struct{}

// AddFavoriteVoice 添加收藏音色
// @Tags      MinimaxVoiceFavorite
// @Summary   添加收藏音色
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      AddFavoriteVoiceRequest  true  "音色ID"
// @Success   200   {object}  response.Response{msg=string}  "添加成功"
// @Router    /ai/minimax-voice-favorite/add [post]
func (m *MinimaxVoiceFavoriteApi) AddFavoriteVoice(c *gin.Context) {
	var req AddFavoriteVoiceRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	key := fmt.Sprintf("minimax_voice_favorites:%d", userID)

	// 获取现有收藏列表
	favorites, err := m.getFavorites(key)
	if err != nil {
		global.GVA_LOG.Error("获取收藏列表失败", zap.Error(err))
		response.FailWithMessage("获取收藏列表失败", c)
		return
	}

	// 检查是否已收藏
	for _, voiceId := range favorites {
		if voiceId == req.VoiceId {
			response.FailWithMessage("该音色已收藏", c)
			return
		}
	}

	// 添加到收藏
	favorites = append(favorites, req.VoiceId)

	// 保存到Redis
	if err := m.saveFavorites(key, favorites); err != nil {
		global.GVA_LOG.Error("保存收藏失败", zap.Error(err))
		response.FailWithMessage("保存收藏失败", c)
		return
	}

	response.OkWithMessage("添加收藏成功", c)
}

// RemoveFavoriteVoice 删除收藏音色
// @Tags      MinimaxVoiceFavorite
// @Summary   删除收藏音色
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      RemoveFavoriteVoiceRequest  true  "音色ID"
// @Success   200   {object}  response.Response{msg=string}  "删除成功"
// @Router    /ai/minimax-voice-favorite/remove [post]
func (m *MinimaxVoiceFavoriteApi) RemoveFavoriteVoice(c *gin.Context) {
	var req RemoveFavoriteVoiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	key := fmt.Sprintf("minimax_voice_favorites:%d", userID)

	// 获取现有收藏列表
	favorites, err := m.getFavorites(key)
	if err != nil {
		global.GVA_LOG.Error("获取收藏列表失败", zap.Error(err))
		response.FailWithMessage("获取收藏列表失败", c)
		return
	}

	// 从收藏中删除
	newFavorites := make([]string, 0)
	found := false
	for _, voiceId := range favorites {
		if voiceId != req.VoiceId {
			newFavorites = append(newFavorites, voiceId)
		} else {
			found = true
		}
	}

	if !found {
		response.FailWithMessage("该音色未收藏", c)
		return
	}

	// 保存到Redis
	if err := m.saveFavorites(key, newFavorites); err != nil {
		global.GVA_LOG.Error("保存收藏失败", zap.Error(err))
		response.FailWithMessage("保存收藏失败", c)
		return
	}

	response.OkWithMessage("删除收藏成功", c)
}

// GetFavoriteVoices 获取收藏音色列表
// @Tags      MinimaxVoiceFavorite
// @Summary   获取收藏音色列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{data=[]string,msg=string}  "获取成功"
// @Router    /ai/minimax-voice-favorite/list [get]
func (m *MinimaxVoiceFavoriteApi) GetFavoriteVoices(c *gin.Context) {
	userID := utils.GetUserID(c)
	key := fmt.Sprintf("minimax_voice_favorites:%d", userID)

	favorites, err := m.getFavorites(key)
	if err != nil {
		global.GVA_LOG.Error("获取收藏列表失败", zap.Error(err))
		response.FailWithMessage("获取收藏列表失败", c)
		return
	}

	response.OkWithData(favorites, c)
}

// 从Redis获取收藏列表
func (m *MinimaxVoiceFavoriteApi) getFavorites(key string) ([]string, error) {
	result, err := global.GVA_REDIS.Get(context.Background(), key).Result()
	if err != nil {
		if err.Error() == "redis: nil" {
			// 如果key不存在，返回空列表
			return []string{}, nil
		}
		return nil, err
	}

	var favorites []string
	if err := json.Unmarshal([]byte(result), &favorites); err != nil {
		return nil, err
	}

	return favorites, nil
}

// 保存收藏列表到Redis
func (m *MinimaxVoiceFavoriteApi) saveFavorites(key string, favorites []string) error {
	data, err := json.Marshal(favorites)
	if err != nil {
		return err
	}

	return global.GVA_REDIS.Set(context.Background(), key, data, 0).Err()
}

// 请求结构体
type AddFavoriteVoiceRequest struct {
	VoiceId string `json:"voiceId" binding:"required"`
}

type RemoveFavoriteVoiceRequest struct {
	VoiceId string `json:"voiceId" binding:"required"`
}
