package ai

import (
	"math/rand"
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AutoVideoApi struct{}

// SubmitAutoVideoTask
// @Tags      AutoVideo
// @Summary   提交一键成片任务
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.AutoVideoTaskParams  true  "一键成片任务参数"
// @Success   200   {object}  response.Response{data=string}  "提交成功"
// @Router    /ai/video/auto/submit [post]
func (a *AutoVideoApi) SubmitAutoVideoTask(c *gin.Context) {
	var taskParams request.AutoVideoTaskParams
	if err := c.ShouldBindJSON(&taskParams); err != nil {
		response.FailWithMessage("参数错误"+err.Error(), c)
		return
	}
	if taskParams.UseMusicDuration {
		if len(taskParams.BackgroundMusics) == 0 {
			response.FailWithMessage("检测存在使用音乐时长，但未添加背景音乐，请添加背景音乐", c)
			return
		}
	}
	userId := utils.GetUserID(c)
	var jobId string
	var err error
	// 如果使用了音乐时长，并且背景音乐数量大于0，并且生成数量大于1，则需要多次请求，每次生成1个
	generateCount := taskParams.GenerateCount
	musicCount := len(taskParams.BackgroundMusics)
	if taskParams.UseMusicDuration && musicCount > 0 {
		var settedDuration float64
		for _, material := range taskParams.LensMaterials {
			if !material.UseMusicDuration {
				if material.SplitMode == "AverageSplit" {
					settedDuration += material.Duration
				} else if material.Duration > 0 {
					// 完整播放模式，如果设置了时长，参与计算
					settedDuration += material.Duration
				}
			}
		}
		minMusicDuration := taskParams.BackgroundMusicDurations[0]
		for _, duration := range taskParams.BackgroundMusicDurations {
			if duration < minMusicDuration {
				minMusicDuration = duration
			}
		}
		if settedDuration >= minMusicDuration {
			response.FailWithMessage("存在背景音乐时长小于镜头总时长的音乐，请调整背景音乐或镜头时长", c)
			return
		}
		var failCount int
		// 保存原始背景音乐列表
		originalBackgroundMusics := make([]string, len(taskParams.BackgroundMusics))
		copy(originalBackgroundMusics, taskParams.BackgroundMusics)
		originalBackgroundMusicDurations := make([]float64, len(taskParams.BackgroundMusicDurations))
		copy(originalBackgroundMusicDurations, taskParams.BackgroundMusicDurations)

		for range make([]struct{}, generateCount) {
			taskParams.GenerateCount = 1
			// 随机一条背景音乐
			randMusic := rand.Intn(musicCount)
			selectedMusic := originalBackgroundMusics[randMusic]
			taskParams.BackgroundMusics = []string{selectedMusic}
			backgroundMusicDuration := originalBackgroundMusicDurations[randMusic]
			// 以背景音乐时长为准的镜头时长计算 = 背景音乐时长 - 已设置的镜头总时长
			for i, material := range taskParams.LensMaterials {
				if material.UseMusicDuration {
					taskParams.LensMaterials[i].Duration = backgroundMusicDuration - settedDuration
					break
				}
			}
			jobId, err = autoVideoService.SubmitAutoVideoTask(taskParams, userId)
			if err != nil {
				global.GVA_LOG.Error("提交一键成片任务失败!", zap.Error(err))
				failCount++
			}
		}
		if failCount == taskParams.GenerateCount {
			response.FailWithMessage("提交一键成片任务失败: "+err.Error(), c)
			return
		} else {
			successCount := generateCount - failCount
			response.OkWithDetailed(gin.H{
				"jobId":        jobId,
				"successCount": successCount,
			}, "提交成功", c)
			return
		}
	} else if taskParams.DisableRandomSubtitle {
		// 禁用随机字幕，不管是全局字幕还是镜头字幕，都分多次请求，并按顺序使用
		originalGenerateCount := taskParams.GenerateCount // 保存原始生成数量
		taskParams.GenerateCount = 1

		var failCount int
		if taskParams.GlobalVoice {
			// 全局字幕模式：按顺序读取 GlobalSubtitleList
			if len(taskParams.GlobalSubtitleList) == 0 {
				// 如果没有配置全局字幕，直接按原始生成数量分多次提交
				for i := 0; i < originalGenerateCount; i++ {
					jobId, err = autoVideoService.SubmitAutoVideoTask(taskParams, userId)
					if err != nil {
						global.GVA_LOG.Error("提交一键成片任务失败!", zap.Error(err))
						failCount++
					}
				}
			} else {
				// 保存原始全局字幕列表
				originalGlobalSubtitles := make([]string, len(taskParams.GlobalSubtitleList))
				copy(originalGlobalSubtitles, taskParams.GlobalSubtitleList)

				for i := 0; i < originalGenerateCount; i++ {
					// 按顺序选择全局字幕，如果不足则循环使用
					subtitleIndex := i % len(originalGlobalSubtitles)
					selectedSubtitle := originalGlobalSubtitles[subtitleIndex]
					taskParams.GlobalSubtitleList = []string{selectedSubtitle}

					jobId, err = autoVideoService.SubmitAutoVideoTask(taskParams, userId)
					if err != nil {
						global.GVA_LOG.Error("提交一键成片任务失败!", zap.Error(err))
						failCount++
					}
				}
			}
		} else {
			// 镜头字幕模式：按顺序读取每个镜头的 Narrations
			// 先检查是否有镜头配置了字幕
			hasNarrations := false
			for _, material := range taskParams.LensMaterials {
				if len(material.Narrations) > 0 || material.Narration != "" {
					hasNarrations = true
					break
				}
			}

			if !hasNarrations {
				// 如果没有配置镜头字幕，直接按原始生成数量分多次提交
				for i := 0; i < originalGenerateCount; i++ {
					jobId, err = autoVideoService.SubmitAutoVideoTask(taskParams, userId)
					if err != nil {
						global.GVA_LOG.Error("提交一键成片任务失败!", zap.Error(err))
						failCount++
					}
				}
			} else {
				// 保存原始镜头材料
				originalLensMaterials := make([]request.LensMaterial, len(taskParams.LensMaterials))
				for i, material := range taskParams.LensMaterials {
					originalLensMaterials[i] = request.LensMaterial{
						MediaId:          material.MediaId,
						MediaUrl:         material.MediaUrl,
						Type:             material.Type,
						Narration:        material.Narration,
						Narrations:       make([]string, len(material.Narrations)),
						ExtraMedia:       material.ExtraMedia,
						SplitMode:        material.SplitMode,
						Duration:         material.Duration,
						UseMusicDuration: material.UseMusicDuration,
					}
					copy(originalLensMaterials[i].Narrations, material.Narrations)
				}

				for i := 0; i < originalGenerateCount; i++ {
					// 为每个镜头按顺序选择字幕
					for j := range taskParams.LensMaterials {
						originalMaterial := originalLensMaterials[j]

						if len(originalMaterial.Narrations) > 0 {
							// 有多个字幕，按顺序循环选择
							narrationIndex := i % len(originalMaterial.Narrations)
							selectedNarration := originalMaterial.Narrations[narrationIndex]
							taskParams.LensMaterials[j].Narrations = []string{selectedNarration}
							taskParams.LensMaterials[j].Narration = selectedNarration
						} else if originalMaterial.Narration != "" {
							// 只有单个字幕，继续使用
							taskParams.LensMaterials[j].Narration = originalMaterial.Narration
							taskParams.LensMaterials[j].Narrations = []string{originalMaterial.Narration}
						}
					}

					jobId, err = autoVideoService.SubmitAutoVideoTask(taskParams, userId)
					if err != nil {
						global.GVA_LOG.Error("提交一键成片任务失败!", zap.Error(err))
						failCount++
					}
				}
			}
		}

		if failCount == originalGenerateCount {
			response.FailWithMessage("提交一键成片任务失败: "+err.Error(), c)
			return
		} else {
			successCount := originalGenerateCount - failCount
			response.OkWithDetailed(gin.H{
				"jobId":        jobId,
				"successCount": successCount,
			}, "提交成功", c)
			return
		}
	} else {
		jobId, err = autoVideoService.SubmitAutoVideoTask(taskParams, userId)
		if err != nil {
			global.GVA_LOG.Error("提交一键成片任务失败!", zap.Error(err))
			response.FailWithMessage("提交一键成片任务失败: "+err.Error(), c)
			return
		}
	}

	response.OkWithData(jobId, c)
}

// GetAutoVideoTaskStatus
// @Tags      AutoVideo
// @Summary   获取一键成片任务状态
// @Security  ApiKeyAuth
// @Produce   application/json
// @Param     jobId  path     string  true  "任务ID"
// @Success   200    {object}  response.Response{data=ai.AutoVideoTaskStatus}  "获取成功"
// @Router    /ai/video/auto/status/{jobId} [get]
func (a *AutoVideoApi) GetAutoVideoTaskStatus(c *gin.Context) {
	jobId := c.Param("jobId")
	if jobId == "" {
		response.FailWithMessage("任务ID不能为空", c)
		return
	}

	taskStatus, err := autoVideoService.GetAutoVideoTaskStatus(jobId)
	if err != nil {
		global.GVA_LOG.Error("获取一键成片任务状态失败!", zap.Error(err))
		response.FailWithMessage("获取一键成片任务状态失败: "+err.Error(), c)
		return
	}
	response.OkWithData(taskStatus, c)
}

// ListAutoVideoTasks
// @Tags      AutoVideo
// @Summary   获取一键成片任务列表
// @Security  ApiKeyAuth
// @Produce   application/json
// @Param     page      query    int     true  "页码"
// @Param     pageSize  query    int     true  "每页数量"
// @Success   200       {object}  response.Response{data=response.PageResult,msg=string}  "获取成功"
// @Router    /ai/video/auto/list [get]
func (a *AutoVideoApi) ListAutoVideoTasks(c *gin.Context) {
	var pageInfo request.AutoVideoTaskListParams
	if err := c.ShouldBindQuery(&pageInfo); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := autoVideoService.ListAutoVideoTasks(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取一键成片任务列表失败!", zap.Error(err))
		response.FailWithMessage("获取一键成片任务列表失败: "+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// DeleteAutoVideoTask
// @Tags      AutoVideo
// @Summary   删除一键成片任务
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      ai.AutoVideoTask  true  "任务ID"
// @Success   200   {object}  response.Response{msg=string}  "删除成功"
// @Router    /ai/video/auto/delete [delete]
func (a *AutoVideoApi) DeleteAutoVideoTask(c *gin.Context) {
	var task ai.AutoVideoTask
	if err := c.ShouldBindJSON(&task); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := autoVideoService.DeleteAutoVideoTask(task); err != nil {
		global.GVA_LOG.Error("删除一键成片任务失败!", zap.Error(err))
		response.FailWithMessage("删除一键成片任务失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// GetResourceLibrary
// @Tags      AutoVideo
// @Summary   获取素材库资源
// @Security  ApiKeyAuth
// @Produce   application/json
// @Param     type      query    string  true  "素材类型：video/image/audio"
// @Param     category  query    string  false  "素材分类ID"
// @Param     page      query    int     true  "页码"
// @Param     pageSize  query    int     true  "每页数量"
// @Param     keyword   query    string  false  "搜索关键词"
// @Success   200       {object}  response.Response{data=response.PageResult,msg=string}  "获取成功"
// @Router    /ai/video/auto/resource [get]
func (a *AutoVideoApi) GetResourceLibrary(c *gin.Context) {
	var params request.ResourceLibraryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 根据素材类型调用不同的媒体服务
	var list interface{}
	var total int64
	var err error

	categoryId := 0
	if params.Category != "" {
		// 尝试将分类ID转换为整数
		categoryId, _ = strconv.Atoi(params.Category)
	}

	switch params.Type {
	case "video":
		// 获取视频资源
		list, total, err = mediaVideoService.GetList(categoryId, params.Keyword, params.Page, params.PageSize)
	case "image":
		// 获取图片资源
		list, total, err = mediaImageService.GetList(categoryId, params.Keyword, params.Page, params.PageSize)
	case "audio":
		// 获取音频资源
		list, total, err = mediaMusicService.GetList(categoryId, params.Keyword, params.Page, params.PageSize)
	default:
		response.FailWithMessage("不支持的素材类型", c)
		return
	}

	if err != nil {
		global.GVA_LOG.Error("获取素材库资源失败!", zap.Error(err))
		response.FailWithMessage("获取素材库资源失败: "+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     params.Page,
		PageSize: params.PageSize,
	}, "获取成功", c)
}

// GetResourceCategories
// @Tags      AutoVideo
// @Summary   获取素材分类
// @Security  ApiKeyAuth
// @Produce   application/json
// @Param     type  query    string  true  "素材类型：video/image/audio"
// @Success   200   {object}  response.Response{data=[]ai.ResourceCategory,msg=string}  "获取成功"
// @Router    /ai/video/auto/categories [get]
func (a *AutoVideoApi) GetResourceCategories(c *gin.Context) {
	resourceType := c.Query("type")
	if resourceType == "" {
		response.FailWithMessage("素材类型不能为空", c)
		return
	}

	// 使用媒体服务的API获取分类信息
	var categories interface{}
	var err error

	// 根据素材类型调用不同的服务
	switch resourceType {
	case "video":
		// 调用视频分类服务
		categories, err = videoCategoryService.GetCategoryList(c)
	case "image":
		// 调用图片分类服务
		categories, err = imageCategoryService.GetCategoryList(c)
	case "audio":
		// 调用音频分类服务
		categories, err = musicCategoryService.GetCategoryList(c)
	default:
		response.FailWithMessage("不支持的素材类型", c)
		return
	}

	if err != nil {
		global.GVA_LOG.Error("获取素材分类失败!", zap.Error(err))
		response.FailWithMessage("获取素材分类失败: "+err.Error(), c)
		return
	}
	response.OkWithData(categories, c)
}

// --- 收藏配音演员 API --- //

// GetFavoriteActors 获取用户收藏的配音演员列表
// @Tags AutoVideo
// @Summary 获取用户收藏的配音演员列表
// @Security ApiKeyAuth
// @Produce json
// @Success 200 {object} response.Response{data=[]string, msg=string} "获取成功"
// @Router /ai/favorite-actors [get]
func (api *AutoVideoApi) GetFavoriteActors(c *gin.Context) {
	userID := utils.GetUserID(c)
	actorIDs, err := autoVideoService.GetFavoriteActors(userID)
	if err != nil {
		global.GVA_LOG.Error("获取收藏演员列表失败!", zap.Error(err))
		response.FailWithMessage("获取收藏演员列表失败", c)
		return
	}
	response.OkWithDetailed(actorIDs, "获取成功", c)
}

// AddFavoriteActor 添加用户收藏的配音演员
// @Tags AutoVideo
// @Summary 添加用户收藏的配音演员
// @Security ApiKeyAuth
// @Accept json
// @Produce json
// @Param data body request.FavoriteActorRequest true "演员ID"
// @Success 200 {object} response.Response{msg=string} "添加成功"
// @Router /ai/favorite-actors [post]
func (api *AutoVideoApi) AddFavoriteActor(c *gin.Context) {
	var req request.FavoriteActorRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if req.ActorID == "" {
		response.FailWithMessage("演员ID不能为空", c)
		return
	}

	userID := utils.GetUserID(c)
	if err := autoVideoService.AddFavoriteActor(userID, req.ActorID); err != nil {
		global.GVA_LOG.Error("添加收藏演员失败!", zap.String("actorID", req.ActorID), zap.Error(err))
		response.FailWithMessage("添加收藏失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("添加收藏成功", c)
}

// RemoveFavoriteActor 移除用户收藏的配音演员
// @Tags AutoVideo
// @Summary 移除用户收藏的配音演员
// @Security ApiKeyAuth
// @Produce json
// @Param actorId path string true "演员ID"
// @Success 200 {object} response.Response{msg=string} "移除成功"
// @Router /ai/favorite-actors/{actorId} [delete]
func (api *AutoVideoApi) RemoveFavoriteActor(c *gin.Context) {
	actorID := c.Param("actorId")
	if actorID == "" {
		response.FailWithMessage("演员ID不能为空", c)
		return
	}

	userID := utils.GetUserID(c)
	if err := autoVideoService.RemoveFavoriteActor(userID, actorID); err != nil {
		global.GVA_LOG.Error("移除收藏演员失败!", zap.String("actorID", actorID), zap.Error(err))
		response.FailWithMessage("移除收藏失败", c)
		return
	}
	response.OkWithMessage("移除收藏成功", c)
}

// --- 收藏花字样式 API --- //

// GetFavoriteFlowerFonts 获取用户收藏的花字样式列表
// @Tags AutoVideo
// @Summary 获取用户收藏的花字样式列表
// @Security ApiKeyAuth
// @Produce json
// @Success 200 {object} response.Response{data=[]string, msg=string} "获取成功"
// @Router /ai/favorite-flower-fonts [get]
func (api *AutoVideoApi) GetFavoriteFlowerFonts(c *gin.Context) {
	userID := utils.GetUserID(c)
	flowerStyles, err := autoVideoService.GetFavoriteFlowerFonts(userID)
	if err != nil {
		global.GVA_LOG.Error("获取收藏花字样式列表失败!", zap.Error(err))
		response.FailWithMessage("获取收藏花字样式列表失败", c)
		return
	}
	response.OkWithDetailed(flowerStyles, "获取成功", c)
}

// AddFavoriteFlowerFont 添加用户收藏的花字样式
// @Tags AutoVideo
// @Summary 添加用户收藏的花字样式
// @Security ApiKeyAuth
// @Accept json
// @Produce json
// @Param data body request.FavoriteFlowerFontRequest true "花字样式ID"
// @Success 200 {object} response.Response{msg=string} "添加成功"
// @Router /ai/favorite-flower-fonts [post]
func (api *AutoVideoApi) AddFavoriteFlowerFont(c *gin.Context) {
	var req request.FavoriteFlowerFontRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if req.FlowerStyle == "" {
		response.FailWithMessage("花字样式ID不能为空", c)
		return
	}

	userID := utils.GetUserID(c)
	if err := autoVideoService.AddFavoriteFlowerFont(userID, req.FlowerStyle); err != nil {
		global.GVA_LOG.Error("添加收藏花字样式失败!", zap.String("flowerStyle", req.FlowerStyle), zap.Error(err))
		response.FailWithMessage("添加收藏失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("添加收藏成功", c)
}

// RemoveFavoriteFlowerFont 移除用户收藏的花字样式
// @Tags AutoVideo
// @Summary 移除用户收藏的花字样式
// @Security ApiKeyAuth
// @Produce json
// @Param flowerStyle path string true "花字样式ID"
// @Success 200 {object} response.Response{msg=string} "移除成功"
// @Router /ai/favorite-flower-fonts/{flowerStyle} [delete]
func (api *AutoVideoApi) RemoveFavoriteFlowerFont(c *gin.Context) {
	flowerStyle := c.Param("flowerStyle")
	if flowerStyle == "" {
		response.FailWithMessage("花字样式ID不能为空", c)
		return
	}

	userID := utils.GetUserID(c)
	if err := autoVideoService.RemoveFavoriteFlowerFont(userID, flowerStyle); err != nil {
		global.GVA_LOG.Error("移除收藏花字样式失败!", zap.String("flowerStyle", flowerStyle), zap.Error(err))
		response.FailWithMessage("移除收藏失败", c)
		return
	}
	response.OkWithMessage("移除收藏成功", c)
}

// GenerateAIScript
// @Tags      AutoVideo
// @Summary   生成AI文案
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.AIScriptRequest  true  "AI文案生成请求"
// @Success   200   {object}  response.Response{data=ai.AIScriptResponse}  "生成成功"
// @Router    /ai/video/auto/script [post]
func (a *AutoVideoApi) GenerateAIScript(c *gin.Context) {
	var scriptReq request.AIScriptRequest
	if err := c.ShouldBindJSON(&scriptReq); err != nil {
		response.FailWithMessage("参数错误"+err.Error(), c)
		return
	}

	// 设置默认提示词模板（如果未提供）
	if scriptReq.Prompt == "" {
		response.FailWithMessage("请输入提示词", c)
		return
	}

	result, err := autoVideoService.GenerateAIScript(scriptReq)
	if err != nil {
		global.GVA_LOG.Error("生成AI文案失败!", zap.Error(err))
		response.FailWithMessage("生成AI文案失败: "+err.Error(), c)
		return
	}
	response.OkWithData(result, c)
}

// SaveStickerTemplate
// @Tags      AutoVideo
// @Summary   保存贴纸模板
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.StickerTemplateParams  true  "贴纸模板参数"
// @Success   200   {object}  response.Response{msg=string}  "保存成功"
// @Router    /ai/video/auto/sticker-template [post]
func (a *AutoVideoApi) SaveStickerTemplate(c *gin.Context) {
	var templateParams request.StickerTemplateParams
	if err := c.ShouldBindJSON(&templateParams); err != nil {
		response.FailWithMessage("参数错误"+err.Error(), c)
		return
	}

	userId := utils.GetUserID(c)
	err := autoVideoService.SaveStickerTemplate(templateParams, userId)
	if err != nil {
		global.GVA_LOG.Error("保存贴纸模板失败!", zap.Error(err))
		response.FailWithMessage("保存贴纸模板失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("保存成功", c)
}

// ListStickerTemplates
// @Tags      AutoVideo
// @Summary   获取贴纸模板列表
// @Security  ApiKeyAuth
// @Produce   application/json
// @Param     page      query    int     true  "页码"
// @Param     pageSize  query    int     true  "每页数量"
// @Param     imageId   query    int     false "图片ID过滤"
// @Success   200       {object}  response.Response{data=response.PageResult,msg=string}  "获取成功"
// @Router    /ai/video/auto/sticker-template [get]
func (a *AutoVideoApi) ListStickerTemplates(c *gin.Context) {
	var pageInfo request.AutoVideoTaskListParams
	if err := c.ShouldBindQuery(&pageInfo); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取imageId参数
	var imageId *uint
	if imageIdStr := c.Query("imageId"); imageIdStr != "" {
		if id, err := strconv.ParseUint(imageIdStr, 10, 32); err == nil {
			imageIdUint := uint(id)
			imageId = &imageIdUint
		}
	}

	userId := utils.GetUserID(c)
	list, total, err := autoVideoService.ListStickerTemplates(pageInfo, userId, imageId)
	if err != nil {
		global.GVA_LOG.Error("获取贴纸模板列表失败!", zap.Error(err))
		response.FailWithMessage("获取贴纸模板列表失败: "+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// DeleteStickerTemplate
// @Tags      AutoVideo
// @Summary   删除贴纸模板
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      ai.StickerTemplate  true  "模板ID"
// @Success   200   {object}  response.Response{msg=string}  "删除成功"
// @Router    /ai/video/auto/sticker-template [delete]
func (a *AutoVideoApi) DeleteStickerTemplate(c *gin.Context) {
	var template ai.StickerTemplate
	if err := c.ShouldBindJSON(&template); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := autoVideoService.DeleteStickerTemplate(template); err != nil {
		global.GVA_LOG.Error("删除贴纸模板失败!", zap.Error(err))
		response.FailWithMessage("删除贴纸模板失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}
