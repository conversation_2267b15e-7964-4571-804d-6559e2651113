package ai

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AutoImageTextApi struct{}

// SubmitAutoImageTextTask
// @Tags      AutoImageText
// @Summary   提交批量图文生成任务
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.AutoImageTextTaskParams  true  "图文生成任务参数"
// @Success   200   {object}  response.Response{data=string}  "提交成功"
// @Router    /ai/imagetext/auto/submit [post]
func (a *AutoImageTextApi) SubmitAutoImageTextTask(c *gin.Context) {
	var taskParams request.AutoImageTextTaskParams
	if err := c.ShouldBindJSON(&taskParams); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userId := utils.GetUserID(c)
	taskId, err := autoImageTextService.SubmitAutoImageTextTask(taskParams, userId)
	if err != nil {
		global.GVA_LOG.Error("提交批量图文生成任务失败!", zap.Error(err))
		response.FailWithMessage("提交任务失败: "+err.Error(), c)
		return
	}

	response.OkWithData(taskId, c)
}

// GetAutoImageTextTaskStatus
// @Tags      AutoImageText
// @Summary   获取图文生成任务状态
// @Security  ApiKeyAuth
// @Produce   application/json
// @Param     taskId   path      string  true  "任务ID"
// @Success   200      {object}  response.Response{data=ai.AutoImageTextTaskStatus}  "获取成功"
// @Router    /ai/imagetext/auto/status/{taskId} [get]
func (a *AutoImageTextApi) GetAutoImageTextTaskStatus(c *gin.Context) {
	taskId := c.Param("taskId")
	if taskId == "" {
		response.FailWithMessage("任务ID不能为空", c)
		return
	}

	taskStatus, err := autoImageTextService.GetAutoImageTextTaskStatus(taskId)
	if err != nil {
		global.GVA_LOG.Error("获取图文生成任务状态失败!", zap.Error(err))
		response.FailWithMessage("获取任务状态失败: "+err.Error(), c)
		return
	}
	response.OkWithData(taskStatus, c)
}

// ListAutoImageTextTasks
// @Tags      AutoImageText
// @Summary   获取图文生成任务列表
// @Security  ApiKeyAuth
// @Produce   application/json
// @Param     page      query    int     true  "页码"
// @Param     pageSize  query    int     true  "每页数量"
// @Param     taskName  query    string  false "任务名称"
// @Param     status    query    int     false "状态"
// @Success   200       {object}  response.Response{data=response.PageResult,msg=string}  "获取成功"
// @Router    /ai/imagetext/auto/list [get]
func (a *AutoImageTextApi) ListAutoImageTextTasks(c *gin.Context) {
	var pageInfo request.AutoImageTextTaskListParams
	if err := c.ShouldBindQuery(&pageInfo); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := autoImageTextService.ListAutoImageTextTasks(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取图文生成任务列表失败!", zap.Error(err))
		response.FailWithMessage("获取任务列表失败: "+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// DeleteAutoImageTextTask
// @Tags      AutoImageText
// @Summary   删除图文生成任务
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      ai.AutoImageTextTask  true  "任务ID"
// @Success   200   {object}  response.Response{msg=string}  "删除成功"
// @Router    /ai/imagetext/auto/delete [delete]
func (a *AutoImageTextApi) DeleteAutoImageTextTask(c *gin.Context) {
	var task ai.AutoImageTextTask
	if err := c.ShouldBindJSON(&task); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := autoImageTextService.DeleteAutoImageTextTask(task); err != nil {
		global.GVA_LOG.Error("删除图文生成任务失败!", zap.Error(err))
		response.FailWithMessage("删除任务失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}
