package ai

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DigitalHumanApi struct{}

// CreateDigitalHumanTask 创建数字人视频任务
// @Tags DigitalHuman
// @Summary 创建数字人视频任务
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.DigitalHumanTaskRequest true "数字人视频任务参数"
// @Success 200 {object} response.Response{data=response.DigitalHumanTaskResponse} "创建成功"
// @Router /ai/digital-human/create [post]
func (api *DigitalHumanApi) CreateDigitalHumanTask(c *gin.Context) {
	var req request.DigitalHumanTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取用户ID
	userId := utils.GetUserID(c)

	// 创建任务
	res, err := service.ServiceGroupApp.AiServiceGroup.DigitalHumanService.CreateDigitalHumanTask(req, userId)
	if err != nil {
		global.GVA_LOG.Error("创建数字人视频任务失败!", zap.Error(err))
		response.FailWithMessage("创建任务失败: "+err.Error(), c)
		return
	}

	response.OkWithData(res, c)
}

// GetDigitalHumanTaskStatus 获取数字人视频任务状态
// @Tags DigitalHuman
// @Summary 获取数字人视频任务状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param taskId path string true "任务ID"
// @Success 200 {object} response.Response{data=ai.DigitalHumanTask} "获取成功"
// @Router /ai/digital-human/status/{taskId} [get]
func (api *DigitalHumanApi) GetDigitalHumanTaskStatus(c *gin.Context) {
	taskId := c.Param("taskId")
	if taskId == "" {
		response.FailWithMessage("任务ID不能为空", c)
		return
	}

	task, err := service.ServiceGroupApp.AiServiceGroup.DigitalHumanService.GetDigitalHumanTaskStatus(taskId)
	if err != nil {
		global.GVA_LOG.Error("获取数字人视频任务状态失败!", zap.Error(err))
		response.FailWithMessage("获取任务状态失败: "+err.Error(), c)
		return
	}

	response.OkWithData(task, c)
}

// GetDigitalHumanTaskList 获取数字人视频任务列表
// @Tags DigitalHuman
// @Summary 获取数字人视频任务列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} response.Response{data=response.PageResult} "获取成功"
// @Router /ai/digital-human/list [get]
func (api *DigitalHumanApi) GetDigitalHumanTaskList(c *gin.Context) {
	var req request.DigitalHumanTaskListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取用户ID
	userId := utils.GetUserID(c)

	tasks, total, err := service.ServiceGroupApp.AiServiceGroup.DigitalHumanService.GetDigitalHumanTaskList(req, userId)
	if err != nil {
		global.GVA_LOG.Error("获取数字人视频任务列表失败!", zap.Error(err))
		response.FailWithMessage("获取任务列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     tasks,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// CreateHeygemDigitalHumanTask 创建HeyGem数字人视频任务
// @Tags DigitalHuman
// @Summary 创建HeyGem数字人视频任务
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.HeygemDigitalHumanTaskRequest true "HeyGem数字人视频任务参数"
// @Success 200 {object} response.Response{data=response.DigitalHumanTaskResponse} "创建成功"
// @Router /ai/digital-human/heygem/create [post]
func (api *DigitalHumanApi) CreateHeygemDigitalHumanTask(c *gin.Context) {
	var req request.HeygemDigitalHumanTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取用户ID
	userId := utils.GetUserID(c)

	// 创建HeyGem任务
	res, err := service.ServiceGroupApp.AiServiceGroup.DigitalHumanService.CreateHeygemDigitalHumanTask(req, userId)
	if err != nil {
		global.GVA_LOG.Error("创建HeyGem数字人视频任务失败!", zap.Error(err))
		response.FailWithMessage("创建HeyGem任务失败: "+err.Error(), c)
		return
	}

	response.OkWithData(res, c)
}

// GetHeygemTaskList 获取HeyGem数字人视频任务列表
// @Tags DigitalHuman
// @Summary 获取HeyGem数字人视频任务列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} response.Response{data=response.PageResult} "获取成功"
// @Router /ai/digital-human/heygem/list [get]
func (api *DigitalHumanApi) GetHeygemTaskList(c *gin.Context) {
	var req request.DigitalHumanTaskListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取用户ID
	userId := utils.GetUserID(c)

	// 获取HeyGem任务列表（只获取工作流类型为3的任务）
	tasks, total, err := service.ServiceGroupApp.AiServiceGroup.DigitalHumanService.GetHeygemTaskList(req, userId)
	if err != nil {
		global.GVA_LOG.Error("获取HeyGem数字人视频任务列表失败!", zap.Error(err))
		response.FailWithMessage("获取HeyGem任务列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     tasks,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// GetHeygemTaskStatus 获取HeyGem数字人视频任务状态
// @Tags DigitalHuman
// @Summary 获取HeyGem数字人视频任务状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param taskId path string true "任务ID"
// @Success 200 {object} response.Response{data=ai.DigitalHumanTask} "获取成功"
// @Router /ai/digital-human/heygem/status/{taskId} [get]
func (api *DigitalHumanApi) GetHeygemTaskStatus(c *gin.Context) {
	taskId := c.Param("taskId")
	if taskId == "" {
		response.FailWithMessage("任务ID不能为空", c)
		return
	}

	task, err := service.ServiceGroupApp.AiServiceGroup.DigitalHumanService.GetDigitalHumanTaskStatus(taskId)
	if err != nil {
		global.GVA_LOG.Error("获取HeyGem数字人视频任务状态失败!", zap.Error(err))
		response.FailWithMessage("获取HeyGem任务状态失败: "+err.Error(), c)
		return
	}

	response.OkWithData(task, c)
}
