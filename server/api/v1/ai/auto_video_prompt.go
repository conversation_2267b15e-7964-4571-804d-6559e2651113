package ai

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// GetPromptTemplates 获取提示词模板列表
// @Tags AutoVideo
// @Summary 获取提示词模板列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param category query string false "分类"
// @Success 200 {object} response.Response{data=[]ai.PromptTemplate,msg=string} "成功"
// @Router /ai/video/auto/prompt-templates [get]
func (autoVideoApi *AutoVideoApi) GetPromptTemplates(c *gin.Context) {
	userId := utils.GetUserID(c)

	type PromptTemplateWithCategory struct {
		ai.PromptTemplate
		CategoryVisibility int `json:"category_visibility"`
	}
	var promptTemplates []PromptTemplateWithCategory

	// 获取查询参数
	page := utils.StringToInt(c.DefaultQuery("page", "1"))
	pageSize := utils.StringToInt(c.DefaultQuery("pageSize", "10"))
	category := c.Query("category")

	// 构建查询条件
	query := global.GVA_DB.Model(&ai.PromptTemplate{}).
		Select("ai_prompt_templates.*, ai_prompt_categories.visibility as category_visibility").
		Joins("left join ai_prompt_categories on ai_prompt_templates.category_id = ai_prompt_categories.id").
		Where("(ai_prompt_templates.user_id = ?) OR (ai_prompt_categories.visibility = 0)", userId)
	if category != "" {
		query = query.Where("ai_prompt_templates.category_id = ?", category)
	}

	// 计算总数
	var total int64
	query.Count(&total)

	// 分页查询
	err := query.Offset((page - 1) * pageSize).Limit(pageSize).Find(&promptTemplates).Error
	if err != nil {
		global.GVA_LOG.Error("获取模板列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	// 返回分页数据
	response.OkWithDetailed(response.PageResult{
		List:     promptTemplates,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, "获取成功", c)
}

// CreatePromptTemplate 创建提示词模板
// @Tags AutoVideo
// @Summary 创建提示词模板
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body ai.PromptTemplate true "提示词模板信息"
// @Success 200 {object} response.Response{data=ai.PromptTemplate,msg=string} "成功"
// @Router /ai/video/auto/prompt-templates [post]
func (autoVideoApi *AutoVideoApi) CreatePromptTemplate(c *gin.Context) {
	var promptTemplate ai.PromptTemplate
	err := c.ShouldBindJSON(&promptTemplate)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置创建者ID
	userId := utils.GetUserID(c)
	promptTemplate.UserID = userId

	err = global.GVA_DB.Create(&promptTemplate).Error
	if err != nil {
		global.GVA_LOG.Error("创建模板失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
		return
	}
	response.OkWithDetailed(promptTemplate, "创建成功", c)
}

// UpdatePromptTemplate 更新提示词模板
// @Tags AutoVideo
// @Summary 更新提示词模板
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path string true "模板ID"
// @Param data body ai.PromptTemplate true "提示词模板信息"
// @Success 200 {object} response.Response{msg=string} "成功"
// @Router /ai/video/auto/prompt-templates/{id} [put]
func (autoVideoApi *AutoVideoApi) UpdatePromptTemplate(c *gin.Context) {
	var promptTemplate ai.PromptTemplate
	err := c.ShouldBindJSON(&promptTemplate)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 0)
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	// 检查权限
	userId := utils.GetUserID(c)
	var existTemplate ai.PromptTemplate
	err = global.GVA_DB.Where("id = ? AND user_id = ?", id, userId).First(&existTemplate).Error
	if err != nil {
		response.FailWithMessage("未找到模板或无权限", c)
		return
	}

	// 更新模板
	updates := map[string]interface{}{
		"name":        promptTemplate.Name,
		"template":    promptTemplate.Template,
		"category_id": promptTemplate.CategoryID,
	}
	err = global.GVA_DB.Model(&ai.PromptTemplate{}).Where("id = ?", id).Updates(updates).Error
	if err != nil {
		global.GVA_LOG.Error("更新模板失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// DeletePromptTemplate 删除提示词模板
// @Tags AutoVideo
// @Summary 删除提示词模板
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path string true "模板ID"
// @Success 200 {object} response.Response{msg=string} "成功"
// @Router /ai/video/auto/prompt-templates/{id} [delete]
func (autoVideoApi *AutoVideoApi) DeletePromptTemplate(c *gin.Context) {
	// 获取路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 0)
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	// 检查权限
	userId := utils.GetUserID(c)
	var existTemplate ai.PromptTemplate
	err = global.GVA_DB.Where("id = ? AND user_id = ?", id, userId).First(&existTemplate).Error
	if err != nil {
		response.FailWithMessage("未找到模板或无权限", c)
		return
	}

	// 删除模板
	err = global.GVA_DB.Delete(&ai.PromptTemplate{}, id).Error
	if err != nil {
		global.GVA_LOG.Error("删除模板失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// GetPromptCategories 获取提示词模板分类列表
// @Tags AutoVideo
// @Summary 获取提示词模板分类列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]ai.PromptCategory,msg=string} "成功"
// @Router /ai/video/auto/prompt-categories [get]
func (autoVideoApi *AutoVideoApi) GetPromptCategories(c *gin.Context) {
	userId := utils.GetUserID(c)
	var categories []ai.PromptCategory

	// 构建查询条件
	query := global.GVA_DB.Model(&ai.PromptCategory{})
	query = query.Where("(user_id = ?) OR (visibility = 0)", userId)

	// 查询分类列表
	err := query.Find(&categories).Error
	if err != nil {
		global.GVA_LOG.Error("获取分类列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(categories, "获取成功", c)
}

// CreatePromptCategory 创建提示词模板分类
// @Tags AutoVideo
// @Summary 创建提示词模板分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body ai.PromptCategory true "提示词模板分类信息"
// @Success 200 {object} response.Response{data=ai.PromptCategory,msg=string} "成功"
// @Router /ai/video/auto/prompt-categories [post]
func (autoVideoApi *AutoVideoApi) CreatePromptCategory(c *gin.Context) {
	var category ai.PromptCategory
	err := c.ShouldBindJSON(&category)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置创建者ID
	userId := utils.GetUserID(c)
	category.UserID = userId

	err = global.GVA_DB.Create(&category).Error
	if err != nil {
		global.GVA_LOG.Error("创建分类失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
		return
	}
	response.OkWithDetailed(category, "创建成功", c)
}

// UpdatePromptCategory 更新提示词模板分类
// @Tags AutoVideo
// @Summary 更新提示词模板分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path string true "分类ID"
// @Param data body ai.PromptCategory true "提示词模板分类信息"
// @Success 200 {object} response.Response{msg=string} "成功"
// @Router /ai/video/auto/prompt-categories/{id} [put]
func (autoVideoApi *AutoVideoApi) UpdatePromptCategory(c *gin.Context) {
	var category ai.PromptCategory
	err := c.ShouldBindJSON(&category)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 0)
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	// 检查权限
	userId := utils.GetUserID(c)
	var existCategory ai.PromptCategory
	err = global.GVA_DB.Where("id = ? AND user_id = ?", id, userId).First(&existCategory).Error
	if err != nil {
		response.FailWithMessage("未找到分类或无权限", c)
		return
	}

	// 更新分类
	updates := map[string]interface{}{
		"name":       category.Name,
		"visibility": category.Visibility,
	}
	err = global.GVA_DB.Model(&ai.PromptCategory{}).Where("id = ?", id).Updates(updates).Error
	if err != nil {
		global.GVA_LOG.Error("更新分类失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// DeletePromptCategory 删除提示词模板分类
// @Tags AutoVideo
// @Summary 删除提示词模板分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path string true "分类ID"
// @Success 200 {object} response.Response{msg=string} "成功"
// @Router /ai/video/auto/prompt-categories/{id} [delete]
func (autoVideoApi *AutoVideoApi) DeletePromptCategory(c *gin.Context) {
	// 获取路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 0)
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	// 检查权限
	userId := utils.GetUserID(c)
	var existCategory ai.PromptCategory
	err = global.GVA_DB.Where("id = ? AND user_id = ?", id, userId).First(&existCategory).Error
	if err != nil {
		response.FailWithMessage("未找到分类或无权限", c)
		return
	}

	// 删除分类
	err = global.GVA_DB.Delete(&ai.PromptCategory{}, id).Error
	if err != nil {
		global.GVA_LOG.Error("删除分类失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}

	response.OkWithMessage("删除成功", c)
}
