package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

type VideoMultiLensApi struct{}

// CreateMultiLensTask
// @Tags      VideoMultiLens
// @Summary   创建多镜头剪辑任务
// @Security  ApiKeyAuth
// @accept    multipart/form-data
// @Produce   application/json
// @Param     taskName  formData  string  true   "任务名称"
// @Param     bgmFile   formData  file    false  "BGM文件"
// @Param     bgmVolume formData  int     true   "BGM音量"
// @Success   200       {object}  response.Response{data=aiResp.MultiLensTaskResponse,msg=string}  "创建成功"
// @Router    /ai/video/multi-lens/create [post]
func (api *VideoMultiLensApi) CreateMultiLensTask(c *gin.Context) {
	// 1. 解析基本信息
	taskName := c.PostForm("taskName")
	if taskName == "" {
		response.FailWithMessage("任务名称不能为空", c)
		return
	}

	// 2. 创建请求对象
	req := &request.CreateMultiLensRequest{
		Step1: request.Step1{
			TaskName: taskName,
			BGM: request.BGM{
				Volume: utils.StringToFloat64(c.PostForm("bgmVolume")),
				Format: c.PostForm("bgmFormat"),
			},
			Lenses: make([]request.Lens, 0),
		},
	}

	// 3. 处理BGM文件
	bgmFile, err := c.FormFile("bgmFile")
	if err == nil && bgmFile != nil {
		// 创建临时目录
		tempDir := filepath.Join(global.GVA_CONFIG.Local.Path, "temp")
		if err := utils.CreateDir(tempDir); err != nil {
			response.FailWithMessage(fmt.Sprintf("创建临时目录失败: %v", err), c)
			return
		}

		// 生成唯一文件名，避免文件名冲突
		uniqueFileName := uuid.New().String() + filepath.Ext(bgmFile.Filename)
		tempPath := filepath.Join(tempDir, uniqueFileName)

		if err := c.SaveUploadedFile(bgmFile, tempPath); err != nil {
			response.FailWithMessage(fmt.Sprintf("保存BGM文件失败: %v", err), c)
			return
		}
		req.Step1.BGM.MusicFile = tempPath
	}

	// 4. 处理镜头文件
	form, err := c.MultipartForm()
	if err != nil {
		response.FailWithMessage(fmt.Sprintf("解析表单失败: %v", err), c)
		return
	}

	// 遍历所有镜头
	for i := 0; ; i++ {
		indexKey := fmt.Sprintf("lens[%d].index", i)
		if _, exists := form.Value[indexKey]; !exists {
			break
		}

		lens := request.Lens{
			Index:     utils.StringToInt(c.PostForm(fmt.Sprintf("lens[%d].index", i))),
			Narration: c.PostForm(fmt.Sprintf("lens[%d].narration", i)),
			From:      utils.StringToFloat64(c.PostForm(fmt.Sprintf("lens[%d].from", i))),
			To:        utils.StringToFloat64(c.PostForm(fmt.Sprintf("lens[%d].to", i))),
		}

		// 处理镜头视频文件
		file, err := c.FormFile(fmt.Sprintf("lens[%d].material", i))
		if err == nil && file != nil {
			// 创建临时目录
			tempDir := filepath.Join(global.GVA_CONFIG.Local.Path, "temp")
			if err := utils.CreateDir(tempDir); err != nil {
				response.FailWithMessage(fmt.Sprintf("创建临时目录失败: %v", err), c)
				return
			}

			// 生成唯一文件名，避免文件名冲突
			uniqueFileName := uuid.New().String() + filepath.Ext(file.Filename)
			tempPath := filepath.Join(tempDir, uniqueFileName)

			if err := c.SaveUploadedFile(file, tempPath); err != nil {
				response.FailWithMessage(fmt.Sprintf("保存镜头视频文件失败: %v", err), c)
				return
			}
			lens.Material = tempPath
		}

		req.Step1.Lenses = append(req.Step1.Lenses, lens)
	}

	// 5. 处理其他参数
	req.Step2 = request.Step2{
		BannerText: c.PostForm("bannerText"),
		Font: request.Font{
			Style:    c.PostForm("fontStyle"),
			Color:    c.PostForm("fontColor"),
			Size:     utils.StringToInt(c.PostForm("fontSize")),
			Position: c.PostForm("fontPosition"),
		},
		FlowerFont: request.FlowerFont{
			Enable: c.PostForm("flowerFontEnable") == "true",
			Style:  c.PostForm("flowerFontStyle"),
		},
	}

	req.Step3 = request.Step3{
		VideoSize:      utils.StringToInt(c.PostForm("videoSize")),
		VideoNum:       utils.StringToInt(c.PostForm("videoNum")),
		RandTransition: c.PostForm("randTransition") == "true",
		RandFilter:     c.PostForm("randFilter") == "true",
		RandEffects:    c.PostForm("randEffects") == "true",
	}

	// 添加对转场子类型的处理
	if req.Step3.RandTransition == false {
		req.Step3.TransitionSubType = c.PostForm("transitionSubType")
	}

	// 添加对滤镜子类型的处理
	if req.Step3.RandFilter == false {
		req.Step3.FilterSubType = c.PostForm("filterSubType")
	}

	// 添加对特效子类型的处理
	if req.Step3.RandEffects == false {
		req.Step3.EffectSubType = c.PostForm("effectSubType")
	}

	// 6. 调用服务创建任务
	videoMultiLensService := service.ServiceGroupApp.AiServiceGroup.VideoMultiLensService
	result, err := videoMultiLensService.CreateMultiLensTask(req, utils.GetUserID(c))
	if err != nil {
		global.GVA_LOG.Error("创建任务失败", zap.Error(err))
		response.FailWithMessage("创建任务失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(result, "创建成功", c)
}

// StreamTaskProgress
// @Tags      VideoMultiLens
// @Summary   流式获取任务进度
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   text/event-stream
// @Param     taskId   query     uint   true   "任务ID"
// @Success   200      {object}  string  "事件流"
// @Router    /ai/video/multi-lens/stream [get]
func (api *VideoMultiLensApi) StreamTaskProgress(c *gin.Context) {
	// 设置 SSE 相关头信息
	c.Writer.Header().Set("Content-Type", "text/event-stream")
	c.Writer.Header().Set("Cache-Control", "no-cache")
	c.Writer.Header().Set("Connection", "keep-alive")
	c.Writer.Header().Set("X-Accel-Buffering", "no") // 防止 Nginx 缓存

	// 获取任务ID
	taskIdStr := c.Query("taskId")
	if taskIdStr == "" {
		sendEvent(c, "error", gin.H{"message": "任务ID不能为空"})
		return
	}

	taskId := utils.StringToUint(taskIdStr)
	if taskId == 0 {
		sendEvent(c, "error", gin.H{"message": "无效的任务ID"})
		return
	}

	// 检查用户权限，验证此任务是否属于当前用户
	userId := utils.GetUserID(c)
	videoMultiLensService := service.ServiceGroupApp.AiServiceGroup.VideoMultiLensService
	task, err := videoMultiLensService.GetTask(taskId)
	if err != nil || task.SysUserId != userId {
		sendEvent(c, "error", gin.H{"message": "无权访问该任务"})
		return
	}

	// 发送初始状态
	sendInitialState(c, task)

	// 获取上次发送的进度ID
	lastProgressId := int64(-1)

	// 设置轮询间隔
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	// 设置连接超时时间(20分钟)
	timeout := time.After(20 * time.Minute)

	// 处理客户端断开连接
	disconnected := c.Request.Context().Done()

	// 创建Redis键
	progressKey := fmt.Sprintf("task:progress:%d", taskId)

	// 任务是否完成的标志
	completed := false

	for {
		select {
		case <-ticker.C:
			// 1. 获取最新的任务状态
			task, err := videoMultiLensService.GetTask(taskId)
			if err != nil {
				sendEvent(c, "error", gin.H{"message": "获取任务状态失败"})
				return
			}

			// 2. 获取从上次发送后新增的进度消息
			newProgressMessages, err := getNewProgressMessages(progressKey, lastProgressId)
			if err != nil {
				global.GVA_LOG.Error("获取进度消息失败", zap.Error(err))
			} else if len(newProgressMessages) > 0 {
				// 发送每条新进度消息
				for _, progressMsg := range newProgressMessages {
					sendEvent(c, "progress", progressMsg)
					lastProgressId = progressMsg.ID
				}
			}

			// 3. 如果任务已完成或失败，并且之前未发送完成事件，则发送完成事件
			if (task.Status == 2 || task.Status == 3) && !completed {
				completed = true
				if task.Status == 2 {
					// 任务成功
					sendEvent(c, "complete", gin.H{
						"taskId":    task.ID,
						"outputUrl": task.OutputUrl,
						"duration":  task.Duration,
						"message":   "任务已完成",
					})
				} else {
					// 任务失败
					sendEvent(c, "error", gin.H{
						"message": task.ErrorMessage,
					})
				}
				return // 任务已完成，结束连接
			}

		case <-disconnected:
			// 客户端断开连接
			return

		case <-timeout:
			// 连接超时
			sendEvent(c, "error", gin.H{"message": "连接超时"})
			return
		}
	}
}

// ProgressMessage 进度消息结构
type ProgressMessage struct {
	ID      int64  `json:"id"`
	Message string `json:"message"`
	Stage   string `json:"stage"`
	Time    int64  `json:"time"`
}

// getNewProgressMessages 获取新的进度消息
func getNewProgressMessages(key string, lastId int64) ([]ProgressMessage, error) {
	// 从Redis中获取新的进度消息
	messages, err := global.GVA_REDIS.LRange(context.Background(), key, lastId+1, -1).Result()
	if err != nil {
		return nil, err
	}

	result := make([]ProgressMessage, 0, len(messages))
	for i, msg := range messages {
		var progressData map[string]interface{}
		if err := json.Unmarshal([]byte(msg), &progressData); err != nil {
			global.GVA_LOG.Error("解析进度消息失败", zap.Error(err))
			continue
		}

		// 创建进度消息对象
		progressMsg := ProgressMessage{
			ID:      lastId + int64(i) + 1,
			Message: fmt.Sprintf("%v", progressData["message"]),
			Stage:   fmt.Sprintf("%v", progressData["stage"]),
		}

		// 尝试解析时间戳
		if timeValue, ok := progressData["time"].(float64); ok {
			progressMsg.Time = int64(timeValue)
		}

		result = append(result, progressMsg)
	}

	return result, nil
}

// 辅助函数 - 发送SSE事件
func sendEvent(c *gin.Context, event string, data interface{}) {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		global.GVA_LOG.Error("序列化事件数据失败", zap.Error(err))
		return
	}

	// 写入事件类型
	c.Writer.Write([]byte("event: " + event + "\n"))
	// 写入数据
	c.Writer.Write([]byte("data: " + string(dataBytes) + "\n\n"))
	// 刷新缓冲区，确保数据发送出去
	c.Writer.Flush()
}

// 发送初始状态
func sendInitialState(c *gin.Context, task *ai.VideoMultiLensTask) {
	var step, status, message string

	switch task.Status {
	case 0: // 待处理
		step = "waiting"
		status = "pending"
		message = "任务正在排队中..."
	case 1: // 处理中
		step = "processing"
		status = "running"
		message = "视频正在处理中..."
	case 2: // 已完成
		step = "complete"
		status = "success"
		message = "视频处理完成"
	case 3: // 失败
		step = "error"
		status = "failed"
		message = task.ErrorMessage
	}

	// 发送当前状态
	sendEvent(c, "progress", gin.H{
		"id":      0,
		"message": message,
		"stage":   step,
		"status":  status,
		"time":    time.Now().Unix(),
	})

	// 如果已经完成，直接发送完成事件
	if task.Status == 2 {
		sendEvent(c, "complete", gin.H{
			"taskId":    task.ID,
			"outputUrl": task.OutputUrl,
			"duration":  task.Duration,
			"message":   "任务已完成",
		})
	} else if task.Status == 3 {
		sendEvent(c, "error", gin.H{
			"message": task.ErrorMessage,
		})
	}
}

// GetTaskStatus
// @Tags      VideoMultiLens
// @Summary   获取任务状态
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     taskId  path      uint                                  true  "任务ID"
// @Success   200     {object}  response.Response{data=ai.VideoMultiLensTask,msg=string}  "获取成功"
// @Router    /ai/video/multi-lens/status/{taskId} [get]
func (api *VideoMultiLensApi) GetTaskStatus(c *gin.Context) {
	var req request.VideoMultiLensTaskStatusRequest

	err := c.ShouldBindUri(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	task, err := videoMultiLensService.GetTask(req.TaskId)
	if err != nil {
		global.GVA_LOG.Error("获取任务状态失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(task, "获取成功", c)
}

// GetTaskList
// @Tags      VideoMultiLens
// @Summary   获取多镜头剪辑任务列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     page      query    int                                  true  "页码"
// @Param     pageSize  query    int                                  true  "每页数量"
// @Param     status    query    int                                  false "任务状态(0:待处理 1:处理中 2:已完成 3:失败)"
// @Param     taskName  query    string                               false "任务名称"
// @Param     startTime query    string                               false "开始时间(格式:YYYY-MM-DD)"
// @Param     endTime   query    string                               false "结束时间(格式:YYYY-MM-DD)"
// @Success   200       {object}  response.Response{data=aiResp.MultiLensTaskListResponse,msg=string}  "获取成功"
// @Router    /ai/video/multi-lens/list [get]
func (api *VideoMultiLensApi) GetTaskList(c *gin.Context) {
	var req request.VideoMultiLensTaskListRequest

	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 参数验证
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取当前用户ID
	userId := utils.GetUserID(c)

	// 调用服务获取任务列表
	listResponse, err := videoMultiLensService.GetTaskList(&req, userId)
	if err != nil {
		global.GVA_LOG.Error("获取多镜头剪辑任务列表失败!", zap.Error(err))
		response.FailWithMessage("获取列表失败", c)
		return
	}

	response.OkWithDetailed(listResponse, "获取成功", c)
}

// DeleteTask
// @Tags      VideoMultiLens
// @Summary   删除多镜头剪辑任务
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data      body      request.VideoMultiLensTaskDelete          true  "任务ID"
// @Success   200       {object}  response.Response{msg=string}        "删除成功"
// @Router    /ai/video/multi-lens/delete [delete]
func (api *VideoMultiLensApi) DeleteTask(c *gin.Context) {
	var req request.VideoMultiLensTaskDelete

	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userId := utils.GetUserID(c)

	// 调用服务删除任务
	err = videoMultiLensService.DeleteTask(req.ID, userId)
	if err != nil {
		global.GVA_LOG.Error("删除多镜头剪辑任务失败!", zap.Error(err))
		response.FailWithMessage(fmt.Sprintf("删除失败: %v", err), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}
