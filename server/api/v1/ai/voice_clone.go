package ai

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type VoiceCloneApi struct{}

// CreateVoiceCloneTask 创建人声克隆任务
// @Tags VoiceClone
// @Summary 创建人声克隆任务
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce application/json
// @Param text formData string true "要转换的文本"
// @Param language formData string false "语言，默认zh"
// @Param speed formData number false "语速，默认1.0"
// @Param temperature formData number false "温度，默认1.0"
// @Param topP formData number false "TopP，默认0.8"
// @Param topK formData int false "TopK，默认30"
// @Param repetitionPenalty formData number false "重复惩罚，默认10"
// @Param lengthPenalty formData number false "长度惩罚，默认0"
// @Param numBeams formData int false "束搜索数量，默认3"
// @Param maxMelTokens formData int false "最大mel tokens，默认600"
// @Param sentenceSplit formData string false "句子分割方式，默认auto"
// @Param referenceAudio formData file true "参考音频文件"
// @Success 200 {object} response.Response{data=response.VoiceCloneTaskResponse} "创建成功"
// @Router /ai/voiceClone/create [post]
func (api *VoiceCloneApi) CreateVoiceCloneTask(c *gin.Context) {
	var req request.VoiceCloneTaskRequest
	if err := c.ShouldBind(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取用户ID
	userId := utils.GetUserID(c)

	// 创建任务
	res, err := service.ServiceGroupApp.AiServiceGroup.VoiceCloneService.CreateVoiceCloneTask(req, userId)
	if err != nil {
		global.GVA_LOG.Error("创建人声克隆任务失败!", zap.Error(err))
		response.FailWithMessage("创建任务失败: "+err.Error(), c)
		return
	}

	response.OkWithData(res, c)
}

// GetVoiceCloneTaskStatus 获取人声克隆任务状态
// @Tags VoiceClone
// @Summary 获取人声克隆任务状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param taskId path string true "任务ID"
// @Success 200 {object} response.Response{data=ai.VoiceCloneTask} "获取成功"
// @Router /ai/voiceClone/status/{taskId} [get]
func (api *VoiceCloneApi) GetVoiceCloneTaskStatus(c *gin.Context) {
	taskId := c.Param("taskId")
	if taskId == "" {
		response.FailWithMessage("任务ID不能为空", c)
		return
	}

	// 注入音乐服务依赖
	voiceCloneService := service.ServiceGroupApp.AiServiceGroup.VoiceCloneService
	voiceCloneService.MusicService = &service.ServiceGroupApp.MediaServiceGroup.MusicService

	task, err := voiceCloneService.GetVoiceCloneTaskStatus(taskId)
	if err != nil {
		global.GVA_LOG.Error("获取人声克隆任务状态失败!", zap.Error(err))
		response.FailWithMessage("获取任务状态失败: "+err.Error(), c)
		return
	}

	response.OkWithData(task, c)
}

// GetVoiceCloneTaskList 获取人声克隆任务列表
// @Tags VoiceClone
// @Summary 获取人声克隆任务列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} response.Response{data=response.PageResult} "获取成功"
// @Router /ai/voiceClone/list [get]
func (api *VoiceCloneApi) GetVoiceCloneTaskList(c *gin.Context) {
	var req request.VoiceCloneTaskListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取用户ID
	userId := utils.GetUserID(c)

	tasks, total, err := service.ServiceGroupApp.AiServiceGroup.VoiceCloneService.GetVoiceCloneTaskList(req, userId)
	if err != nil {
		global.GVA_LOG.Error("获取人声克隆任务列表失败!", zap.Error(err))
		response.FailWithMessage("获取任务列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     tasks,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}
