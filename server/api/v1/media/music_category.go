package media

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	mediaModel "github.com/flipped-aurora/gin-vue-admin/server/model/media"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MediaMusicCategoryApi struct{}

// GetCategoryList 获取音乐分类列表
// @Tags Media.MusicCategory
// @Summary 获取音乐分类列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]mediaModel.MusicCategory,msg=string} "获取音乐分类列表"
// @Router /media/music/categories [get]
func (m *MediaMusicCategoryApi) GetCategoryList(c *gin.Context) {
	categoryList, err := musicCategoryService.GetCategoryList(c)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(categoryList, "获取成功", c)
}

// AddMusicCategory 添加/编辑音乐分类
// @Tags Media.MusicCategory
// @Summary 添加/编辑音乐分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body mediaModel.MusicCategory true "分类信息"
// @Success 200 {object} response.Response{msg=string} "添加/编辑音乐分类"
// @Router /media/music/categories [post]
func (m *MediaMusicCategoryApi) AddMusicCategory(c *gin.Context) {
	var category mediaModel.MusicCategory
	err := c.ShouldBindJSON(&category)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = musicCategoryService.AddOrUpdateMusicCategory(&category, c)
	if err != nil {
		global.GVA_LOG.Error("操作失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("操作成功", c)
}

// DeleteMusicCategory 删除音乐分类
// @Tags Media.MusicCategory
// @Summary 删除音乐分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "分类ID"
// @Success 200 {object} response.Response{msg=string} "删除音乐分类"
// @Router /media/music/categories/{id} [delete]
func (m *MediaMusicCategoryApi) DeleteMusicCategory(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}
	if err := musicCategoryService.DeleteMusicCategory(uint(id)); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// UpdateCategoryOrder 更新分类排序
// @Tags Media.MusicCategory
// @Summary 更新分类排序
// @accept application/json
// @Produce application/json
// @Param data body request.UpdateCategoryOrderRequest true "排序信息"
// @Success 200 {object} response.Response{msg=string} "更新分类排序"
// @Router /media/music/categories/order [post]
func (m *MediaMusicCategoryApi) UpdateCategoryOrder(c *gin.Context) {
	var req request.UpdateCategoryOrderRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = musicCategoryService.UpdateCategoryOrder(c, req.Pid, req.Order)
	if err != nil {
		global.GVA_LOG.Error("更新排序失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("更新排序成功", c)
}
