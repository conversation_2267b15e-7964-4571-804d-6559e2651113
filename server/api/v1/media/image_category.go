package media

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	mediaModel "github.com/flipped-aurora/gin-vue-admin/server/model/media"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MediaImageCategoryApi struct{}

// GetCategoryList 获取图片分类列表
// @Tags Media.ImageCategory
// @Summary 获取图片分类列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]mediaModel.ImageCategory,msg=string} "获取图片分类列表"
// @Router /media/image/categories [get]
func (m *MediaImageCategoryApi) GetCategoryList(c *gin.Context) {
	categoryList, err := imageCategoryService.GetCategoryList(c)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(categoryList, "获取成功", c)
}

// AddImageCategory 添加/编辑图片分类
// @Tags Media.ImageCategory
// @Summary 添加/编辑图片分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body mediaModel.ImageCategory true "分类信息"
// @Success 200 {object} response.Response{msg=string} "添加/编辑图片分类"
// @Router /media/image/categories [post]
func (m *MediaImageCategoryApi) AddImageCategory(c *gin.Context) {
	var category mediaModel.ImageCategory
	err := c.ShouldBindJSON(&category)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = imageCategoryService.AddOrUpdateImageCategory(&category, c)
	if err != nil {
		global.GVA_LOG.Error("操作失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("操作成功", c)
}

// DeleteImageCategory 删除图片分类
// @Tags Media.ImageCategory
// @Summary 删除图片分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "分类ID"
// @Success 200 {object} response.Response{msg=string} "删除图片分类"
// @Router /media/image/categories/{id} [delete]
func (m *MediaImageCategoryApi) DeleteImageCategory(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	err = imageCategoryService.DeleteImageCategory(uint(id))
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// UpdateCategoryOrder 更新分类排序
// @Tags Media.ImageCategory
// @Summary 更新分类排序
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body mediaModel.ImageCategory true "分类信息"
// @Success 200 {object} response.Response{msg=string} "更新分类排序"
// @Router /media/image/categories/order [post]
func (api *MediaImageCategoryApi) UpdateCategoryOrder(c *gin.Context) {
	var req struct {
		Pid   uint   `json:"pid"`
		Order []uint `json:"order"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	if err := imageCategoryService.UpdateCategoryOrder(c, req.Pid, req.Order); err != nil {
		global.GVA_LOG.Error("更新分类排序失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}
