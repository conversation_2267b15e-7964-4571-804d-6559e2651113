package media

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	mediaModel "github.com/flipped-aurora/gin-vue-admin/server/model/media"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MediaVideoCategoryApi struct{}

// GetCategoryList 获取视频分类列表
// @Tags Media.VideoCategory
// @Summary 获取视频分类列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]mediaModel.VideoCategory,msg=string} "获取视频分类列表"
// @Router /media/video/categories [get]
func (m *MediaVideoCategoryApi) GetCategoryList(c *gin.Context) {
	categoryList, err := videoCategoryService.GetCategoryList(c)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(categoryList, "获取成功", c)
}

// AddVideoCategory 添加/编辑视频分类
// @Tags Media.VideoCategory
// @Summary 添加/编辑视频分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body mediaModel.VideoCategory true "分类信息"
// @Success 200 {object} response.Response{msg=string} "添加/编辑视频分类"
// @Router /media/video/categories [post]
func (m *MediaVideoCategoryApi) AddVideoCategory(c *gin.Context) {
	var category mediaModel.VideoCategory
	err := c.ShouldBindJSON(&category)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = videoCategoryService.AddOrUpdateVideoCategory(&category, c)
	if err != nil {
		global.GVA_LOG.Error("操作失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("操作成功", c)
}

// DeleteVideoCategory 删除视频分类
// @Tags Media.VideoCategory
// @Summary 删除视频分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "分类ID"
// @Success 200 {object} response.Response{msg=string} "删除视频分类"
// @Router /media/video/categories/{id} [delete]
func (m *MediaVideoCategoryApi) DeleteVideoCategory(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}
	if err := videoCategoryService.DeleteVideoCategory(uint(id)); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// UpdateCategoryOrder 更新分类排序
// @Tags Media.VideoCategory
// @Summary 更新分类排序
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.UpdateCategoryOrderRequest true "排序信息"
// @Success 200 {object} response.Response{msg=string} "更新分类排序"
// @Router /media/video/categories/order [put]
func (m *MediaVideoCategoryApi) UpdateCategoryOrder(c *gin.Context) {
	var req request.UpdateCategoryOrderRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = videoCategoryService.UpdateCategoryOrder(c, req.Pid, req.Order)
	if err != nil {
		global.GVA_LOG.Error("更新排序失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("更新排序成功", c)
}
