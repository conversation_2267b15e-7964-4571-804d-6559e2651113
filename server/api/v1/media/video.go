package media

import (
	"strconv"
	"strings"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	mediaModel "github.com/flipped-aurora/gin-vue-admin/server/model/media"
	mediaReq "github.com/flipped-aurora/gin-vue-admin/server/model/media/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MediaVideoApi struct{}

// UploadVideo 上传视频
// @Tags Media.Video
// @Summary 上传视频文件
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce application/json
// @Param files formData file true "视频文件(支持多文件)"
// @Param names formData string false "视频名称(多个用逗号分隔，不传则使用文件名)"
// @Param classId formData int false "分类ID"
// @Success 200 {object} response.Response{data=[]mediaModel.Video,msg=string} "上传视频"
// @Router /media/video/upload [post]
func (api *MediaVideoApi) UploadVideo(c *gin.Context) {
	form, err := c.MultipartForm()
	if err != nil {
		global.GVA_LOG.Error("解析表单失败!", zap.Error(err))
		response.FailWithMessage("解析表单失败", c)
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		global.GVA_LOG.Error("没有上传文件!", zap.Error(err))
		response.FailWithMessage("请选择要上传的视频文件", c)
		return
	}

	global.GVA_LOG.Info("收到视频上传请求", zap.Int("文件数量", len(files)))

	names := c.PostForm("names")
	nameArr := []string{}
	if names != "" {
		nameArr = strings.Split(names, ",")
	}

	classId := c.DefaultPostForm("classId", "0")
	classIdUint, _ := strconv.Atoi(classId)

	userId := utils.GetUserID(c)

	var videos []*mediaModel.Video
	for i, header := range files {
		var name string
		if i < len(nameArr) && nameArr[i] != "" {
			name = nameArr[i]
		} else {
			// 使用文件名作为默认名称
			name = ""
		}

		video, err := videoService.UploadVideoFile(header, name, uint(classIdUint), userId)
		if err != nil {
			global.GVA_LOG.Error("上传视频失败!", zap.Error(err), zap.String("文件名", header.Filename))
			continue
		}
		videos = append(videos, video)
	}

	if len(videos) == 0 {
		response.FailWithMessage("所有视频上传失败", c)
		return
	} else if len(videos) < len(files) {
		response.OkWithDetailed(videos, "部分视频上传成功", c)
		return
	}

	response.OkWithDetailed(videos, "上传成功", c)
}

// GetVideoList 获取视频列表
// @Tags Media.Video
// @Summary 获取视频列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param keyword query string false "关键词"
// @Param categoryId query int false "分类ID"
// @Param mediaId query string false "媒资ID"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取视频列表"
// @Router /media/video/list [get]
func (api *MediaVideoApi) GetVideoList(c *gin.Context) {
	var pageInfo mediaReq.VideoSearch
	pageInfo.Page, _ = strconv.Atoi(c.DefaultQuery("page", "1"))
	pageInfo.PageSize, _ = strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	pageInfo.Keyword = c.Query("keyword")
	categoryId, _ := strconv.Atoi(c.DefaultQuery("categoryId", "0"))
	pageInfo.CategoryId = uint(categoryId)
	pageInfo.MediaId = c.Query("mediaId")

	userId := utils.GetUserID(c)
	list, total, err := videoService.GetVideoList(userId, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// DeleteVideo 删除视频
// @Tags Media.Video
// @Summary 删除视频
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "视频ID"
// @Success 200 {object} response.Response{msg=string} "删除视频"
// @Router /media/video/{id} [delete]
func (api *MediaVideoApi) DeleteVideo(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	userId := utils.GetUserID(c)
	err = videoService.DeleteVideo(uint(id), userId)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// EditVideoName 编辑视频名称
// @Tags Media.Video
// @Summary 编辑视频名称
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "视频ID"
// @Param data body map[string]string true "名称"
// @Success 200 {object} response.Response{msg=string} "编辑视频名称"
// @Router /media/video/{id}/name [patch]
func (api *MediaVideoApi) EditVideoName(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	var reqData struct {
		Name string `json:"name"`
	}
	if err := c.ShouldBindJSON(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	video := &mediaModel.Video{}
	video.GVA_MODEL.ID = uint(id)
	video.Name = reqData.Name

	err = videoService.EditVideoName(video)
	if err != nil {
		global.GVA_LOG.Error("编辑失败!", zap.Error(err))
		response.FailWithMessage("编辑失败", c)
		return
	}

	response.OkWithMessage("编辑成功", c)
}

// ImportVideoURL 导入视频URL
// @Tags Media.Video
// @Summary 导入视频URL
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body []mediaModel.Video true "视频列表"
// @Success 200 {object} response.Response{msg=string} "导入视频URL"
// @Router /media/video/import [post]
func (api *MediaVideoApi) ImportVideoURL(c *gin.Context) {
	var videos []mediaModel.Video
	err := c.ShouldBindJSON(&videos)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if len(videos) == 0 {
		response.FailWithMessage("没有视频数据", c)
		return
	}

	err = videoService.ImportVideoURLs(videos)
	if err != nil {
		global.GVA_LOG.Error("导入失败!", zap.Error(err))
		response.FailWithMessage("导入失败", c)
		return
	}

	response.OkWithMessage("导入成功", c)
}

// EditVideoCategory 修改视频分类
// @Tags Media.Video
// @Summary 修改视频分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "视频ID"
// @Param data body map[string]uint true "分类ID"
// @Success 200 {object} response.Response{msg=string} "修改视频分类"
// @Router /media/video/{id}/category [patch]
func (api *MediaVideoApi) EditVideoCategory(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	var reqData struct {
		CategoryId uint `json:"categoryId"`
	}
	if err := c.ShouldBindJSON(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	video := &mediaModel.Video{}
	video.GVA_MODEL.ID = uint(id)
	video.CategoryId = reqData.CategoryId

	err = videoService.EditVideoCategory(video)
	if err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败", c)
		return
	}

	response.OkWithMessage("修改成功", c)
}
