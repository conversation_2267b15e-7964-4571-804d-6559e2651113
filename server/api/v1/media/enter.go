package media

import "github.com/flipped-aurora/gin-vue-admin/server/service"

var (
	imageService               = service.ServiceGroupApp.MediaServiceGroup.ImageService
	imageCategoryService       = service.ServiceGroupApp.MediaServiceGroup.ImageCategoryService
	musicService               = service.ServiceGroupApp.MediaServiceGroup.MusicService
	musicCategoryService       = service.ServiceGroupApp.MediaServiceGroup.MusicCategoryService
	videoService               = service.ServiceGroupApp.MediaServiceGroup.VideoService
	videoCategoryService       = service.ServiceGroupApp.MediaServiceGroup.VideoCategoryService
	copywritingService         = service.ServiceGroupApp.MediaServiceGroup.CopywritingService
	copywritingCategoryService = service.ServiceGroupApp.MediaServiceGroup.CopywritingCategoryService
	topicService               = service.ServiceGroupApp.MediaServiceGroup.TopicService
	topicCategoryService       = service.ServiceGroupApp.MediaServiceGroup.TopicCategoryService
)

type ApiGroup struct {
	MediaImageApi
	MediaImageCategoryApi
	MediaMusicApi
	MediaMusicCategoryApi
	MediaVideoApi
	MediaVideoCategoryApi
	MediaCopywritingApi
	MediaCopywritingCategoryApi
	MediaTopicApi
	MediaTopicCategoryApi
	MediaApi
}
