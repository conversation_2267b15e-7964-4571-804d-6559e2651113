package media

import (
	"strconv"
	"strings"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	mediaModel "github.com/flipped-aurora/gin-vue-admin/server/model/media"
	mediaReq "github.com/flipped-aurora/gin-vue-admin/server/model/media/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type MediaTopicApi struct{}

// CreateTopic 创建话题
// @Tags Media.Topic
// @Summary 创建话题
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body mediaModel.Topic true "话题信息（包含 content, categoryId），content 支持换行批量添加"
// @Success 200 {object} response.Response{msg=string} "创建话题，包含创建和跳过的数量"
// @Router /media/topic/create [post]
func (api *MediaTopicApi) CreateTopic(c *gin.Context) {
	var req mediaModel.Topic
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userId := utils.GetUserID(c)
	contentLines := strings.Split(req.Content, "\n")

	var createdCount int
	var skippedCount int
	var firstError error

	for _, line := range contentLines {
		trimmedLine := strings.TrimSpace(line)
		if trimmedLine == "" {
			continue
		}

		var existingTopic mediaModel.Topic
		err := global.GVA_DB.Where(
			"content = ? AND category_id = ?", trimmedLine, req.CategoryId,
		).First(&existingTopic).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			global.GVA_LOG.Error("查询话题是否存在时出错", zap.String("content", trimmedLine), zap.Error(err))
			if firstError == nil {
				firstError = err
			}
			continue
		}

		if err == nil {
			skippedCount++
			global.GVA_LOG.Info("话题已存在，跳过创建", zap.String("content", trimmedLine))
			continue
		}

		topic := mediaModel.Topic{
			Content:    trimmedLine,
			CategoryId: req.CategoryId,
		}

		if err := topicService.CreateTopic(&topic, userId); err != nil {
			global.GVA_LOG.Error("创建话题失败!", zap.String("content", trimmedLine), zap.Error(err))
			if firstError == nil {
				firstError = err
			}
		} else {
			createdCount++
		}
	}

	var msg strings.Builder
	if createdCount > 0 {
		msg.WriteString("成功创建 " + strconv.Itoa(createdCount) + " 个话题。")
	}
	if skippedCount > 0 {
		if msg.Len() > 0 {
			msg.WriteString(" ")
		}
		msg.WriteString("跳过 " + strconv.Itoa(skippedCount) + " 个已存在的话题。")
	}

	if firstError != nil {
		finalMsg := "部分或全部话题处理失败"
		if msg.Len() > 0 {
			finalMsg = msg.String() + " 但处理过程中遇到错误: " + firstError.Error()
		} else {
			finalMsg += ": " + firstError.Error()
		}
		response.FailWithMessage(finalMsg, c)
		return
	}

	if createdCount == 0 && skippedCount == 0 {
		response.FailWithMessage("没有有效的话题内容被处理", c)
		return
	}

	response.OkWithMessage(msg.String(), c)
}

// GetTopicList 获取话题列表
// @Tags Media.Topic
// @Summary 获取话题列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param keyword query string false "关键词(搜索内容)"
// @Param categoryId query int false "分类ID"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取话题列表"
// @Router /media/topic/list [get]
func (api *MediaTopicApi) GetTopicList(c *gin.Context) {
	var pageInfo mediaReq.TopicSearch
	pageInfo.Page, _ = strconv.Atoi(c.DefaultQuery("page", "1"))
	pageInfo.PageSize, _ = strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	pageInfo.Keyword = c.Query("keyword")
	categoryId, _ := strconv.Atoi(c.DefaultQuery("categoryId", "0"))
	pageInfo.CategoryId = uint(categoryId)

	userId := utils.GetUserID(c)
	list, total, err := topicService.GetTopicList(userId, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// DeleteTopic 删除话题
// @Tags Media.Topic
// @Summary 删除话题
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "话题ID"
// @Success 200 {object} response.Response{msg=string} "删除话题"
// @Router /media/topic/{id} [delete]
func (api *MediaTopicApi) DeleteTopic(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	userId := utils.GetUserID(c)
	err = topicService.DeleteTopic(uint(id), userId)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// EditTopic 编辑话题
// @Tags Media.Topic
// @Summary 编辑话题
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "话题ID"
// @Param data body mediaModel.Topic true "话题信息（只包含 content）"
// @Success 200 {object} response.Response{msg=string} "编辑话题"
// @Router /media/topic/{id} [put]
func (api *MediaTopicApi) EditTopic(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	var topic mediaModel.Topic
	if err := c.ShouldBindJSON(&topic); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	topic.GVA_MODEL.ID = uint(id)
	topic.CreatorId = 0

	err = topicService.EditTopic(&topic)
	if err != nil {
		global.GVA_LOG.Error("编辑失败!", zap.Error(err))
		response.FailWithMessage("编辑失败", c)
		return
	}

	response.OkWithMessage("编辑成功", c)
}

// EditTopicCategory 修改话题分类
// @Tags Media.Topic
// @Summary 修改话题分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "话题ID"
// @Param data body map[string]uint true "分类ID, {\"categoryId\": 1}"
// @Success 200 {object} response.Response{msg=string} "修改话题分类"
// @Router /media/topic/{id}/category [patch]
func (api *MediaTopicApi) EditTopicCategory(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	var reqData struct {
		CategoryId uint `json:"categoryId"`
	}
	if err := c.ShouldBindJSON(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	topic := &mediaModel.Topic{}
	topic.GVA_MODEL.ID = uint(id)
	topic.CategoryId = reqData.CategoryId

	err = topicService.EditTopicCategory(topic)
	if err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败", c)
		return
	}

	response.OkWithMessage("修改成功", c)
}
