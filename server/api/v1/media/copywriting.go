package media

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	mediaModel "github.com/flipped-aurora/gin-vue-admin/server/model/media"
	mediaReq "github.com/flipped-aurora/gin-vue-admin/server/model/media/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MediaCopywritingApi struct{}

// CreateCopywriting 创建文案
// @Tags Media.Copywriting
// @Summary 创建文案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body mediaModel.Copywriting true "文案信息（包含 content, categoryId）"
// @Success 200 {object} response.Response{data=mediaModel.Copywriting,msg=string} "创建文案"
// @Router /media/copywriting/create [post]
func (api *MediaCopywritingApi) CreateCopywriting(c *gin.Context) {
	var copywriting mediaModel.Copywriting
	if err := c.ShouldBindJSON(&copywriting); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userId := utils.GetUserID(c)
	if err := copywritingService.CreateCopywriting(&copywriting, userId); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
		return
	}

	response.OkWithDetailed(copywriting, "创建成功", c)
}

// GetCopywritingList 获取文案列表
// @Tags Media.Copywriting
// @Summary 获取文案列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param keyword query string false "关键词(搜索内容)"
// @Param categoryId query int false "分类ID"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取文案列表"
// @Router /media/copywriting/list [get]
func (api *MediaCopywritingApi) GetCopywritingList(c *gin.Context) {
	var pageInfo mediaReq.CopywritingSearch
	pageInfo.Page, _ = strconv.Atoi(c.DefaultQuery("page", "1"))
	pageInfo.PageSize, _ = strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	pageInfo.Keyword = c.Query("keyword")
	categoryId, _ := strconv.Atoi(c.DefaultQuery("categoryId", "0"))
	pageInfo.CategoryId = uint(categoryId)

	userId := utils.GetUserID(c)
	list, total, err := copywritingService.GetCopywritingList(userId, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// DeleteCopywriting 删除文案
// @Tags Media.Copywriting
// @Summary 删除文案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "文案ID"
// @Success 200 {object} response.Response{msg=string} "删除文案"
// @Router /media/copywriting/{id} [delete]
func (api *MediaCopywritingApi) DeleteCopywriting(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	userId := utils.GetUserID(c)
	err = copywritingService.DeleteCopywriting(uint(id), userId)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// EditCopywriting 编辑文案
// @Tags Media.Copywriting
// @Summary 编辑文案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "文案ID"
// @Param data body mediaModel.Copywriting true "文案信息（只包含 content）"
// @Success 200 {object} response.Response{msg=string} "编辑文案"
// @Router /media/copywriting/{id} [put]
func (api *MediaCopywritingApi) EditCopywriting(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	var copywriting mediaModel.Copywriting
	if err := c.ShouldBindJSON(&copywriting); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	copywriting.GVA_MODEL.ID = uint(id)
	// creatorId 不允许在此处修改
	copywriting.CreatorId = 0

	err = copywritingService.EditCopywriting(&copywriting)
	if err != nil {
		global.GVA_LOG.Error("编辑失败!", zap.Error(err))
		response.FailWithMessage("编辑失败", c)
		return
	}

	response.OkWithMessage("编辑成功", c)
}

// EditCopywritingCategory 修改文案分类
// @Tags Media.Copywriting
// @Summary 修改文案分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "文案ID"
// @Param data body map[string]uint true "分类ID, {\"categoryId\": 1}"
// @Success 200 {object} response.Response{msg=string} "修改文案分类"
// @Router /media/copywriting/{id}/category [patch]
func (api *MediaCopywritingApi) EditCopywritingCategory(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	var reqData struct {
		CategoryId uint `json:"categoryId"`
	}
	if err := c.ShouldBindJSON(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	copywriting := &mediaModel.Copywriting{}
	copywriting.GVA_MODEL.ID = uint(id)
	copywriting.CategoryId = reqData.CategoryId

	err = copywritingService.EditCopywritingCategory(copywriting)
	if err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败", c)
		return
	}

	response.OkWithMessage("修改成功", c)
}
