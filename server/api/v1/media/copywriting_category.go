package media

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	mediaModel "github.com/flipped-aurora/gin-vue-admin/server/model/media"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MediaCopywritingCategoryApi struct{}

// GetCategoryList 获取文案分类列表
// @Tags Media.CopywritingCategory
// @Summary 获取文案分类列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]mediaModel.CopywritingCategory,msg=string} "获取文案分类列表"
// @Router /media/copywriting/categories [get]
func (m *MediaCopywritingCategoryApi) GetCategoryList(c *gin.Context) {
	categoryList, err := copywritingCategoryService.GetCategoryList(c)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(categoryList, "获取成功", c)
}

// AddCopywritingCategory 添加/编辑文案分类
// @Tags Media.CopywritingCategory
// @Summary 添加/编辑文案分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body mediaModel.CopywritingCategory true "分类信息"
// @Success 200 {object} response.Response{msg=string} "添加/编辑文案分类"
// @Router /media/copywriting/categories [post]
func (m *MediaCopywritingCategoryApi) AddCopywritingCategory(c *gin.Context) {
	var category mediaModel.CopywritingCategory
	err := c.ShouldBindJSON(&category)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = copywritingCategoryService.AddOrUpdateCopywritingCategory(&category, c)
	if err != nil {
		global.GVA_LOG.Error("操作失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("操作成功", c)
}

// DeleteCopywritingCategory 删除文案分类
// @Tags Media.CopywritingCategory
// @Summary 删除文案分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "分类ID"
// @Success 200 {object} response.Response{msg=string} "删除文案分类"
// @Router /media/copywriting/categories/{id} [delete]
func (m *MediaCopywritingCategoryApi) DeleteCopywritingCategory(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}
	if err := copywritingCategoryService.DeleteCopywritingCategory(uint(id)); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// UpdateCategoryOrder 更新分类排序
// @Tags Media.CopywritingCategory
// @Summary 更新分类排序
// @accept application/json
// @Produce application/json
// @Param data body request.UpdateCategoryOrderRequest true "排序信息"
// @Success 200 {object} response.Response{msg=string} "更新分类排序"
// @Router /media/copywriting/categories/order [post]
func (m *MediaCopywritingCategoryApi) UpdateCategoryOrder(c *gin.Context) {
	var req request.UpdateCategoryOrderRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = copywritingCategoryService.UpdateCategoryOrder(c, req.Pid, req.Order)
	if err != nil {
		global.GVA_LOG.Error("更新排序失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("更新排序成功", c)
}
