package media

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MediaApi struct{}

// GetResourceCategoryCounts 获取所有分类的资源计数
// @Tags Media
// @Summary 获取所有分类的资源计数
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param type query string true "资源类型：video/image/audio/copywriting"
// @Success 200 {object} response.Response{data=map[string]int64,msg=string} "获取成功"
// @Router /media/category/counts [get]
func (api *MediaApi) GetResourceCategoryCounts(c *gin.Context) {
	resourceType := c.Query("type")
	if resourceType == "" {
		response.FailWithMessage("资源类型不能为空", c)
		return
	}

	userId := utils.GetUserID(c)
	var counts map[string]int64
	var err error

	// 根据资源类型调用不同的服务
	switch resourceType {
	case "video":
		counts, err = videoCategoryService.GetCategoryCounts(userId)
	case "image":
		counts, err = imageCategoryService.GetCategoryCounts(userId)
	case "audio":
		counts, err = musicCategoryService.GetCategoryCounts(userId)
	case "copywriting":
		counts, err = copywritingCategoryService.GetCategoryCounts(userId)
	default:
		response.FailWithMessage("不支持的资源类型", c)
		return
	}

	if err != nil {
		global.GVA_LOG.Error("获取分类计数失败!", zap.Error(err))
		response.FailWithMessage("获取分类计数失败: "+err.Error(), c)
		return
	}

	response.OkWithData(counts, c)
}
