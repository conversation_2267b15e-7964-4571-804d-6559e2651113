package media

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	mediaModel "github.com/flipped-aurora/gin-vue-admin/server/model/media"
	mediaReq "github.com/flipped-aurora/gin-vue-admin/server/model/media/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MediaImageApi struct{}

// UploadImage 上传图片
// @Tags Media.Image
// @Summary 上传图片文件
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce application/json
// @Param file formData file true "支持 jpg、png、webp、gif、bmp 格式的图片文件"
// @Param name formData string true "图片名称"
// @Param classId formData int false "分类ID"
// @Success 200 {object} response.Response{data=mediaModel.Image,msg=string} "上传图片"
// @Router /media/image/upload [post]
func (api *MediaImageApi) UploadImage(c *gin.Context) {
	if err := c.Request.ParseMultipartForm(10 << 20); err != nil { // 10 MB max memory
		global.GVA_LOG.Error("解析表单失败!", zap.Error(err))
		response.FailWithMessage("解析表单失败", c)
		return
	}

	form := c.Request.MultipartForm
	files := form.File["file"] // "file" is the default name used by el-upload
	if len(files) == 0 {
		response.FailWithMessage("没有检测到图片文件", c)
		return
	}

	classIdStr := c.Request.FormValue("classId")
	classIdUint, _ := strconv.Atoi(classIdStr)
	userId := utils.GetUserID(c)

	var uploadedImages []mediaModel.Image
	var errorMessages []string

	for _, header := range files {
		file, err := header.Open()
		if err != nil {
			global.GVA_LOG.Error("打开图片文件失败: "+header.Filename, zap.Error(err))
			errorMessages = append(errorMessages, header.Filename+": 打开失败")
			continue
		}

		image, err := imageService.UploadImageFile(header, header.Filename, uint(classIdUint), userId)
		// Ensure file is closed regardless of success or failure of UploadImageFile
		if ferr := file.Close(); ferr != nil {
		    global.GVA_LOG.Error("关闭文件失败: "+header.Filename, zap.Error(ferr))
		    // Optionally, you could add this to errorMessages as well, but it's a secondary error
		}

		if err != nil {
			global.GVA_LOG.Error("上传图片失败: "+header.Filename, zap.Error(err))
			errorMessages = append(errorMessages, header.Filename+": "+err.Error())
			continue
		}
		uploadedImages = append(uploadedImages, *image)
	}

	if len(uploadedImages) == 0 && len(errorMessages) > 0 {
		response.FailWithMessage("所有图片上传失败: "+errorMessages[0], c) // Return first error for simplicity
		return
	}

	msg := "上传完成."
	if len(uploadedImages) > 0 {
	    msg = strconv.Itoa(len(uploadedImages)) + " 张图片上传成功."
	}
	if len(errorMessages) > 0 {
	    msg += " " + strconv.Itoa(len(errorMessages)) + " 张图片上传失败."
	    global.GVA_LOG.Warn("部分图片上传失败", zap.Strings("errors", errorMessages))
	}

	response.OkWithDetailed(uploadedImages, msg, c)
}

// GetImageList 获取图片列表
// @Tags Media.Image
// @Summary 获取图片列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param keyword query string false "关键词"
// @Param categoryId query int false "分类ID"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取图片列表"
// @Router /media/image/list [get]
func (api *MediaImageApi) GetImageList(c *gin.Context) {
	var pageInfo mediaReq.ImageSearch
	pageInfo.Page, _ = strconv.Atoi(c.DefaultQuery("page", "1"))
	pageInfo.PageSize, _ = strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	pageInfo.Keyword = c.Query("keyword")
	categoryId, _ := strconv.Atoi(c.DefaultQuery("categoryId", "0"))
	pageInfo.CategoryId = uint(categoryId)

	list, total, err := imageService.GetImageList(utils.GetUserID(c), pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// DeleteImage 删除图片
// @Tags Media.Image
// @Summary 删除图片
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "图片ID"
// @Success 200 {object} response.Response{msg=string} "删除图片"
// @Router /media/image/{id} [delete]
func (api *MediaImageApi) DeleteImage(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	userId := utils.GetUserID(c)
	err = imageService.DeleteImage(uint(id), userId)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// EditImageName 编辑图片名称
// @Tags Media.Image
// @Summary 编辑图片名称
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "图片ID"
// @Param data body map[string]string true "名称"
// @Success 200 {object} response.Response{msg=string} "编辑图片名称"
// @Router /media/image/{id}/name [patch]
func (api *MediaImageApi) EditImageName(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	var reqData struct {
		Name string `json:"name"`
	}
	if err := c.ShouldBindJSON(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	image := &mediaModel.Image{}
	image.GVA_MODEL.ID = uint(id)
	image.Name = reqData.Name

	err = imageService.EditImageName(image)
	if err != nil {
		global.GVA_LOG.Error("编辑失败!", zap.Error(err))
		response.FailWithMessage("编辑失败", c)
		return
	}

	response.OkWithMessage("编辑成功", c)
}

// ImportImageURL 导入图片URL
// @Tags Media.Image
// @Summary 导入图片URL
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body []mediaModel.Image true "图片列表"
// @Success 200 {object} response.Response{msg=string} "导入图片URL"
// @Router /media/image/import [post]
func (api *MediaImageApi) ImportImageURL(c *gin.Context) {
	var images []mediaModel.Image
	err := c.ShouldBindJSON(&images)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if len(images) == 0 {
		response.FailWithMessage("没有图片数据", c)
		return
	}

	userId := utils.GetUserID(c)
	err = imageService.ImportImageURLs(images, userId)
	if err != nil {
		global.GVA_LOG.Error("导入失败!", zap.Error(err))
		response.FailWithMessage("导入失败", c)
		return
	}

	response.OkWithMessage("导入成功", c)
}

// EditImageCategory 修改图片分类
// @Tags Media.Image
// @Summary 修改图片分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "图片ID"
// @Param data body map[string]uint true "分类ID"
// @Success 200 {object} response.Response{msg=string} "修改图片分类"
// @Router /media/image/{id}/category [patch]
func (api *MediaImageApi) EditImageCategory(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.FailWithMessage("无效的ID", c)
		return
	}

	var reqData struct {
		CategoryId uint `json:"categoryId"`
	}
	if err := c.ShouldBindJSON(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	image := &mediaModel.Image{}
	image.GVA_MODEL.ID = uint(id)
	image.CategoryId = reqData.CategoryId

	err = imageService.EditImageCategory(image)
	if err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败", c)
		return
	}

	response.OkWithMessage("修改成功", c)
}
