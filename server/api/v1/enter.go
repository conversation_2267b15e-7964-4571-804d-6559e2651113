package v1

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/ai"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/creative"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/example"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/media"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/system"
)

var ApiGroupApp = new(ApiGroup)

type ApiGroup struct {
	SystemApiGroup   system.ApiGroup
	ExampleApiGroup  example.ApiGroup
	DouyinApiGroup   douyin.ApiGroup
	MediaApiGroup    media.ApiGroup
	AiApiGroup       ai.ApiGroup
	CreativeApiGroup creative.ApiGroup
}
