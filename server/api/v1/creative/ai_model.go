package creative

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	creativeRequest "github.com/flipped-aurora/gin-vue-admin/server/model/creative/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AiModelApi struct{}

// CreateAiModel
// @Tags      AiModel
// @Summary   创建AI模型
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      creativeRequest.CreateAiModel  true  "创建AI模型"
// @Success   200   {object}  response.Response{msg=string}  "创建成功"
// @Router    /creative/ai-model/create [post]
func (a *AiModelApi) CreateAiModel(c *gin.Context) {
	var createReq creativeRequest.CreateAiModel
	err := c.ShouldBindJSON(&createReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	model := &creative.AiModel{
		Name:            createReq.Name,
		ModelType:       createReq.ModelType,
		VideoCategoryID: createReq.VideoCategoryID,
		MusicID:         createReq.MusicID,
		MusicName:       createReq.MusicName,
		MusicURL:        createReq.MusicURL,
		FaceImageURL:    createReq.FaceImageURL,
		Status:          createReq.Status,
		CreatedBy:       utils.GetUserID(c),
		UpdatedBy:       utils.GetUserID(c),
	}

	err = aiModelService.CreateAiModel(model)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// GetAiModelList
// @Tags      AiModel
// @Summary   分页获取AI模型列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     page      query  int     false  "页码"
// @Param     pageSize  query  int     false  "每页大小"
// @Param     name      query  string  false  "模型名称"
// @Param     status    query  int     false  "状态"
// @Success   200       {object}  response.Response{data=response.PageResult,msg=string}  "获取成功"
// @Router    /creative/ai-model/list [get]
func (a *AiModelApi) GetAiModelList(c *gin.Context) {
	var search creativeRequest.AiModelSearch
	err := c.ShouldBindQuery(&search)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	// 获取当前用户ID
	currentUserID := utils.GetUserID(c)

	// 获取当前用户及其下级用户的ID列表
	userIds, err := userService.GetSubordinateUserIds(currentUserID)
	if err != nil {
		response.FailWithMessage("获取用户权限失败: "+err.Error(), c)
		return
	}
	// 添加当前用户ID
	userIds = append(userIds, currentUserID)

	list, total, err := aiModelService.GetAiModelList(search, userIds)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     search.Page,
		PageSize: search.PageSize,
	}, "获取成功", c)
}

// GetAiModelById
// @Tags      AiModel
// @Summary   根据ID获取AI模型
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id   path      int     true  "AI模型ID"
// @Success   200  {object}  response.Response{data=response.AiModelResponse,msg=string}  "获取成功"
// @Router    /creative/ai-model/{id} [get]
func (a *AiModelApi) GetAiModelById(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("ID格式错误", c)
		return
	}
	if id == 0 {
		response.FailWithMessage("ID不能为空", c)
		return
	}

	data, err := aiModelService.GetAiModelById(uint(id))
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithData(data, c)
}

// UpdateAiModel
// @Tags      AiModel
// @Summary   更新AI模型
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id    path      int                    true   "AI模型ID"
// @Param     data  body      creative.AiModel             true   "更新AI模型"
// @Success   200   {object}  response.Response{msg=string}  "更新成功"
// @Router    /creative/ai-model/{id} [put]
func (a *AiModelApi) UpdateAiModel(c *gin.Context) {
	var model creative.AiModel
	err := c.ShouldBindJSON(&model)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("ID格式错误", c)
		return
	}
	if id == 0 {
		response.FailWithMessage("ID不能为空", c)
		return
	}
	model.ID = uint(id)
	model.UpdatedBy = utils.GetUserID(c)

	err = aiModelService.UpdateAiModel(&model)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// DeleteAiModel
// @Tags      AiModel
// @Summary   删除AI模型
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id   path      int     true  "AI模型ID"
// @Success   200  {object}  response.Response{msg=string}  "删除成功"
// @Router    /creative/ai-model/{id} [delete]
func (a *AiModelApi) DeleteAiModel(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("ID格式错误", c)
		return
	}
	if id == 0 {
		response.FailWithMessage("ID不能为空", c)
		return
	}

	err = aiModelService.DeleteAiModel(uint(id))
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// UpdateAiModelStatus
// @Tags      AiModel
// @Summary   更新AI模型状态
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id      path    int                                true   "AI模型ID"
// @Param     data    body    creativeRequest.UpdateStatusRequest      true   "更新状态"
// @Success   200     {object}  response.Response{msg=string}  "更新成功"
// @Router    /creative/ai-model/status/{id} [put]
func (a *AiModelApi) UpdateAiModelStatus(c *gin.Context) {
	var updateReq creativeRequest.UpdateStatusRequest
	err := c.ShouldBindJSON(&updateReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("ID格式错误", c)
		return
	}
	if id == 0 {
		response.FailWithMessage("ID不能为空", c)
		return
	}

	err = aiModelService.UpdateAiModelStatus(uint(id), updateReq.Status, utils.GetUserID(c))
	if err != nil {
		global.GVA_LOG.Error("更新状态失败!", zap.Error(err))
		response.FailWithMessage("更新状态失败", c)
		return
	}
	response.OkWithMessage("更新状态成功", c)
}
