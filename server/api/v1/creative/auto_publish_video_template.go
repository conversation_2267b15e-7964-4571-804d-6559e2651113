package creative

import (
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
)

type AutoPublishVideoTemplateApi struct{}

// 获取管理员自动发布视频模板名称列表
// @Tags      AutoPublishVideoTemplate
// @Summary   获取管理员自动发布视频模板
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{data=[]response.AutoPublishVideoTemplateResponse,msg=string}  "获取成功"
// @Router    /creative/auto-publish-video-template/name-list [get]
func (e *AutoPublishVideoTemplateApi) NameList(c *gin.Context) {
	userID := utils.GetUserID(c)
	resp, err := autoPublishVideoTemplateService.GetTemplates(userID)
	if err != nil {
		response.FailWithMessage("获取模板失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(resp, "获取成功", c)
}

// 保存管理员自动发布视频模板
// @Tags      AutoPublishVideoTemplate
// @Summary   保存管理员自动发布视频模板
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.SaveAutoPublishVideoTemplateRequest  true  "模板列表"
// @Success   200   {object}  response.Response{msg=string}  "保存成功"
// @Router    /creative/auto-publish-video-template/save [post]
func (e *AutoPublishVideoTemplateApi) Save(c *gin.Context) {
	var req request.SaveAutoPublishVideoTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	// 提取req的template_id
	if req.OldTemplateId != "" {
		// 根据template_id获取原来的模板
		oldTemplate, err := autoPublishVideoTemplateService.GetTemplateByTemplateId(req.OldTemplateId)
		if err != nil {
			response.FailWithMessage("获取模板失败: "+err.Error(), c)
			return
		}
		// 由于 oldTemplate.SysUserId 是 int64 类型，userID 是 uint 类型，需要进行类型转换
		if oldTemplate.SysUserId != int64(userID) {
			response.FailWithMessage("您没有操作权限", c)
			return
		}

		// 删除原来的模板
		err = autoPublishVideoTemplateService.DeleteByTemplateId(req.OldTemplateId)
		if err != nil {
			response.FailWithMessage("删除模板失败: "+err.Error(), c)
			return
		}
	}

	// 根据userID和req中的templateName用md5的方式生成一个新的templateId
	for i := range req.List {
		req.List[i].SysUserId = int64(userID)
		req.List[i].TemplateId = utils.MD5V([]byte(fmt.Sprintf("%s:%d", req.List[i].TemplateName, userID)))
	}

	// 批量新增新模板
	err := autoPublishVideoTemplateService.BatchCreate(req.List)
	if err != nil {
		response.FailWithMessage("保存模板失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("保存成功", c)
}

// 根据模板ID获取模板列表
// @Tags      AutoPublishVideoTemplate
// @Summary   根据模板ID获取模板
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.GetTemplateByTemplateIdRequest  true  "模板ID"
// @Success   200   {object}  response.Response{data=response.AutoPublishVideoTemplateResponse,msg=string}  "获取成功"
// @Router    /creative/auto-publish-video-template/list [get]
func (e *AutoPublishVideoTemplateApi) List(c *gin.Context) {
	var req request.TemplateByTemplateIdRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userID := utils.GetUserID(c)
	resp, err := autoPublishVideoTemplateService.GetTemplatesByTemplateIdAndSysUserId(req.TemplateId, userID)
	if err != nil {
		response.FailWithMessage("获取模板失败: "+err.Error(), c)
		return
	}
	response.OkWithDetailed(resp, "获取成功", c)
}

// 根据模板ID删除模板
// @Tags      AutoPublishVideoTemplate
// @Summary   根据模板ID删除模板
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.GetTemplateByTemplateIdRequest  true  "模板ID"
// @Success   200   {object}  response.Response{msg=string}  "删除成功"
// @Router    /creative/auto-publish-video-template/delete [delete]
func (e *AutoPublishVideoTemplateApi) Delete(c *gin.Context) {
	var req request.TemplateByTemplateIdRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
	}

	userID := utils.GetUserID(c)
	// 根据template_id获取原来的模板
	oldTemplate, err := autoPublishVideoTemplateService.GetTemplateByTemplateId(req.TemplateId)
	if err != nil {
		response.FailWithMessage("获取模板失败: "+err.Error(), c)
		return
	}
	// 判断删除权限
	if oldTemplate.SysUserId != int64(userID) {
		response.FailWithMessage("您没有操作权限", c)
		return
	}

	err = autoPublishVideoTemplateService.DeleteByTemplateId(req.TemplateId)
	if err != nil {
		response.FailWithMessage("删除模板失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}
