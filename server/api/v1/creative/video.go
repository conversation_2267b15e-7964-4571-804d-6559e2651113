package creative

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	creativeRequest "github.com/flipped-aurora/gin-vue-admin/server/model/creative/request"
	creativeResponse "github.com/flipped-aurora/gin-vue-admin/server/model/creative/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	moreResponse "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
	"github.com/flipped-aurora/gin-vue-admin/server/service/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/upload"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/video"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type VideoApi struct{}

// CreateVideo
// @Tags      Video
// @Summary   创建视频
// @Security  ApiKeyAuth
// @accept    multipart/form-data
// @Produce   application/json
// @Param     data  formData  creative.Video  true  "视频标题, 分类ID"
// @Param     cover  formData  file  false  "视频封面"
// @Param     video  formData  file  true  "视频文件"
// @Success   200    {object}  response.Response{msg=string}  "创建成功"
// @Router    /creative/video/create [post]
func (v *VideoApi) CreateVideo(c *gin.Context) {
	// 获取参数
	var videoCreate creativeRequest.VideoCreate
	if err := c.ShouldBind(&videoCreate); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取上传的封面文件
	var coverUrl string
	cover, err := c.FormFile("cover")
	if err == nil && cover != nil {
		// 处理封面上传到阿里云OSS
		coverOss := upload.NewOss()
		coverUrl, _, err = coverOss.UploadFile(cover)
		if err != nil {
			global.GVA_LOG.Error("封面上传失败", zap.Error(err))
			response.FailWithMessage("封面上传失败", c)
			return
		}
	}

	// 检查是否是编辑模式且不需要更新视频
	isEditMode := videoCreate.ID != 0
	videoUpdateFlag := c.PostForm("videoUpdate")
	skipVideoUpdate := isEditMode && videoUpdateFlag == "false"

	var videoUrl string
	var videoDuration int64 // 添加变量存储视频时长

	if !skipVideoUpdate {
		// 获取上传的视频文件
		videoFile, err := c.FormFile("video")
		if err != nil {
			global.GVA_LOG.Error("获取视频文件失败", zap.Error(err))
			response.FailWithMessage("获取视频文件失败", c)
			return
		}

		global.GVA_LOG.Info("获取到视频文件",
			zap.String("filename", videoFile.Filename),
			zap.Int64("size", videoFile.Size))

		// 如果获取到了非常小的文件（例如空文件），并且是编辑模式，可能是前端故意传一个占位符
		// 这种情况下也应该跳过视频更新
		if videoFile.Size < 100 && isEditMode { // 小于100字节视为占位符
			global.GVA_LOG.Info("检测到占位符视频文件，跳过视频更新")
			oldVideo, err := videoService.GetVideo(videoCreate.ID)
			if err != nil {
				global.GVA_LOG.Error("获取原视频信息失败", zap.Error(err))
				response.FailWithMessage("获取原视频信息失败", c)
				return
			}
			videoUrl = oldVideo.Url
			videoDuration = oldVideo.Duration // 保留原视频时长

			// 如果没有新的封面上传，保留原封面
			if coverUrl == "" {
				coverUrl = oldVideo.Cover
			}
		} else {
			// 继续处理正常的视频上传
			// 如果没有上传封面，自动从视频中提取第一帧作为封面
			if coverUrl == "" {
				// 将视频保存到临时目录
				tempVideoPath := filepath.Join(os.TempDir(), videoFile.Filename)
				if err := c.SaveUploadedFile(videoFile, tempVideoPath); err != nil {
					global.GVA_LOG.Error("保存视频到临时目录失败",
						zap.String("path", tempVideoPath),
						zap.Error(err))
					// 提取封面失败不影响视频上传，继续处理
				} else {
					defer os.Remove(tempVideoPath) // 使用完毕后删除临时文件
					global.GVA_LOG.Info("视频已保存到临时目录，准备提取第一帧",
						zap.String("path", tempVideoPath))

					// 使用新的 VideoFrameExtractor 提取第一帧并上传到OSS（不压缩）
					extractor := video.NewVideoFrameExtractor().
						WithVideoPath(tempVideoPath).
						WithCompression(false). // 不压缩首帧图
						WithOSSUpload(true).
						WithDuration(true) // 同时获取视频时长

					defer extractor.Cleanup() // 确保清理临时文件

					result, err := extractor.ExtractWithResult()
					if err != nil {
						global.GVA_LOG.Error("视频帧提取失败",
							zap.String("videoPath", tempVideoPath),
							zap.Error(err))
						// 提取失败不影响视频上传，继续处理
					} else {
						coverUrl = result.ImageURL           // 使用OSS URL作为封面
						videoDuration = result.VideoDuration // 设置视频时长
						global.GVA_LOG.Info("成功提取并上传视频封面",
							zap.String("coverUrl", coverUrl),
							zap.Int64("duration", result.VideoDuration))
					}
				}
			} else {
				// 如果已经有封面，但仍需要获取视频时长
				tempVideoPath := filepath.Join(os.TempDir(), videoFile.Filename)
				if err := c.SaveUploadedFile(videoFile, tempVideoPath); err != nil {
					global.GVA_LOG.Error("保存视频到临时目录失败",
						zap.String("path", tempVideoPath),
						zap.Error(err))
				} else {
					defer os.Remove(tempVideoPath) // 使用完毕后删除临时文件

					// 仅获取视频时长
					extractor := video.NewVideoFrameExtractor().
						WithVideoPath(tempVideoPath).
						WithDuration(true)

					defer extractor.Cleanup() // 确保清理临时文件

					result, err := extractor.ExtractWithResult()
					if err != nil {
						global.GVA_LOG.Error("获取视频时长失败",
							zap.String("videoPath", tempVideoPath),
							zap.Error(err))
					} else {
						videoDuration = result.VideoDuration // 设置视频时长
						global.GVA_LOG.Info("成功获取视频时长",
							zap.Int64("duration", result.VideoDuration))
					}
				}
			}

			// 直接使用OSS上传视频文件
			videoOss := upload.NewOss()
			videoUrl, _, err = videoOss.UploadFile(videoFile)
			if err != nil {
				global.GVA_LOG.Error("视频上传失败", zap.Error(err))
				response.FailWithMessage("视频上传失败", c)
				return
			}
		}
	} else if isEditMode {
		// 编辑模式但不更新视频，需要获取原视频的URL和时长
		oldVideo, err := videoService.GetVideo(videoCreate.ID)
		if err != nil {
			global.GVA_LOG.Error("获取原视频信息失败", zap.Error(err))
			response.FailWithMessage("获取原视频信息失败", c)
			return
		}
		videoUrl = oldVideo.Url
		videoDuration = oldVideo.Duration // 保留原视频时长

		// 如果没有新的封面上传，保留原封面
		if coverUrl == "" {
			coverUrl = oldVideo.Cover
		}
	}

	// 获取当前用户
	userID := utils.GetUserID(c)

	// 视频入库
	video := creative.Video{
		CategoryId:      videoCreate.CategoryId,
		Title:           videoCreate.Title,
		Cover:           coverUrl,
		Url:             videoUrl,
		Status:          creative.VideoStatusPending,
		CreatedBy:       userID,
		Source:          creative.VideoSourceManual,
		Topic:           videoCreate.Topic,
		ManualProductId: videoCreate.ManualProductId,
		Duration:        videoDuration,             // 设置视频时长
		ContentType:     creative.ContentTypeVideo, // 视频类型
	}

	if videoCreate.ID != 0 {
		// 编辑视频
		video.ID = videoCreate.ID
		video.Status = videoCreate.Status
		err = videoService.UpdateVideo(&video)
	} else {
		// 新增视频
		err = videoService.CreateVideo(&video)
	}

	if err != nil {
		global.GVA_LOG.Error("保存视频信息失败!", zap.Error(err))
		response.FailWithMessage("保存视频信息失败", c)
		return
	}

	response.OkWithMessage("创建成功", c)
}

// DeleteVideo
// @Tags      Video
// @Summary   删除视频
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      creative.VideoDelete  true  "视频ID"
// @Success   200   {object}  response.Response{msg=string}  "删除成功"
// @Router    /creative/video/delete [delete]
func (v *VideoApi) DeleteVideo(c *gin.Context) {
	var videoDelete creativeRequest.VideoDelete
	err := c.ShouldBindJSON(&videoDelete)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = videoService.DeleteVideo(videoDelete.ID)
	if err != nil {
		global.GVA_LOG.Error("删除视频失败!", zap.Error(err))
		response.FailWithMessage("删除视频失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// SetCover
// @Tags      Video
// @Summary   设置封面
// @Security  ApiKeyAuth
// @accept    multipart/form-data
// @Produce   application/json
// @Param     id  formData  uint  true  "视频ID"
// @Param     cover  formData  file  true  "封面图片"
// @Success   200   {object}  response.Response{msg=string}  "设置成功"
// @Router    /creative/video/set-cover [post]
func (v *VideoApi) SetCover(c *gin.Context) {
	// 获取视频ID
	id := c.PostForm("id")
	if id == "" {
		response.FailWithMessage("视频ID不能为空", c)
		return
	}

	idUint := utils.StringToUint(id)
	if idUint == 0 {
		response.FailWithMessage("无效的视频ID", c)
		return
	}

	// 获取上传的封面文件
	cover, err := c.FormFile("cover")
	if err != nil {
		response.FailWithMessage("获取封面文件失败", c)
		return
	}

	// 处理封面上传到阿里云OSS
	coverOss := upload.NewOss()
	coverUrl, _, err := coverOss.UploadFile(cover)
	if err != nil {
		global.GVA_LOG.Error("封面上传失败", zap.Error(err))
		response.FailWithMessage("封面上传失败", c)
		return
	}

	// 更新视频封面
	err = videoService.SetCover(idUint, coverUrl)
	if err != nil {
		global.GVA_LOG.Error("设置封面失败!", zap.Error(err))
		response.FailWithMessage("设置封面失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("设置成功", c)
}

// GetVideoList
// @Tags      Video
// @Summary   获取视频列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     creative.VideoSearch                        true  "页码, 每页大小, 搜索条件"
// @Success   200   {object}  response.Response{data=creative.VideoList}  "获取成功"
// @Router    /creative/video/list [get]
func (v *VideoApi) GetVideoList(c *gin.Context) {
	var videoSearch creativeRequest.VideoSearch
	err := c.ShouldBindQuery(&videoSearch)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if videoSearch.Page == 0 {
		videoSearch.Page = 1
	}
	if videoSearch.PageSize == 0 {
		videoSearch.PageSize = 10
	}

	// 获取当前用户ID
	currentUserID := utils.GetUserID(c)

	// 获取当前用户及其下级用户的ID列表
	userIds, err := userService.GetSubordinateUserIds(currentUserID)
	if err != nil {
		response.FailWithMessage("获取用户权限失败: "+err.Error(), c)
		return
	}
	// 添加当前用户ID
	userIds = append(userIds, currentUserID)

	list, total, err := videoService.GetVideoList(videoSearch, userIds)
	if err != nil {
		global.GVA_LOG.Error("获取视频列表失败!", zap.Error(err))
		response.FailWithMessage("获取视频列表失败", c)
		return
	}

	response.OkWithDetailed(creativeResponse.VideoList{
		List:     list,
		Total:    total,
		Page:     videoSearch.Page,
		PageSize: videoSearch.PageSize,
	}, "获取成功", c)
}

// SearchTopics 搜索话题
// @Tags      Video
// @Summary   搜索话题
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.MoreApiSearchTopicRequest  true  "搜索关键词"
// @Success   200   {object}  response.Response{data=response.MoreApiSearchTopicResponse}  "获取成功"
// @Router    /creative/video/search-topics [get]
func (v *VideoApi) SearchTopics(c *gin.Context) {
	var searchReq request.MoreApiSearchTopicRequest
	err := c.ShouldBindQuery(&searchReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认返回数量
	if searchReq.Count == "" {
		searchReq.Count = "10"
	}

	// 手动解析keywords数组参数，因为ShouldBindQuery无法正确处理GET请求中的数组
	keywordsParam := c.QueryArray("keywords")
	if len(keywordsParam) > 0 {
		searchReq.Keywords = keywordsParam
	}

	// 兼容性处理：如果Keywords为空但Keyword不为空，则将Keyword添加到Keywords中
	if len(searchReq.Keywords) == 0 && searchReq.Keyword != "" {
		searchReq.Keywords = []string{searchReq.Keyword}
	}

	// 检查是否有关键词需要搜索
	if len(searchReq.Keywords) == 0 {
		response.FailWithMessage("请提供至少一个搜索关键词", c)
		return
	}

	global.GVA_LOG.Info("搜索话题请求", zap.Strings("keywords", searchReq.Keywords), zap.String("count", searchReq.Count))

	var allResults []moreResponse.MoreApiSearchTopicResponse
	// 使用协程并发调用MoreAPI搜索话题
	type keywordResult struct {
		index  int
		result moreResponse.MoreApiSearchTopicResponse
		err    error
	}

	resultChan := make(chan keywordResult, len(searchReq.Keywords))

	// 为每个关键词启动一个协程
	for i, keyword := range searchReq.Keywords {
		go func(index int, kw string) {
			defer func() {
				if r := recover(); r != nil {
					global.GVA_LOG.Error("搜索话题协程panic", zap.String("keyword", kw), zap.Any("panic", r))
					resultChan <- keywordResult{
						index: index,
						err:   fmt.Errorf("协程执行异常: %v", r),
					}
				}
			}()

			// 构建单个搜索请求
			singleReq := request.MoreApiSearchTopicRequest{
				Keyword: kw,
				Count:   searchReq.Count,
			}

			resp, err := douyin.MoreApiServiceApp.SearchTopic(singleReq)
			if err != nil {
				global.GVA_LOG.Error("搜索话题失败", zap.String("keyword", kw), zap.Error(err))
			}

			resultChan <- keywordResult{
				index:  index,
				result: resp,
				err:    err,
			}
		}(i, keyword)
	}

	// 收集所有结果，按原始顺序排列
	results := make([]moreResponse.MoreApiSearchTopicResponse, len(searchReq.Keywords))
	errors := make([]error, len(searchReq.Keywords))

	for i := 0; i < len(searchReq.Keywords); i++ {
		result := <-resultChan
		results[result.index] = result.result
		errors[result.index] = result.err
	}

	// 将结果添加到allResults中
	for i, result := range results {
		if errors[i] != nil {
			// 即使有错误也添加空结果，保持顺序一致
			allResults = append(allResults, moreResponse.MoreApiSearchTopicResponse{})
		} else {
			allResults = append(allResults, result)
		}
	}

	response.OkWithData(allResults, c)
}
