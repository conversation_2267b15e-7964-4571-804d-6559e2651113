package creative

import "github.com/flipped-aurora/gin-vue-admin/server/service"

type ApiGroup struct {
	VideoCategoryApi
	AiModelApi
	WatchTargetApi
	VideoApi
	AutoPublishVideoApi
	AutoPublishVideoTemplateApi
}

var (
	videoCategoryService            = service.ServiceGroupApp.CreativeServiceGroup.VideoCategoryService
	aiModelService                  = service.ServiceGroupApp.CreativeServiceGroup.AiModelService
	musicService                    = service.ServiceGroupApp.MediaServiceGroup.MusicService
	watchTargetUserService          = service.ServiceGroupApp.CreativeServiceGroup.WatchTargetUserService
	watchTargetPostService          = service.ServiceGroupApp.CreativeServiceGroup.WatchTargetPostService
	videoService                    = service.ServiceGroupApp.CreativeServiceGroup.VideoService
	autoPublishVideoService         = service.ServiceGroupApp.CreativeServiceGroup.AutoPublishVideoService
	autoPublishVideoTemplateService = service.ServiceGroupApp.CreativeServiceGroup.AutoPublishVideoTemplateService
	autoPublishVideoRecordService   = service.ServiceGroupApp.CreativeServiceGroup.AutoPublishVideoRecordService
	dyUserService                   = service.ServiceGroupApp.DouyinServiceGroup.DyUserService
	userService                     = service.ServiceGroupApp.SystemServiceGroup.UserService
)
