package douyin

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DyProductApi struct{}

// GetProductFeed
// @Tags      DyProduct
// @Summary   获取商品Feed流
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.ProductListRequest                                        true  "页码, 每页大小, 抖音用户token"
// @Success   200   {object}  response.Response{msg=string}                                     "获取成功"
// @Router    /douyin/product/feed [get]
func (api *DyProductApi) GetProductFeed(c *gin.Context) {
	var req request.DyProductListRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	// 获取抖音用户ID
	dyUser, err := dyUserService.GetUserByToken(req.Token)
	if err != nil {
		global.GVA_LOG.Error("获取抖音用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取抖音用户信息失败", c)
		return
	}

	// 检查同一天同一个抖音用户是否已经执行过GetProductFeed
	today := time.Now().Format("2006-01-02")
	todayStart, _ := time.Parse("2006-01-02", today)
	todayEnd := todayStart.Add(24 * time.Hour)

	var count int64
	err = global.GVA_DB.Model(&douyin.DyProductSelection{}).
		Where("dy_user_id = ? AND created_at >= ? AND created_at < ?", dyUser.ID, todayStart, todayEnd).
		Count(&count).Error

	if err != nil {
		global.GVA_LOG.Error("检查选品记录失败!", zap.Error(err))
		response.FailWithMessage("系统错误，请稍后再试", c)
		return
	}

	if count > 0 {
		response.FailWithMessage("今日已经为该抖音用户执行过选品，请明天再试", c)
		return
	}

	// 立即返回响应，告知用户请求已接收
	response.OkWithMessage("选品请求已发送，请稍后在【选品中心】-【商品列表】下查看结果", c)

	// 在后台使用goroutine继续执行获取商品的任务
	go func() {
		err := dyProductService.GrabDyProductList(req, dyUser.ID, userID, 1)
		if err != nil {
			global.GVA_LOG.Error("后台获取商品Feed流失败!", zap.Error(err))
		} else {
			global.GVA_LOG.Info("后台获取商品Feed流成功!")
		}
	}()
}

// GetProductListDetail
// @Tags      DyProduct
// @Summary   获取商品列表详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.DyProductListDetailRequest                                        true  "页码, 每页大小, 抖音用户token"
// @Success   200   {object}  response.Response{data=response.DyProductListDetailResponse,msg=string}   "获取成功"
// @Router    /douyin/product/list-detail [get]
func (api *DyProductApi) GetProductListDetail(c *gin.Context) {
	var req request.DyProductListDetailRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	result, err := dyProductService.GetProductList(req, userID)
	if err != nil {
		global.GVA_LOG.Error("获取商品列表失败!", zap.Error(err))
		response.FailWithMessage("获取商品列表失败", c)
		return
	}

	response.OkWithDetailed(result, "获取成功", c)
}

// GetProductUserList
// @Tags      DyProduct
// @Summary   商品页获取抖音用户列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200  {object}  response.Response{data=response.ProductUserListResponse,msg=string}  "获取成功"
// @Router    /douyin/product/user-list [get]
func (s *DyProductApi) GetProductUserList(c *gin.Context) {
	// 获取当前用户ID
	userId := utils.GetUserID(c)

	// 调用服务获取抖音用户列表
	list, err := dyProductService.GetProductUserList(userId)
	if err != nil {
		global.GVA_LOG.Error("获取抖音用户列表失败!", zap.Error(err))
		response.FailWithMessage("获取抖音用户列表失败: "+err.Error(), c)
		return
	}

	// 返回成功响应
	response.OkWithDetailed(gin.H{"list": list}, "获取成功", c)
}
