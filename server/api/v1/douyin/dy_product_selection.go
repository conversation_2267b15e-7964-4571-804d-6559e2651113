package douyin

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	douyinReq "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DyProductSelectionApi struct{}

// GetProductSelectionList
// @Tags      DyProductSelection
// @Summary   获取商品选品记录列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.PageInfo                                        true  "页码, 每页大小"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取成功"
// @Router    /douyin/product/selection/list [get]
func (api *DyProductSelectionApi) GetProductSelectionList(c *gin.Context) {
	var pageInfo douyinReq.ProductSelectionListRequest
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := dyProductSelectionService.GetProductSelectionList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}