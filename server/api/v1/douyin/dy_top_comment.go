package douyin

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
)

type DyTopCommentApi struct{}

// GetCommentList 获取顶级评论列表
// @Tags DyTopComment
// @Summary 获取视频评论列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.DyTopCommentListRequest true "查询参数"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "返回评论列表"
// @Router /dyComment/getCommentList [get]
func (api *DyTopCommentApi) GetCommentList(c *gin.Context) {
	var params request.DyTopCommentListSearch
	if err := c.ShouldBindQuery(&params); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	currentUserID := utils.GetUserID(c)
	// 获取当前用户及其下级用户的ID列表
	userIds, err := userService.GetSubordinateUserIds(currentUserID)
	if err != nil {
		response.FailWithMessage("获取用户权限失败: "+err.Error(), c)
		return
	}
	userIds = append(userIds, currentUserID)

	list, total, err := dyTopCommentService.GetCommentList(params, userIds)
	if err != nil {
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     params.Page,
		PageSize: params.PageSize,
	}, "获取成功", c)
}
