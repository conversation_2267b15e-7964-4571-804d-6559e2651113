package douyin

import (
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/logic"
	logicRequest "github.com/flipped-aurora/gin-vue-admin/server/logic/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/gin-gonic/gin"
)

type DyChatApi struct{}

// GetContactList
// @Tags DouyinChat
// @Summary 获取抖音联系人列表
// @Produce json
// @Param dyUserId query int true "抖音用户ID"
// @Success 200 {object} response.Response{data=[]douyin.DyChatContactUser}
// @Router /api/douyin/chat/contact-list [get]
func (api *DyChatApi) GetContactList(c *gin.Context) {
	// 获取并验证参数
	var req struct {
		DyUserId int64 `form:"dyUserId" binding:"required" required:"true"`
	}
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage("参数解析失败", c)
		return
	}

	// 调用逻辑层
	chatLogic := logic.DyChatLogic{}
	list, err := chatLogic.GetContactList(req.DyUserId)
	if err != nil {
		response.FailWithMessage("查询失败："+err.Error(), c)
		return
	}

	response.OkWithData(list, c)
}

// SaveWebCookie
// @Tags DouyinChat
// @Summary 保存抖音web端cookie
// @Accept json
// @Produce json
// @Param data body struct {
//
//	    ID     uint   `json:"id"`
//	    Cookie string `json:"cookie"`
//	} true "请求参数"
//
// @Success 200 {object} response.Response
// @Router /api/douyin/chat/save-web-cookie [post]
func (api *DyChatApi) SaveWebCookie(c *gin.Context) {
	var req struct {
		ID     uint   `json:"id"`
		Cookie string `json:"cookie"`
	}

	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误："+err.Error(), c)
		return
	}

	// 获取用户信息
	user, err := dyUserService.GetUserByID(req.ID)
	if err != nil {
		response.FailWithMessage("用户查询失败："+err.Error(), c)
		return
	}

	// 存储到Redis
	redisKey := fmt.Sprintf(douyin.DyWebCookieKey, user.UniqueId)
	if err := global.GVA_REDIS.Set(c, redisKey, req.Cookie, -1).Err(); err != nil {
		response.FailWithMessage("存储失败："+err.Error(), c)
		return
	}

	response.OkWithMessage("保存成功", c)
}

// SendMessage
// @Tags DouyinChat
// @Summary 发送消息
// @Accept json
// @Produce json
// @Param data body struct {sendId int64, receiveId int64, message string} true "请求参数"
// @Success 200 {object} response.Response
// @Router /api/douyin/chat/send-message [post]
func (api *DyChatApi) SendMessage(c *gin.Context) {
	var req logicRequest.DyChatSendMessageRequest
	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误："+err.Error(), c)
		return
	}
	// 调用逻辑层
	chatLogic := logic.DyChatLogic{}
	err := chatLogic.SendMessage(req)
	if err != nil {
		response.FailWithMessage("发送失败："+err.Error(), c)
		return
	}

	response.OkWithMessage("发送成功", c)
}
