package douyin

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	common "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DyUserCategoryApi struct{}

// GetCategoryList
// @Tags      GetDyUserCategoryList
// @Summary   抖音用户分类列表
// @Security  DyUserCategory
// @Produce   application/json
// @Success   200   {object}  response.Response{data=douyin.DyUserCategory,msg=string}  "抖音用户分类列表"
// @Router    /dyUserCategory/getCategoryList [get]
func (a *DyUserCategoryApi) GetCategoryList(c *gin.Context) {
	userID := utils.GetUserID(c)
	res, err := dyUserCategoryService.GetCategoryList(userID)
	if err != nil {
		global.GVA_LOG.Error("获取分类列表失败!", zap.Error(err))
		response.FailWithMessage("获取分类列表失败", c)
		return
	}
	response.OkWithData(res, c)
}

// AddCategory
// @Tags      AddDyUserCategory
// @Summary   添加抖音用户分类
// @Security  DyUserCategory
// @accept    application/json
// @Produce   application/json
// @Param     data  body      douyin.DyUserCategory  true  "抖音用户分类数据"
// @Success   200   {object}  response.Response{msg=string}   "添加抖音用户分类"
// @Router    /dyUserCategory/addCategory [post]
func (a *DyUserCategoryApi) AddCategory(c *gin.Context) {
	var req douyin.DyUserCategory
	if err := c.ShouldBindJSON(&req); err != nil {
		global.GVA_LOG.Error("参数错误!", zap.Error(err))
		response.FailWithMessage("参数错误", c)
		return
	}

	req.UserID = utils.GetUserID(c)
	if err := dyUserCategoryService.AddCategory(&req); err != nil {
		global.GVA_LOG.Error("创建/更新失败!", zap.Error(err))
		response.FailWithMessage("创建/更新失败："+err.Error(), c)
		return
	}
	response.OkWithMessage("创建/更新成功", c)
}

// DeleteCategory
// @Tags      DeleteDyUserCategory
// @Summary   删除抖音用户分类
// @Security  DyUserCategory
// @accept    application/json
// @Produce   application/json
// @Param     data  body      common.GetById                true  "分类id"
// @Success   200   {object}  response.Response{msg=string}  "删除抖音用户分类"
// @Router    /dyUserCategory/deleteCategory [post]
func (a *DyUserCategoryApi) DeleteCategory(c *gin.Context) {
	var req common.GetById
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userID := utils.GetUserID(c)
	if err := dyUserCategoryService.DeleteCategory(&req.ID, userID); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}
