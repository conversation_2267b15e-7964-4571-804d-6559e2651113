package douyin

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/logic"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
)

type DyAwemeApi struct{}

// parseIdsFromString 解析逗号分隔的ID字符串为uint数组
func parseIdsFromString(idsStr string) ([]uint, error) {
	if idsStr == "" {
		return nil, fmt.Errorf("ID字符串不能为空")
	}

	ids := strings.Split(idsStr, ",")
	result := make([]uint, 0, len(ids))

	for _, id := range ids {
		// 去除空格并检查是否为空
		id = strings.TrimSpace(id)
		if id == "" {
			continue
		}

		idUint, err := strconv.ParseUint(id, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("ID格式错误: %v", err)
		}
		result = append(result, uint(idUint))
	}

	if len(result) == 0 {
		return nil, fmt.Errorf("没有有效的ID")
	}

	return result, nil
}

// 获取作品列表
func (l *DyAwemeApi) GetAwemeList(c *gin.Context) {
	var req request.AwemeListSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	currentUserID := utils.GetUserID(c)
	// 获取当前用户及其下级用户的ID列表
	userIds, err := userService.GetSubordinateUserIds(currentUserID)
	if err != nil {
		response.FailWithMessage("获取用户权限失败: "+err.Error(), c)
		return
	}
	// 添加当前用户ID
	userIds = append(userIds, currentUserID)
	req.SysUserIds = userIds

	if req.DyUserIds != "" {
		dyUserIdsUint, err3 := parseIdsFromString(req.DyUserIds)
		if err3 != nil {
			response.FailWithMessage("用户选取有误: "+err3.Error(), c)
			return
		}

		var dyUsers []douyin.DyUser
		err = global.GVA_DB.Model(&douyin.DyUser{}).Where("id in (?)", dyUserIdsUint).Find(&dyUsers).Error
		if err != nil {
			response.FailWithMessage("查询抖音用户失败: "+err.Error(), c)
			return
		}
		for _, dyUser := range dyUsers {
			req.UniqueIds = append(req.UniqueIds, dyUser.UniqueId)
		}
	}

	list, total, err := dyAwemeService.GetAwemeList(req)
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}

	// 提取所有 aweme_id
	var awemeIds []string
	for _, item := range list {
		awemeIds = append(awemeIds, item.AwemeId)
	}

	videoMap := make(map[string]string)
	if len(awemeIds) > 0 {
		var videos []creative.Video
		err = global.GVA_DB.Where("aweme_id IN (?)", awemeIds).Find(&videos).Error
		if err != nil {
			response.FailWithMessage("查询 video 表失败: "+err.Error(), c)
			return
		}

		for _, video := range videos {
			videoMap[video.AwemeId] = video.Url // 假设 video 表有 Url 字段，根据实际情况调整
		}
	}

	data := make([]struct {
		ID               uint      `json:"ID"`
		AwemeId          string    `json:"awemeId"`
		AwemeType        int       `json:"awemeType"`
		CreateTime       int64     `json:"createTime"`
		Cover            string    `json:"cover"`
		Nickname         string    `json:"nickname"`
		UniqueId         string    `json:"uniqueId"`
		Desc             string    `json:"desc"`
		Duration         int64     `json:"duration"`
		GroupId          string    `json:"groupId"`
		StatusValue      int       `json:"status_value"`
		Status           string    `json:"status"`
		PrivateStatus    int       `json:"private_status"`
		InReviewing      int       `json:"in_reviewing"`
		Statistics       string    `json:"statistics"`
		CommentCount     int       `json:"comment_count"`
		DiggCount        int       `json:"digg_count"`
		PlayCount        int       `json:"play_count"`
		ShareCount       int       `json:"share_count"`
		ReviewStruct     string    `json:"review_struct"`
		ReviewStatus     int       `json:"review_status"`
		ReviewStatusDesc string    `json:"review_status_desc"`
		SysDispose       int       `json:"sys_dispose"`
		SysDisposeMethod int       `json:"sys_dispose_method"`
		SysDisposeTime   int64     `json:"sys_dispose_time"`
		SysUserId        int64     `json:"sys_user_id"`
		PlayUrl          string    `json:"playUrl"`
		UpdatedAt        time.Time `json:"updatedAt"`
	}, len(list))
	// 将 playUrl 填回 list 中
	for i, item := range list {
		data[i].ID = item.ID
		data[i].AwemeId = item.AwemeId
		data[i].AwemeType = item.AwemeType
		data[i].CreateTime = item.CreateTime
		data[i].Cover = item.Cover
		data[i].Nickname = item.Nickname
		data[i].UniqueId = item.UniqueId
		data[i].Desc = item.Desc
		data[i].Duration = item.Duration
		data[i].GroupId = item.GroupId
		data[i].StatusValue = item.StatusValue
		data[i].Status = item.Status
		data[i].PrivateStatus = item.PrivateStatus
		data[i].InReviewing = item.InReviewing
		data[i].Statistics = item.Statistics
		data[i].CommentCount = item.CommentCount
		data[i].DiggCount = item.DiggCount
		data[i].PlayCount = item.PlayCount
		data[i].ShareCount = item.ShareCount
		data[i].ReviewStruct = item.ReviewStruct
		data[i].ReviewStatus = item.ReviewStatus
		data[i].ReviewStatusDesc = item.ReviewStatusDesc
		data[i].SysDispose = item.SysDispose
		data[i].SysDisposeMethod = item.SysDisposeMethod
		data[i].SysDisposeTime = item.SysDisposeTime
		data[i].SysUserId = item.SysUserId
		data[i].UpdatedAt = item.UpdatedAt
		data[i].PlayUrl = ""
		if playUrl, exists := videoMap[item.AwemeId]; exists {
			data[i].PlayUrl = playUrl
		}
	}

	response.OkWithDetailed(response.PageResult{
		List:     data,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// 同步管理作品
func (l *DyAwemeApi) ManageAweme(c *gin.Context) {
	var req request.ManageAwemeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误："+err.Error(), c)
		return
	}

	dyUserIdsUint, err2 := parseIdsFromString(req.DyUserIds)
	if err2 != nil {
		response.FailWithMessage("用户选取有误: "+err2.Error(), c)
		return
	}

	var users []*douyin.DyUser
	err := global.GVA_DB.Model(&douyin.DyUser{}).Where("id in (?)", dyUserIdsUint).Find(&users).Error
	if err != nil {
		response.FailWithMessage("查询抖音用户失败: "+err.Error(), c)
		return
	}

	errUsers := []string{}
	awemeLogic := logic.DyAwemeLogic{}
	var wg sync.WaitGroup
	var mutex sync.Mutex

	for _, dyUser := range users {
		wg.Add(1)
		go func(user *douyin.DyUser) {
			defer wg.Done()

			// 设置一个5分钟的redis锁，防止重复执行
			ctx := context.Background()
			lockKey := fmt.Sprintf("manage_aweme_request:%s", user.UniqueId)
			// 锁存在时，跳过
			if global.GVA_REDIS.Exists(ctx, lockKey).Val() > 0 {
				errUsers = append(errUsers, fmt.Sprintf("%s:同步过于频繁，五分钟内只能同步一次", user.Nickname))
				return
			}

			// 设置锁，5分钟后自动释放
			_, err := global.GVA_REDIS.SetNX(ctx, lockKey, 1, time.Duration(5*60)*time.Second).Result()
			if err != nil {
				errUsers = append(errUsers, fmt.Sprintf("%s:设置锁失败", user.Nickname))
				return
			}

			err = awemeLogic.ManageAwemeList(user, 1)
			if err != nil {
				mutex.Lock()
				errUsers = append(errUsers, fmt.Sprintf("%s", err.Error()))
				mutex.Unlock()
			}
		}(dyUser)
	}
	wg.Wait()
	if len(errUsers) > 0 {
		response.FailWithMessage(fmt.Sprintf("%v", errUsers), c)
		return
	}

	response.Ok(c)
}

// 修改作品权限
func (l *DyAwemeApi) UpdateAweme(c *gin.Context) {
	var req struct {
		PrivateStatus int `json:"private_status"`
		DyAwemeId     int `json:"dyAwemeId"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误："+err.Error(), c)
		return
	}

	// 获取作品信息
	var aweme douyin.DyAweme
	if err := global.GVA_DB.Where("id =?", req.DyAwemeId).First(&aweme).Error; err != nil {
		response.FailWithMessage("获取作品信息失败："+err.Error(), c)
		return
	}

	if aweme.PrivateStatus == 4 {
		response.FailWithMessage("该作品已删除", c)
		return
	}

	// 根据aweme.UniqueId获取dyUser
	dyUser, err := dyUserService.GetUserByUniqueId(aweme.UniqueId)
	if err != nil {
		response.FailWithMessage("获取抖音用户信息失败："+err.Error(), c)
		return
	}

	awemeLogic := logic.DyAwemeLogic{}
	err = awemeLogic.EditAwemePermission(dyUser, &aweme, req.PrivateStatus, 2)
	if err != nil {
		response.FailWithMessage("远程修改作品失败:"+err.Error(), c)
		return
	}

	// 修改数据库的aweme信息
	err = dyAwemeService.UpdateAweme(&aweme)
	if err != nil {
		response.FailWithMessage("编辑失败:"+err.Error(), c)
		return
	}

	response.Ok(c)
}

// 删除作品
func (l *DyAwemeApi) DeleteAweme(c *gin.Context) {
	var req struct {
		DyAwemeId int `json:"dyAwemeId"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误："+err.Error(), c)
		return
	}

	// 获取作品信息
	var aweme douyin.DyAweme
	if err := global.GVA_DB.Where("id =?", req.DyAwemeId).First(&aweme).Error; err != nil {
		response.FailWithMessage("获取作品信息失败："+err.Error(), c)
		return
	}

	if aweme.PrivateStatus == 4 {
		response.FailWithMessage("作品已删除，无须处理", c)
		return
	}

	// 根据aweme.UniqueId获取dyUser
	dyUser, err := dyUserService.GetUserByUniqueId(aweme.UniqueId)
	if err != nil {
		response.FailWithMessage("获取抖音用户信息失败："+err.Error(), c)
		return
	}

	awemeLogic := logic.DyAwemeLogic{}
	err = awemeLogic.DeleteAweme(dyUser, &aweme, 2)
	if err != nil {
		response.FailWithMessage("远程删除作品失败:"+err.Error(), c)
		return
	}

	err = dyAwemeService.UpdateAweme(&aweme)
	if err != nil {
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}

	response.Ok(c)
}

// 批量处理作品
func (l *DyAwemeApi) BatchDisposeAweme(c *gin.Context) {
	var req request.BatchDisposeAweme
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误："+err.Error(), c)
		return
	}

	awemeIdsUint, err1 := parseIdsFromString(req.DyAwemeIds)
	if err1 != nil {
		response.FailWithMessage("作品选取有误: "+err1.Error(), c)
		return
	}

	var awemes []*douyin.DyAweme
	err := global.GVA_DB.Model(&douyin.DyAweme{}).Where("id in (?)", awemeIdsUint).Find(&awemes).Error
	if err != nil {
		response.FailWithMessage("查询作品失败: "+err.Error(), c)
		return
	}

	var uniqueIds []string
	var awemeIds []uint
	uniqueIdMap := make(map[string]struct{})
	for _, aweme := range awemes {
		// 如果作品已删除，跳过
		if aweme.PrivateStatus == 4 {
			continue
		}

		// 如果作品的可视权限与要修改的一致，跳过
		if (req.Action == 9 && aweme.PrivateStatus == 0) || (req.Action == 10 && aweme.PrivateStatus == 1) || (req.Action == 11 && aweme.PrivateStatus == 2) {
			continue
		}

		awemeIds = append(awemeIds, aweme.ID)

		if _, existed := uniqueIdMap[aweme.UniqueId]; !existed {
			uniqueIdMap[aweme.UniqueId] = struct{}{}
			uniqueIds = append(uniqueIds, aweme.UniqueId)
		}
	}

	updateData := map[string]any{
		"sys_dispose":        req.Action,
		"sys_dispose_method": 2,
		"sys_dispose_time":   0,
	}
	err = global.GVA_DB.Model(&douyin.DyAweme{}).Where("id in (?)", awemeIds).Updates(updateData).Error
	if err != nil {
		response.FailWithMessage("批量操作失败: "+err.Error(), c)
		return
	}

	// 将成员添加到集合中
	if len(uniqueIds) > 0 {
		// 将 []string 类型的 uniqueIds 转换为 []interface{} 类型
		args := make([]interface{}, len(uniqueIds))
		for i, v := range uniqueIds {
			args[i] = v
		}
		err = global.GVA_REDIS.SAdd(context.Background(), douyin.DisposeAwemeUserSetKey, args...).Err()
		if err != nil {
			response.FailWithMessage("添加到集合失败: "+err.Error(), c)
			return
		}
	}

	response.Ok(c)
}
