package douyin

import (
	"encoding/json"
	"fmt"

	common "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
)

type DyAuthUserApi struct{}

// GetAuthUrl 获取抖音授权URL
// @Tags      DyAuthUser
// @Summary   获取抖音授权URL
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.GetAuthUrlRequest  true  "授权参数"
// @Success   200   {object}  response.Response{data=string,msg=string}  "获取成功"
// @Router    /douyin/auth/url [post]
func (e *DyAuthUserApi) GetAuthUrl(c *gin.Context) {
	var req request.GetAuthUrlRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前登录用户
	userID := utils.GetUserID(c)

	// 获取授权URL
	authUrl, err := dyAuthUserService.GetAuthUrl(req.CategoryId, int64(userID), req.RedirectUri)
	if err != nil {
		response.FailWithMessage("获取授权URL失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(authUrl, "获取成功", c)
}

// AuthCallback 抖音授权回调
// @Tags      DyAuthUser
// @Summary   抖音授权回调
// @accept    application/json
// @Produce   application/json
// @Param     code  query     string  true  "授权码"
// @Param     state  query    string  true  "状态"
// @Success   200   {object}  response.Response{data=douyin.DyAuthUser,msg=string}  "授权成功"
// @Router    /douyin/auth/callback [get]
func (e *DyAuthUserApi) AuthCallback(c *gin.Context) {
	code := c.Query("code")
	state := c.Query("state")

	if code == "" {
		response.FailWithMessage("授权码不能为空", c)
		return
	}

	// 解析state参数，格式为：categoryId_userId
	stateParams, err := dyAuthUserService.ParseStateParams(state)
	if err != nil {
		response.FailWithMessage("无效的state参数: "+err.Error(), c)
		return
	}

	// 获取access_token
	tokenInfo, err := dyAuthUserService.GetAccessToken(code)
	if err != nil {
		response.FailWithMessage("获取访问令牌失败: "+err.Error(), c)
		return
	}

	// 使用access_token获取用户信息
	userInfo, err := dyAuthUserService.GetUserInfoByAccessToken(tokenInfo.AccessToken, tokenInfo.OpenId)
	if err != nil {
		response.FailWithMessage("获取用户信息失败: "+err.Error(), c)
		return
	}

	// 创建用户
	avatarBytes, _ := json.Marshal(userInfo.Avatar)
	avatarLargerBytes, _ := json.Marshal(userInfo.AvatarLarger)

	dyAuthUser := &douyin.DyAuthUser{
		OpenId:         tokenInfo.OpenId,
		UnionId:        userInfo.UnionId,
		AccessToken:    tokenInfo.AccessToken,
		RefreshToken:   tokenInfo.RefreshToken,
		ExpiresIn:      tokenInfo.ExpiresIn,
		Scope:          tokenInfo.Scope,
		CategoryId:     stateParams.CategoryId,
		SysUserId:      stateParams.UserId,
		Nickname:       userInfo.Nickname,
		Avatar:         string(avatarBytes),
		AvatarLarger:   string(avatarLargerBytes),
		UniqueId:       userInfo.UniqueId,
		FollowerCount:  userInfo.FollowerCount,
		FollowingCount: userInfo.FollowingCount,
		AwemeCount:     userInfo.AwemeCount,
		TotalFavorited: userInfo.TotalFavorited,
	}

	// 保存用户
	err = dyAuthUserService.AddUser(dyAuthUser)
	if err != nil {
		response.FailWithMessage("保存用户失败: "+err.Error(), c)
		return
	}

	// 返回HTML页面，显示授权成功并自动关闭窗口
	c.Header("Content-Type", "text/html")
	c.String(200, `
		<!DOCTYPE html>
		<html>
		<head>
			<meta charset="utf-8">
			<title>授权成功</title>
			<style>
				body { font-family: Arial, sans-serif; text-align: center; padding-top: 50px; }
				.success { color: #4CAF50; font-size: 24px; margin-bottom: 20px; }
			</style>
		</head>
		<body>
			<div class="success">授权成功！</div>
			<p>您已成功授权，窗口将在3秒后自动关闭</p>
			<script>
				setTimeout(function() {
					window.opener.postMessage({ type: 'auth_success', data: %s }, '*');
					window.close();
				}, 3000);
			</script>
		</body>
		</html>
	`, fmt.Sprintf("'%s'", userInfo.Nickname))
}

// GetUserList
// @Tags      DyAuthUser
// @Summary   获取抖音授权用户列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.DyAuthUserSearch                                        true  "页码, 每页大小, 筛选条件"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}      "获取成功"
// @Router    /douyin/auth/list [get]
func (e *DyAuthUserApi) GetUserList(c *gin.Context) {
	var pageInfo request.DyAuthUserSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	currentUserID := utils.GetUserID(c)

	// 获取当前用户及其下级用户的ID列表
	userIds, err := authUserService.GetSubordinateUserIds(currentUserID)
	if err != nil {
		response.FailWithMessage("获取用户权限失败: "+err.Error(), c)
		return
	}
	// 添加当前用户ID
	userIds = append(userIds, currentUserID)

	list, total, err := dyAuthUserService.GetUserList(pageInfo, userIds)
	if err != nil {
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// DeleteUser
// @Tags      DyAuthUser
// @Summary   删除抖音授权用户
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      common.GetById                true  "用户ID"
// @Success   200   {object}  response.Response{msg=string}  "删除成功"
// @Router    /douyin/auth/delete [delete]
func (e *DyAuthUserApi) DeleteUser(c *gin.Context) {
	var req common.GetById
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := dyAuthUserService.DeleteUser(uint(req.ID)); err != nil {
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// ToggleProductEnabled
// @Tags      DyAuthUser
// @Summary   切换选品状态
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.ToggleAuthProductEnabledRequest  true  "用户ID和选品状态"
// @Success   200   {object}  response.Response{msg=string}        "操作成功"
// @Router    /douyin/auth/toggle-product [post]
func (e *DyAuthUserApi) ToggleProductEnabled(c *gin.Context) {
	var req request.ToggleAuthProductEnabledRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := dyAuthUserService.ToggleProductEnabled(uint(req.ID), req.Enabled); err != nil {
		response.FailWithMessage("操作失败: "+err.Error(), c)
		return
	}

	statusText := "关闭"
	if req.Enabled {
		statusText = "开启"
	}
	response.OkWithMessage("选品已"+statusText, c)
}

// RefreshUserInfo
// @Tags      DyAuthUser
// @Summary   刷新用户信息
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      common.GetById                                  true  "用户ID"
// @Success   200   {object}  response.Response{data=douyin.DyAuthUser,msg=string}  "刷新成功"
// @Router    /douyin/auth/refresh [post]
func (e *DyAuthUserApi) RefreshUserInfo(c *gin.Context) {
	var req common.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage("参数解析失败", c)
		return
	}

	// 刷新用户信息
	err = dyAuthUserService.RefreshUserInfo(uint(req.ID))
	if err != nil {
		response.FailWithMessage("刷新用户信息失败: "+err.Error(), c)
		return
	}

	// 获取更新后的用户信息
	updatedUser, err := dyAuthUserService.GetUserByID(uint(req.ID))
	if err != nil {
		response.FailWithMessage("获取用户信息失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(updatedUser, "刷新成功", c)
}
