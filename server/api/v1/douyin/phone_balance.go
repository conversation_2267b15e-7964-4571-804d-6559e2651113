package douyin

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	commonReq "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type PhoneBalanceApi struct{}

// QueryPhoneBalance 查询手机话费余额
// @Tags PhoneBalance
// @Summary 查询手机话费余额
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.PhoneBalanceQuery true "手机号码和运营商类型"
// @Success 200 {object} response.Response{data=response.PhoneBalanceResponse} "成功"
// @Router /douyin/phone-balance/query [post]
func (api *PhoneBalanceApi) QueryPhoneBalance(c *gin.Context) {
	var req request.PhoneBalanceQuery
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 获取用户ID和IP
	userId := utils.GetUserID(c)
	clientIP := c.ClientIP()

	// 调用服务
	resp, err := phoneBalanceService.QueryPhoneBalance(req, userId, clientIP)
	if err != nil {
		global.GVA_LOG.Error("查询手机话费余额失败", zap.Error(err))
		response.FailWithMessage("查询手机话费余额失败: "+err.Error(), c)
		return
	}

	response.OkWithData(resp, c)
}

// GetPhoneBalanceList 获取手机话费余额查询记录列表
// @Tags PhoneBalance
// @Summary 获取手机话费余额查询记录列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PhoneBalanceSearch true "查询参数"
// @Success 200 {object} response.Response{data=response.PageResult,total=int64,list=[]douyin.PhoneBalance} "成功"
// @Router /douyin/phone-balance/list [get]
func (api *PhoneBalanceApi) GetPhoneBalanceList(c *gin.Context) {
	var req request.PhoneBalanceSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	// 获取当前用户ID
	currentUserID := utils.GetUserID(c)

	// 获取当前用户及其下级用户的ID列表
	userIds, err := userService.GetSubordinateUserIds(currentUserID)
	if err != nil {
		response.FailWithMessage("获取用户权限失败: "+err.Error(), c)
		return
	}
	// 添加当前用户ID
	userIds = append(userIds, currentUserID)

	list, total, err := phoneBalanceService.GetPhoneBalanceList(req, userIds)
	if err != nil {
		global.GVA_LOG.Error("获取手机话费余额查询记录列表失败", zap.Error(err))
		response.FailWithMessage("获取手机话费余额查询记录列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// DeletePhoneBalanceRecord 删除手机话费余额查询记录
// @Tags PhoneBalance
// @Summary 删除手机话费余额查询记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body commonReq.GetById true "记录ID"
// @Success 200 {object} response.Response "成功"
// @Router /douyin/phone-balance/delete [delete]
func (api *PhoneBalanceApi) DeletePhoneBalanceRecord(c *gin.Context) {
	var req commonReq.GetById
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if err := phoneBalanceService.DeletePhoneBalanceRecord(uint(req.ID)); err != nil {
		global.GVA_LOG.Error("删除手机话费余额查询记录失败", zap.Error(err))
		response.FailWithMessage("删除手机话费余额查询记录失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// GetOperatorTypes 获取支持的运营商类型
// @Tags PhoneBalance
// @Summary 获取支持的运营商类型
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]map[string]string} "成功"
// @Router /douyin/phone-balance/operator-types [get]
func (api *PhoneBalanceApi) GetOperatorTypes(c *gin.Context) {
	types := phoneBalanceService.GetOperatorTypes()
	response.OkWithData(types, c)
}

// RefreshPhoneBalance 刷新手机话费余额
// @Tags PhoneBalance
// @Summary 刷新手机话费余额
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body commonReq.GetById true "记录ID"
// @Success 200 {object} response.Response{data=response.PhoneBalanceResponse} "成功"
// @Router /douyin/phone-balance/refresh [post]
func (api *PhoneBalanceApi) RefreshPhoneBalance(c *gin.Context) {
	var req commonReq.GetById
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 获取用户ID和IP
	userId := utils.GetUserID(c)
	clientIP := c.ClientIP()

	// 调用服务
	resp, err := phoneBalanceService.RefreshPhoneBalance(uint(req.ID), userId, clientIP)
	if err != nil {
		global.GVA_LOG.Error("刷新手机话费余额失败", zap.Error(err))
		response.FailWithMessage("刷新手机话费余额失败: "+err.Error(), c)
		return
	}

	response.OkWithData(resp, c)
}

// BatchRefreshPhoneBalance 批量刷新手机话费余额
// @Tags PhoneBalance
// @Summary 批量刷新手机话费余额
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body commonReq.IdsReq true "记录ID列表"
// @Success 200 {object} response.Response{data=[]response.PhoneBalanceResponse} "成功"
// @Router /douyin/phone-balance/batch-refresh [post]
func (api *PhoneBalanceApi) BatchRefreshPhoneBalance(c *gin.Context) {
	var req commonReq.IdsReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 获取用户ID和IP
	userId := utils.GetUserID(c)
	clientIP := c.ClientIP()

	// 将int转为uint
	ids := make([]uint, len(req.Ids))
	for i, id := range req.Ids {
		ids[i] = uint(id)
	}

	// 调用服务
	results, err := phoneBalanceService.BatchRefreshPhoneBalance(ids, userId, clientIP)
	if err != nil {
		global.GVA_LOG.Error("批量刷新手机话费余额失败", zap.Error(err))
		response.FailWithMessage("批量刷新手机话费余额失败: "+err.Error(), c)
		return
	}

	response.OkWithData(results, c)
}

// UpdatePhoneBalanceRecord 更新手机话费余额记录
func (p *PhoneBalanceApi) UpdatePhoneBalanceRecord(c *gin.Context) {
	var req request.PhoneBalanceUpdate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = phoneBalanceService.UpdatePhoneBalanceRecord(req)
	if err != nil {
		global.GVA_LOG.Error("更新手机话费余额记录失败!", zap.Error(err))
		response.FailWithMessage("更新手机话费余额记录失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// SearchPhone 模糊查询手机号
// @Tags PhoneBalance
// @Summary 模糊查询手机号
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SearchPhone true "模糊查询手机号的请求参数"
// @Success 200 {object} response.Response{data=response.PhoneBalanceResponse} "成功"
// @Router /douyin/phone-balance/search [post]
func (api *PhoneBalanceApi) SearchPhone(c *gin.Context) {
	var req request.SearchPhone
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}
	if len(req.PhoneNumber) < 3 {
		response.Ok(c)
		return
	}
	// 只能查询自己或者下级用户的手机号
	userIds, err := userService.GetSubordinateUserIds(utils.GetUserID(c))
	if err != nil {
		response.FailWithMessage("获取用户权限失败: "+err.Error(), c)
		return
	}
	userIds = append(userIds, utils.GetUserID(c))

	resp, err := phoneBalanceService.SearchPhone(req.PhoneNumber, userIds)
	if err != nil {
		response.FailWithMessage("模糊查询手机号失败: "+err.Error(), c)
		return
	}

	response.OkWithData(resp, c)
}

// CreatePhoneBalanceRecord 创建手机话费余额记录（仅录入）
// @Tags PhoneBalance
// @Summary 创建手机话费余额记录（仅录入）
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.PhoneBalanceQuery true "手机号码、运营商类型和实名（可选）"
// @Success 200 {object} response.Response "成功"
// @Router /douyin/phone-balance/create [post]
func (api *PhoneBalanceApi) CreatePhoneBalanceRecord(c *gin.Context) {
	var req request.PhoneBalanceQuery // 复用 PhoneBalanceQuery 结构体，如果字段不同需新建
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 获取用户ID
	userId := utils.GetUserID(c)

	// 调用服务
	if err := phoneBalanceService.CreatePhoneBalanceRecord(req, userId); err != nil {
		global.GVA_LOG.Error("创建手机话费余额记录失败", zap.Error(err))
		response.FailWithMessage("创建手机话费余额记录失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("录入成功", c)
}

// UpdatePhoneBalanceRemark 更新手机话费余额记录备注
// @Tags PhoneBalance
// @Summary 更新手机话费余额记录备注
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.PhoneBalanceRemarkUpdate true "记录ID和备注"
// @Success 200 {object} response.Response "成功"
// @Router /douyin/phone-balance/update-remark [post]
func (api *PhoneBalanceApi) UpdatePhoneBalanceRemark(c *gin.Context) {
	var req request.PhoneBalanceRemarkUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if err := phoneBalanceService.UpdatePhoneBalanceRemark(req.ID, req.Remark); err != nil {
		global.GVA_LOG.Error("更新手机话费余额记录备注失败", zap.Error(err))
		response.FailWithMessage("更新手机话费余额记录备注失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新备注成功", c)
}
