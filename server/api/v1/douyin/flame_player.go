package douyin

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type FlamePlayer<PERSON><PERSON> struct{}

// GetPlayerList 获取火苗玩家列表
// @Tags      FlamePlayer
// @Summary   获取火苗玩家列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.FlamePlayerSearch  true  "页码, 每页大小, 搜索条件"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取成功"
// @Router    /douyin/flame-player/list [get]
func (e *FlamePlayerApi) GetPlayerList(c *gin.Context) {
	var pageInfo request.FlamePlayerSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := flamePlayerService.GetPlayerList(pageInfo)
	if err != nil {
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// SendFlame 赠送火苗
// @Tags      FlamePlayer
// @Summary   赠送火苗
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.SendFlameRequest  true  "赠送火苗参数"
// @Success   200   {object}  response.Response{data=response.SendFlameResponse,msg=string}  "赠送成功"
// @Router    /douyin/flame-player/send-flame [post]
func (e *FlamePlayerApi) SendFlame(c *gin.Context) {
	var req request.SendFlameRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	result, err := flameApiService.SendFlame(req)
	if err != nil {
		global.GVA_LOG.Error("赠送火苗失败!", zap.Error(err))
		response.FailWithMessage("赠送火苗失败", c)
		return
	}

	// 更新玩家的IsSentFlame状态
	var player douyin.FlamePlayer
	if err := global.GVA_DB.Where("uid = ?", req.ToUserID).First(&player).Error; err == nil {
		player.IsSentFlame = true
		if updateErr := global.GVA_DB.Model(&player).Update("is_sent_flame", true).Error; updateErr != nil {
			global.GVA_LOG.Error("更新玩家赠送状态失败", zap.Error(updateErr), zap.String("uid", req.ToUserID))
		} else {
			global.GVA_LOG.Info("更新玩家赠送状态成功", zap.String("uid", req.ToUserID))
		}
	}

	// 赠送成功后，获取并更新赠送者的火苗余额
	flameUser, err := flameUserService.GetUserByToken(req.Token)
	if err != nil {
		global.GVA_LOG.Error("获取赠送者信息失败!", zap.Error(err))
	} else {
		// 获取最新的火苗余额
		balanceReq := request.GetFlameBalanceRequest{
			Token: req.Token,
		}
		balanceResp, err := flameApiService.GetFlameBalance(balanceReq)
		if err != nil {
			global.GVA_LOG.Error("获取火苗余额失败!", zap.Error(err))
		} else {
			// 更新用户的火苗余额
			err = flameUserService.UpdateFlameBalance(flameUser.ID, int64(balanceResp.FlameNum))
			if err != nil {
				global.GVA_LOG.Error("更新火苗余额失败!", zap.Error(err))
			} else {
				global.GVA_LOG.Info("更新火苗余额成功", zap.Uint("用户ID", flameUser.ID), zap.Int64("火苗余额", int64(balanceResp.FlameNum)))
			}
		}
	}

	response.OkWithDetailed(result, "赠送成功", c)
}

// BatchSendFlame 批量赠送火苗
// @Tags      FlamePlayer
// @Summary   批量赠送火苗
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.BatchSendFlameRequest  true  "批量赠送火苗参数"
// @Success   200   {object}  response.Response{data=map[string]interface{},msg=string}  "赠送成功"
// @Router    /douyin/flame-player/batch-send-flame [post]
func (e *FlamePlayerApi) BatchSendFlame(c *gin.Context) {
	var req request.BatchSendFlameRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 立即返回响应，告知前端请求已接收，正在后台处理
	response.OkWithDetailed(map[string]interface{}{
		"status":  "processing",
		"message": "批量赠送请求已接收",
	}, "批量赠送请求已接收，系统正在后台处理中。为保证稳定性，每位玩家赠送间隔为3秒，请耐心等待。您可以继续使用其他功能，无需等待处理完成。", c)

	// 使用协程在后台执行批量赠送操作
	go func() {
		// 执行批量赠送操作，添加 3 秒的频率限制
		successCount, failCount, err := flamePlayerService.BatchSendFlameWithRateLimit(
			req,
			flameApiService,
			3, // 3秒的频率限制
		)

		if err != nil {
			global.GVA_LOG.Error("批量赠送火苗失败!", zap.Error(err))
			return
		}

		// 批量赠送成功后，获取并更新赠送者的火苗余额
		flameUser, err := flameUserService.GetUserByToken(req.Token)
		if err != nil {
			global.GVA_LOG.Error("获取赠送者信息失败!", zap.Error(err))
		} else {
			// 获取最新的火苗余额
			balanceReq := request.GetFlameBalanceRequest{
				Token: req.Token,
			}
			balanceResp, err := flameApiService.GetFlameBalance(balanceReq)
			if err != nil {
				global.GVA_LOG.Error("获取火苗余额失败!", zap.Error(err))
			} else {
				// 更新用户的火苗余额
				err = flameUserService.UpdateFlameBalance(flameUser.ID, int64(balanceResp.FlameNum))
				if err != nil {
					global.GVA_LOG.Error("更新火苗余额失败!", zap.Error(err))
				} else {
					global.GVA_LOG.Info("更新火苗余额成功", zap.Uint("用户ID", flameUser.ID), zap.Int64("火苗余额", int64(balanceResp.FlameNum)))
				}
			}
		}

		global.GVA_LOG.Info("批量赠送火苗完成",
			zap.Int("成功数量", successCount),
			zap.Int("失败数量", failCount),
			zap.Int("总数量", successCount+failCount))
	}()
}
