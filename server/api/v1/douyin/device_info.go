package douyin

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	douyinRequest "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DeviceInfoApi struct{}

// CreateDeviceInfo 创建设备信息
// @Tags DeviceInfo
// @Summary 创建设备信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body douyinRequest.DeviceInfoRequest true "设备信息"
// @Success 200 {object} response.Response{data=response.DeviceInfoResponse} "成功"
// @Router /device/create [post]
func (e *DeviceInfoApi) CreateDeviceInfo(c *gin.Context) {
	var req douyinRequest.DeviceInfoRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage("参数解析失败", c)
		global.GVA_LOG.Error("参数解析失败!", zap.Error(err))
		return
	}

	resp, err := deviceInfoService.CreateDeviceInfo(req)
	if err != nil {
		response.FailWithMessage("创建设备信息失败: "+err.Error(), c)
		global.GVA_LOG.Error("创建设备信息失败!", zap.Error(err))
		return
	}

	response.OkWithData(resp, c)
}

// GetDeviceInfoList 获取设备信息列表
// @Tags DeviceInfo
// @Summary 获取设备信息列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "页码, 每页大小"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "成功"
// @Router /device/list [get]
func (e *DeviceInfoApi) GetDeviceInfoList(c *gin.Context) {
	var pageInfo request.PageInfo
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage("参数解析失败", c)
		global.GVA_LOG.Error("参数解析失败!", zap.Error(err))
		return
	}

	list, total, err := deviceInfoService.GetDeviceInfoList(pageInfo)
	if err != nil {
		response.FailWithMessage("获取设备信息列表失败: "+err.Error(), c)
		global.GVA_LOG.Error("获取设备信息列表失败!", zap.Error(err))
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}
