package douyin

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type FlameUserApi struct{}

// AddUser 添加火苗用户（账号授权）
// @Tags      FlameUser
// @Summary   添加火苗用户
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.AddFlameUserRequest  true  "火苗用户数据"
// @Success   200   {object}  response.Response{data=douyin.FlameUser,msg=string}  "创建成功"
// @Router    /douyin/flame/user/add [post]
func (e *FlameUserApi) AddUser(c *gin.Context) {
	var req request.AddFlameUserRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前登录用户
	userID := utils.GetUserID(c)

	// 获取用户信息
	// 尝试从IP池中获取可用IP
	var bindIP string
	availableIPs, err := dyIPService.GetAvailableIP()
	if err == nil && len(availableIPs) > 0 {
		// 如果有可用IP，选择第一个（已按用户数排序）
		bindIP = availableIPs[0]
	}
	// 如果没有可用IP，授权失败，因为没IP无法从火苗获取到用户信息
	if bindIP == "" {
		response.FailWithMessage("没有可用IP，授权失败", c)
		return
	}

	// 调用火苗API获取用户信息
	userInfo, err := awemeService.GetUserInfo(req.ImToken, bindIP)
	if err != nil {
		response.FailWithMessage("获取用户信息失败: "+err.Error(), c)
		return
	}

	// 创建用户
	flameUser := &douyin.FlameUser{
		ImToken:    req.ImToken,
		CategoryId: req.CategoryId,
		SysUserId:  int64(userID),
		Did:        req.Did,
		BindIP:     bindIP,
	}

	// 复制用户信息
	avatarBytes, _ := json.Marshal(userInfo.User.Avatar168x168)
	avatar := string(avatarBytes)
	flameUser.UID = userInfo.User.UID
	flameUser.Nickname = userInfo.User.Nickname
	flameUser.Avatar = avatar
	flameUser.UniqueId = userInfo.User.UniqueId
	flameUser.ShortId = userInfo.User.ShortId
	flameUser.SecUid = userInfo.User.SecUid
	flameUser.FollowerCount = userInfo.User.FollowerCount
	flameUser.FollowingCount = userInfo.User.FollowingCount
	flameUser.AwemeCount = userInfo.User.AwemeCount
	flameUser.TotalFavorited = userInfo.User.TotalFavorited
	flameUser.AccountRegion = userInfo.User.AccountRegion
	flameUser.Province = userInfo.User.Province
	flameUser.City = userInfo.User.City
	flameUser.CollegeName = userInfo.User.CollegeName
	flameUser.BindPhone = userInfo.User.BindPhone
	flameUser.Birthday = userInfo.User.Birthday
	flameUser.Gender = userInfo.User.Gender
	flameUser.Signature = userInfo.User.Signature

	err = flameUserService.AddUser(flameUser)
	if err != nil {
		response.FailWithMessage("创建用户失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(flameUser, "创建成功", c)
}

// 将头像URL列表转换为JSON字符串
func getAvatarJson(urlList []string) string {
	if len(urlList) == 0 {
		return ""
	}

	avatarData := map[string]interface{}{
		"url_list": urlList,
	}

	jsonBytes, err := json.Marshal(avatarData)
	if err != nil {
		return ""
	}

	return string(jsonBytes)
}

// GetUserList 获取火苗用户列表
// @Tags      FlameUser
// @Summary   获取火苗用户列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.FlameUserSearch  true  "页码, 每页大小, 搜索条件"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取成功"
// @Router    /douyin/flame/user/list [get]
func (e *FlameUserApi) GetUserList(c *gin.Context) {
	var pageInfo request.FlameUserSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 默认所有用户都可以查看所有数据
	var sysUserIds []uint
	// 如果需要限制，可以在此处添加权限检查
	// 这里简单处理，不做权限限制

	list, total, err := flameUserService.GetUserList(pageInfo, sysUserIds)
	if err != nil {
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// DeleteUser 删除火苗用户
// @Tags      FlameUser
// @Summary   删除火苗用户
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      douyin.FlameUser  true  "火苗用户ID"
// @Success   200   {object}  response.Response{msg=string}  "删除成功"
// @Router    /douyin/flame/user/delete [delete]
func (e *FlameUserApi) DeleteUser(c *gin.Context) {
	var flameUser douyin.FlameUser
	err := c.ShouldBindJSON(&flameUser)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = flameUserService.DeleteUser(flameUser.ID)
	if err != nil {
		response.FailWithMessage("删除失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// ToggleCollectEnabled 切换采集状态
// @Tags      FlameUser
// @Summary   切换采集状态
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.ToggleCollectEnabledRequest  true  "火苗用户ID和状态"
// @Success   200   {object}  response.Response{msg=string}  "操作成功"
// @Router    /douyin/flame/user/toggle-collect [post]
func (e *FlameUserApi) ToggleCollectEnabled(c *gin.Context) {
	var req request.ToggleCollectEnabledRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = flameUserService.ToggleCollectEnabled(req.ID, req.Enabled)
	if err != nil {
		response.FailWithMessage("操作失败", c)
		return
	}
	response.OkWithMessage("操作成功", c)
}

// UpdateUserIP 更新用户IP
// @Tags      FlameUser
// @Summary   更新用户IP
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.UpdateFlameUserIPRequest  true  "火苗用户ID和IP"
// @Success   200   {object}  response.Response{msg=string}  "更新成功"
// @Router    /douyin/flame/user/update-ip [post]
func (e *FlameUserApi) UpdateUserIP(c *gin.Context) {
	var req request.UpdateFlameUserIPRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = flameUserService.UpdateUserIP(req.ID, req.BindIP)
	if err != nil {
		response.FailWithMessage("更新失败", c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// BindDevice 绑定设备
// @Tags      FlameUser
// @Summary   绑定设备
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.BindFlameDeviceRequest  true  "火苗用户ID"
// @Success   200   {object}  response.Response{msg=string}  "绑定成功"
// @Router    /douyin/flame/user/bind-device [post]
func (e *FlameUserApi) BindDevice(c *gin.Context) {
	var req request.BindFlameDeviceRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 更新用户的did字段
	err = flameUserService.UpdateDid(req.ID, req.Did)
	if err != nil {
		response.FailWithMessage("绑定设备失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("绑定设备成功", c)
}

// StartCollect 开始采集
// @Tags      FlameUser
// @Summary   开始采集
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      douyin.FlameUser  true  "火苗用户ID"
// @Success   200   {object}  response.Response{msg=string}  "采集成功"
// @Router    /douyin/flame/user/start-collect [post]
func (e *FlameUserApi) StartCollect(c *gin.Context) {
	var flameUser douyin.FlameUser
	err := c.ShouldBindJSON(&flameUser)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取用户信息
	user, err := flameUserService.GetUserByToken(flameUser.ImToken)
	if err != nil {
		response.FailWithMessage("获取用户信息失败: "+err.Error(), c)
		return
	}

	// 开启采集
	err = flameUserService.ToggleCollectEnabled(user.ID, true)
	if err != nil {
		response.FailWithMessage("开启采集失败: "+err.Error(), c)
		return
	}

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 6*time.Hour)

	// 启动新的goroutine执行采集任务
	go func() {
		defer cancel()
		err := flameUserService.GrabFlameUsers(ctx, user.ID, 1)
		if err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("用户 %s (ID: %d) 采集任务失败: %v", user.Nickname, user.ID, err))
		}
	}()

	response.OkWithMessage("采集任务已启动", c)
}

// GetGroupedUserList 获取分组火苗用户列表
// @Tags      FlameUser
// @Summary   获取分组火苗用户列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{data=response.FlameUserListResponse,msg=string}  "获取成功"
// @Router    /douyin/flame/user/grouped-list [get]
func (e *FlameUserApi) GetGroupedUserList(c *gin.Context) {
	// 获取当前用户ID
	userId := utils.GetUserID(c)

	// 调用服务获取火苗用户列表
	list, err := flameUserService.GetGroupedUserList(userId)
	if err != nil {
		global.GVA_LOG.Error("获取火苗用户列表失败!", zap.Error(err))
		response.FailWithMessage("获取火苗用户列表失败: "+err.Error(), c)
		return
	}

	// 返回成功响应
	response.OkWithDetailed(gin.H{"list": list}, "获取成功", c)
}

// RefreshUserInfo 刷新用户信息和火苗数量
// @Tags      FlameUser
// @Summary   刷新用户信息和火苗数量
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      douyin.FlameUser  true  "火苗用户ID"
// @Success   200   {object}  response.Response{data=douyin.FlameUser,msg=string}  "刷新成功"
// @Router    /douyin/flame/user/refresh [post]
func (e *FlameUserApi) RefreshUserInfo(c *gin.Context) {
	var flameUser douyin.FlameUser
	err := c.ShouldBindJSON(&flameUser)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取用户信息
	user, err := flameUserService.GetUserByID(flameUser.ID)
	if err != nil {
		response.FailWithMessage("获取用户信息失败: "+err.Error(), c)
		return
	}

	// 获取火苗余额
	balanceReq := request.GetFlameBalanceRequest{
		Token: user.ImToken,
	}
	balanceResp, err := flameApiService.GetFlameBalance(balanceReq)
	if err != nil {
		response.FailWithMessage("获取火苗余额失败: "+err.Error(), c)
		return
	}

	// 更新用户火苗数量
	err = flameUserService.UpdateFlameBalance(user.ID, int64(balanceResp.FlameNum))
	if err != nil {
		response.FailWithMessage("更新火苗余额失败: "+err.Error(), c)
		return
	}

	// 重新获取更新后的用户信息
	updatedUser, err := flameUserService.GetUserByID(user.ID)
	if err != nil {
		response.FailWithMessage("获取更新后的用户信息失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(updatedUser, "刷新成功", c)
}
