package douyin

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DyProductCategoryApi struct{}

var dyProductCategoryService = service.ServiceGroupApp.DouyinServiceGroup.DyProductCategoryService

// CreateProductCategory
// @Tags      DyProductCategory
// @Summary   创建商品分类
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.DyProductCategoryRequest  true  "分类信息"
// @Success   200   {object}  response.Response{msg=string}     "创建成功"
// @Router    /douyin/product-category [post]
func (api *DyProductCategoryApi) CreateProductCategory(c *gin.Context) {
	var req request.DyProductCategoryRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户信息
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	err = dyProductCategoryService.CreateProductCategory(req, userID)
	if err != nil {
		global.GVA_LOG.Error("创建商品分类失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("创建成功", c)
}

// UpdateProductCategory
// @Tags      DyProductCategory
// @Summary   更新商品分类
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id    path      uint                              true  "分类ID"
// @Param     data  body      request.DyProductCategoryRequest  true  "分类信息"
// @Success   200   {object}  response.Response{msg=string}     "更新成功"
// @Router    /douyin/product-category/:id [put]
func (api *DyProductCategoryApi) UpdateProductCategory(c *gin.Context) {
	var req request.DyProductCategoryRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	id := utils.StringToUint(c.Param("id"))
	if id == 0 {
		response.FailWithMessage("分类ID不能为空", c)
		return
	}

	err = dyProductCategoryService.UpdateProductCategory(id, req)
	if err != nil {
		global.GVA_LOG.Error("更新商品分类失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// GetProductCategoryList
// @Tags      DyProductCategory
// @Summary   获取商品分类列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.DyProductCategoryListRequest  true  "页码, 每页大小"
// @Success   200   {object}  response.Response{data=response.DyProductCategoryListResponse,msg=string}  "获取成功"
// @Router    /douyin/product-category [get]
func (api *DyProductCategoryApi) GetProductCategoryList(c *gin.Context) {
	var req request.DyProductCategoryListRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	result, err := dyProductCategoryService.GetProductCategoryList(req)
	if err != nil {
		global.GVA_LOG.Error("获取商品分类列表失败!", zap.Error(err))
		response.FailWithMessage("获取商品分类列表失败", c)
		return
	}

	response.OkWithDetailed(result, "获取成功", c)
}

// GetProductCategoryById
// @Tags      DyProductCategory
// @Summary   根据ID获取商品分类
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id    path      uint                            true  "分类ID"
// @Success   200   {object}  response.Response{data=douyin.DyProductCategory,msg=string}  "获取成功"
// @Router    /douyin/product-category/:id [get]
func (api *DyProductCategoryApi) GetProductCategoryById(c *gin.Context) {
	id := utils.StringToUint(c.Param("id"))
	if id == 0 {
		response.FailWithMessage("分类ID不能为空", c)
		return
	}

	category, err := dyProductCategoryService.GetProductCategoryById(id)
	if err != nil {
		global.GVA_LOG.Error("获取商品分类详情失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithDetailed(category, "获取成功", c)
}

// DeleteProductCategory
// @Tags      DyProductCategory
// @Summary   删除商品分类
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id    path      uint                            true  "分类ID"
// @Success   200   {object}  response.Response{msg=string}   "删除成功"
// @Router    /douyin/product-category/:id [delete]
func (api *DyProductCategoryApi) DeleteProductCategory(c *gin.Context) {
	id := utils.StringToUint(c.Param("id"))
	if id == 0 {
		response.FailWithMessage("分类ID不能为空", c)
		return
	}

	err := dyProductCategoryService.DeleteProductCategory(id)
	if err != nil {
		global.GVA_LOG.Error("删除商品分类失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// UpdateCategoryStatus
// @Tags      DyProductCategory
// @Summary   更新分类状态
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.UpdateCategoryStatusRequest  true  "分类状态信息"
// @Success   200   {object}  response.Response{msg=string}        "更新成功"
// @Router    /douyin/product-category/status [put]
func (api *DyProductCategoryApi) UpdateCategoryStatus(c *gin.Context) {
	var req request.UpdateCategoryStatusRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = dyProductCategoryService.UpdateCategoryStatus(req.ID, req.Status)
	if err != nil {
		global.GVA_LOG.Error("更新分类状态失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// GetAllCategories
// @Tags      DyProductCategory
// @Summary   获取所有启用的分类
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{data=[]douyin.DyProductCategory,msg=string}  "获取成功"
// @Router    /douyin/product-category/all [get]
func (api *DyProductCategoryApi) GetAllCategories(c *gin.Context) {
	categories, err := dyProductCategoryService.GetAllCategories()
	if err != nil {
		global.GVA_LOG.Error("获取分类列表失败!", zap.Error(err))
		response.FailWithMessage("获取分类列表失败", c)
		return
	}

	response.OkWithDetailed(categories, "获取成功", c)
}

// UpdateCategorySort
// @Tags      DyProductCategory
// @Summary   更新分类排序
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.UpdateCategorySortRequest  true  "分类排序信息"
// @Success   200   {object}  response.Response{msg=string}      "更新成功"
// @Router    /douyin/product-category/sort [put]
func (api *DyProductCategoryApi) UpdateCategorySort(c *gin.Context) {
	var req request.UpdateCategorySortRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = dyProductCategoryService.UpdateCategorySort(req)
	if err != nil {
		global.GVA_LOG.Error("更新分类排序失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}
