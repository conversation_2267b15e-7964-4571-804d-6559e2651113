package douyin

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CreateProductManual
// @Tags      DyProductManual
// @Summary   创建手动录入商品
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.DyProductManualRequest  true  "商品信息"
// @Success   200   {object}  response.Response{msg=string}   "创建成功"
// @Router    /douyin/product-manual [post]
func (api *DyProductApi) CreateProductManual(c *gin.Context) {
	var req request.DyProductManualRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	err = dyProductService.CreateProductManual(req, userID)
	if err != nil {
		global.GVA_LOG.Error("创建商品失败!", zap.Error(err))
		response.FailWithMessage("创建商品失败", c)
		return
	}

	response.OkWithMessage("创建成功", c)
}

// UpdateProductManual
// @Tags      DyProductManual
// @Summary   更新手动录入商品
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id    path      uint                            true  "商品ID"
// @Param     data  body      request.DyProductManualRequest  true  "商品信息"
// @Success   200   {object}  response.Response{msg=string}   "更新成功"
// @Router    /douyin/product-manual/:id [put]
func (api *DyProductApi) UpdateProductManual(c *gin.Context) {
	var req request.DyProductManualRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	id := utils.StringToUint(c.Param("id"))
	if id == 0 {
		response.FailWithMessage("商品ID不能为空", c)
		return
	}

	err = dyProductService.UpdateProductManual(id, req)
	if err != nil {
		global.GVA_LOG.Error("更新商品失败!", zap.Error(err))
		response.FailWithMessage("更新商品失败", c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// GetProductManualList
// @Tags      DyProductManual
// @Summary   获取手动录入商品列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.DyProductManualListRequest  true  "页码, 每页大小"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取成功"
// @Router    /douyin/product-manual [get]
func (api *DyProductApi) GetProductManualList(c *gin.Context) {
	var req request.DyProductManualListRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	list, total, err := dyProductService.GetProductManualList(req, userID)
	if err != nil {
		global.GVA_LOG.Error("获取商品列表失败!", zap.Error(err))
		response.FailWithMessage("获取商品列表失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// GetProductManualById
// @Tags      DyProductManual
// @Summary   根据ID获取手动录入商品
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id    path      uint                            true  "商品ID"
// @Success   200   {object}  response.Response{data=douyin.DyProductManual,msg=string}  "获取成功"
// @Router    /douyin/product-manual/:id [get]
func (api *DyProductApi) GetProductManualById(c *gin.Context) {
	id := utils.StringToUint(c.Param("id"))
	if id == 0 {
		response.FailWithMessage("商品ID不能为空", c)
		return
	}

	product, err := dyProductService.GetProductManualById(id)
	if err != nil {
		global.GVA_LOG.Error("获取商品详情失败!", zap.Error(err))
		response.FailWithMessage("获取商品详情失败", c)
		return
	}

	response.OkWithDetailed(product, "获取成功", c)
}

// DeleteProductManual
// @Tags      DyProductManual
// @Summary   删除手动录入商品
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id    path      uint                            true  "商品ID"
// @Success   200   {object}  response.Response{msg=string}   "删除成功"
// @Router    /douyin/product-manual/:id [delete]
func (api *DyProductApi) DeleteProductManual(c *gin.Context) {
	id := utils.StringToUint(c.Param("id"))
	if id == 0 {
		response.FailWithMessage("商品ID不能为空", c)
		return
	}

	err := dyProductService.DeleteProductManual(id)
	if err != nil {
		global.GVA_LOG.Error("删除商品失败!", zap.Error(err))
		response.FailWithMessage("删除商品失败", c)
		return
	}

	response.OkWithMessage("删除成功", c)
}
