package douyin

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type FlamePlayerCollectionApi struct{}

// GetFlamePlayerCollectionList
// @Tags      FlamePlayerCollection
// @Summary   分页获取火苗采集记录列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.FlamePlayerCollectionSearch  true  "页码, 每页大小, 搜索条件"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取成功"
// @Router    /douyin/flamePlayerCollection/list [get]
func (api *FlamePlayerCollectionApi) GetFlamePlayerCollectionList(c *gin.Context) {
	var searchInfo request.FlamePlayerCollectionSearch
	err := c.ShouldBindQuery(&searchInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := flamePlayerCollectionService.GetCollectionList(searchInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     searchInfo.Page,
		PageSize: searchInfo.PageSize,
	}, "获取成功", c)
}
