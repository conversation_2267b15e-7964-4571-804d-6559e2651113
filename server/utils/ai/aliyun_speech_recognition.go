package ai

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"go.uber.org/zap"
)

// AliyunSpeechRecognitionProcessor 阿里云录音文件识别处理器
type AliyunSpeechRecognitionProcessor struct {
	AppKey       string
	URL          string
	TokenService *AliyunTokenService
}

// AliyunSpeechResponse 阿里云语音识别响应
type AliyunSpeechResponse struct {
	TaskId      string            `json:"task_id"`
	Status      int               `json:"status"`
	Message     string            `json:"message"`
	FlashResult AliyunFlashResult `json:"flash_result"`
	Result      string            `json:"result"` // 保留兼容性，由解析后的文本填充
}

// AliyunFlashResult 阿里云语音识别flash_result部分
type AliyunFlashResult struct {
	Duration  int                    `json:"duration"`
	Sentences []AliyunSpeechSentence `json:"sentences"`
}

// AliyunSpeechSentence 语音识别句子
type AliyunSpeechSentence struct {
	Text      string             `json:"text"`
	BeginTime int                `json:"begin_time"`
	EndTime   int                `json:"end_time"`
	ChannelId int                `json:"channel_id"`
	Words     []AliyunSpeechWord `json:"words"`
}

// AliyunSpeechWord 语音识别单词
type AliyunSpeechWord struct {
	Text      string `json:"text"`
	BeginTime string `json:"begin_time"`
	EndTime   string `json:"end_time"`
	Punc      string `json:"punc"`
}

// NewAliyunSpeechRecognitionProcessor 创建阿里云录音文件识别处理器实例
func NewAliyunSpeechRecognitionProcessor() *AliyunSpeechRecognitionProcessor {
	// 如果配置文件中的URL为空，使用默认URL
	url := global.GVA_CONFIG.AliyunSpeech.URL
	if url == "" {
		url = "https://nls-gateway-cn-shanghai.aliyuncs.com/stream/v1/FlashRecognizer"
	}

	// 创建token服务
	tokenService := NewAliyunTokenService(
		global.GVA_CONFIG.AliyunSpeech.AccessKeyId,
		global.GVA_CONFIG.AliyunSpeech.AccessKeySecret,
	)

	return &AliyunSpeechRecognitionProcessor{
		AppKey:       global.GVA_CONFIG.AliyunSpeech.AppKey,
		URL:          url,
		TokenService: tokenService,
	}
}

// RecognizeAudio 识别音频文件内容
func (p *AliyunSpeechRecognitionProcessor) RecognizeAudio(audioData []byte, format string, sampleRate int) (*AliyunSpeechResponse, error) {
	global.GVA_LOG.Info("开始阿里云语音识别",
		zap.String("format", format),
		zap.Int("sampleRate", sampleRate),
		zap.Int("audioSize", len(audioData)))

	// 获取动态token
	token, err := p.TokenService.GetToken()
	if err != nil {
		global.GVA_LOG.Error("获取阿里云语音识别token失败", zap.Error(err))
		return nil, fmt.Errorf("获取token失败: %v", err)
	}

	// 构建请求URL
	requestURL := fmt.Sprintf("%s?appkey=%s&token=%s&format=%s&sample_rate=%d",
		p.URL, p.AppKey, token, format, sampleRate)

	global.GVA_LOG.Debug("语音识别请求URL", zap.String("url", requestURL))

	// 创建HTTP请求
	req, err := http.NewRequest("POST", requestURL, bytes.NewReader(audioData))
	if err != nil {
		global.GVA_LOG.Error("创建HTTP请求失败", zap.Error(err))
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/octet-stream")
	req.Header.Set("Content-Length", fmt.Sprintf("%d", len(audioData)))

	// 发送请求
	client := &http.Client{
		Timeout: 120 * time.Second, // 设置120秒超时
	}

	global.GVA_LOG.Info("发送语音识别请求", zap.String("url", requestURL))
	resp, err := client.Do(req)
	if err != nil {
		global.GVA_LOG.Error("发送HTTP请求失败", zap.Error(err))
		return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		global.GVA_LOG.Error("读取响应失败", zap.Error(err))
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	global.GVA_LOG.Debug("语音识别响应", zap.String("response", string(body)))

	// 解析响应
	var speechResp AliyunSpeechResponse
	err = json.Unmarshal(body, &speechResp)
	if err != nil {
		global.GVA_LOG.Error("解析响应失败", zap.Error(err), zap.String("response", string(body)))
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查识别状态
	if speechResp.Status != 20000000 {
		global.GVA_LOG.Error("语音识别失败", zap.Int("status", speechResp.Status), zap.String("message", speechResp.Message))
		return nil, fmt.Errorf("语音识别失败，状态码: %d, 消息: %s", speechResp.Status, speechResp.Message)
	}

	// 从flash_result中提取并拼接文本
	speechResp.Result = p.extractTextFromFlashResult(&speechResp.FlashResult)

	global.GVA_LOG.Info("语音识别成功", zap.String("result", speechResp.Result))
	return &speechResp, nil
}

// RecognizeAudioFromURL 从URL识别音频内容
func (p *AliyunSpeechRecognitionProcessor) RecognizeAudioFromURL(audioURL string, format string, sampleRate int) (*AliyunSpeechResponse, error) {
	global.GVA_LOG.Info("开始从URL识别语音", zap.String("url", audioURL))

	// 下载音频文件
	resp, err := http.Get(audioURL)
	if err != nil {
		global.GVA_LOG.Error("下载音频文件失败", zap.Error(err))
		return nil, fmt.Errorf("下载音频文件失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		global.GVA_LOG.Error("下载音频文件失败", zap.Int("statusCode", resp.StatusCode))
		return nil, fmt.Errorf("下载音频文件失败，状态码: %d", resp.StatusCode)
	}

	// 读取音频数据
	audioData, err := io.ReadAll(resp.Body)
	if err != nil {
		global.GVA_LOG.Error("读取音频数据失败", zap.Error(err))
		return nil, fmt.Errorf("读取音频数据失败: %v", err)
	}

	global.GVA_LOG.Info("音频文件下载成功", zap.Int("size", len(audioData)))

	// 调用识别
	return p.RecognizeAudio(audioData, format, sampleRate)
}

// extractTextFromFlashResult 从flash_result中提取并拼接文本
func (p *AliyunSpeechRecognitionProcessor) extractTextFromFlashResult(flashResult *AliyunFlashResult) string {
	if flashResult == nil || len(flashResult.Sentences) == 0 {
		global.GVA_LOG.Warn("flash_result为空或没有句子")
		return ""
	}

	var textParts []string
	for _, sentence := range flashResult.Sentences {
		if sentence.Text != "" {
			textParts = append(textParts, sentence.Text)
		}
	}

	result := ""
	for _, text := range textParts {
		result += text
	}

	global.GVA_LOG.Info("提取文本成功",
		zap.Int("sentenceCount", len(flashResult.Sentences)),
		zap.String("extractedText", result))

	return result
}
