package ai

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"go.uber.org/zap"
)

// MinimaxProcessor MiniMax API 处理器
type MinimaxProcessor struct {
	apiKey  string
	groupId string
	baseURL string
}

// NewMinimaxProcessor 创建 MiniMax 处理器
func NewMinimaxProcessor() *MinimaxProcessor {
	return &MinimaxProcessor{
		apiKey:  global.GVA_CONFIG.MiniMax.ApiKey,
		groupId: global.GVA_CONFIG.MiniMax.GroupId,
		baseURL: "https://api.minimax.chat",
	}
}

// MinimaxFileUploadResponse 文件上传响应
type MinimaxFileUploadResponse struct {
	File struct {
		FileId    int64  `json:"file_id"`
		Bytes     int64  `json:"bytes"`
		CreatedAt int64  `json:"created_at"`
		Filename  string `json:"filename"`
		Purpose   string `json:"purpose"`
	} `json:"file"`
	BaseResp struct {
		StatusCode int    `json:"status_code"`
		StatusMsg  string `json:"status_msg"`
	} `json:"base_resp"`
	InputSensitive     bool `json:"input_sensitive"`
	InputSensitiveType int  `json:"input_sensitive_type"`
}

// MinimaxVoiceCloneResponse 音频复刻响应
type MinimaxVoiceCloneResponse struct {
	BaseResp struct {
		StatusCode int    `json:"status_code"`
		StatusMsg  string `json:"status_msg"`
	} `json:"base_resp"`
	InputSensitive     bool `json:"input_sensitive"`
	InputSensitiveType int  `json:"input_sensitive_type"`
}

// MinimaxT2AResponse T2A 响应
type MinimaxT2AResponse struct {
	Data struct {
		Audio  string `json:"audio"`  // hex编码的音频
		Status int    `json:"status"` // 状态码
	} `json:"data"`
	ExtraInfo struct {
		AudioLength             int     `json:"audio_length"`
		AudioSampleRate         int     `json:"audio_sample_rate"`
		AudioSize               int     `json:"audio_size"`
		AudioBitrate            int     `json:"audio_bitrate"`
		WordCount               int     `json:"word_count"`
		InvisibleCharacterRatio float64 `json:"invisible_character_ratio"`
		AudioFormat             string  `json:"audio_format"`
		UsageCharacters         int     `json:"usage_characters"`
	} `json:"extra_info"`
	TraceId  string `json:"trace_id"`
	BaseResp struct {
		StatusCode int    `json:"status_code"`
		StatusMsg  string `json:"status_msg"`
	} `json:"base_resp"`
}

// MinimaxSystemVoice 系统音色信息
type MinimaxSystemVoice struct {
	VoiceId     string   `json:"voice_id"`
	VoiceName   string   `json:"voice_name"`
	Description []string `json:"description"`
}

// MinimaxVoiceCloning 音色克隆信息
type MinimaxVoiceCloning struct {
	VoiceId     string   `json:"voice_id"`
	Description []string `json:"description"`
	CreatedTime string   `json:"created_time"`
}

// MinimaxVoiceGeneration 音色生成信息
type MinimaxVoiceGeneration struct {
	VoiceId     string   `json:"voice_id"`
	Description []string `json:"description"`
	CreatedTime string   `json:"created_time"`
}

// MinimaxMusicGeneration 音乐生成信息
type MinimaxMusicGeneration struct {
	VoiceId        string `json:"voice_id"`
	InstrumentalId string `json:"instrumental_id"`
	CreatedTime    string `json:"created_time"`
}

// MinimaxVoiceListResponse 音色列表响应
type MinimaxVoiceListResponse struct {
	SystemVoice     []MinimaxSystemVoice     `json:"system_voice"`
	VoiceCloning    []MinimaxVoiceCloning    `json:"voice_cloning"`
	VoiceGeneration []MinimaxVoiceGeneration `json:"voice_generation"`
	MusicGeneration []MinimaxMusicGeneration `json:"music_generation"`
}

// MinimaxVoiceInfo 统一音色信息（用于前端展示）
type MinimaxVoiceInfo struct {
	VoiceId     string `json:"voice_id"`
	VoiceName   string `json:"voice_name"`
	Type        string `json:"type"`        // system/cloning/generation
	Description string `json:"description"` // 描述字符串
	CreatedTime string `json:"created_time,omitempty"`
}

// UploadAudioFile 上传音频文件
func (p *MinimaxProcessor) UploadAudioFile(audioData []byte, fileName string) (*MinimaxFileUploadResponse, error) {
	url := fmt.Sprintf("%s/v1/files/upload?GroupId=%s", p.baseURL, p.groupId)

	// 创建 multipart form
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加 purpose 字段
	err := writer.WriteField("purpose", "voice_clone")
	if err != nil {
		return nil, fmt.Errorf("写入 purpose 字段失败: %v", err)
	}

	// 添加文件字段
	part, err := writer.CreateFormFile("file", fileName)
	if err != nil {
		return nil, fmt.Errorf("创建文件字段失败: %v", err)
	}

	_, err = part.Write(audioData)
	if err != nil {
		return nil, fmt.Errorf("写入文件数据失败: %v", err)
	}

	err = writer.Close()
	if err != nil {
		return nil, fmt.Errorf("关闭 writer 失败: %v", err)
	}

	// 创建请求
	req, err := http.NewRequest("POST", url, &buf)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+p.apiKey)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	global.GVA_LOG.Info("MiniMax 文件上传响应", zap.String("response", string(body)))

	var response MinimaxFileUploadResponse
	err = json.Unmarshal(body, &response)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if response.BaseResp.StatusCode != 0 {
		return nil, fmt.Errorf("上传失败: %s", response.BaseResp.StatusMsg)
	}

	return &response, nil
}

// CloneVoice 克隆音色
func (p *MinimaxProcessor) CloneVoice(fileId int64, voiceId string) (*MinimaxVoiceCloneResponse, error) {
	url := fmt.Sprintf("%s/v1/voice_clone?GroupId=%s", p.baseURL, p.groupId)

	requestData := map[string]interface{}{
		"file_id":  fileId,
		"voice_id": voiceId,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+p.apiKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	global.GVA_LOG.Info("MiniMax 音色克隆响应", zap.String("response", string(body)))

	var response MinimaxVoiceCloneResponse
	err = json.Unmarshal(body, &response)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if response.BaseResp.StatusCode != 0 {
		return nil, fmt.Errorf("音色克隆失败: %s", response.BaseResp.StatusMsg)
	}

	return &response, nil
}

// TextToAudio 文本转语音
func (p *MinimaxProcessor) TextToAudio(
	text, voiceId, model string, speed, volume, pitch float64, audioSampleRate, bitRate int, format string,
) (*MinimaxT2AResponse, error) {
	url := fmt.Sprintf("%s/v1/t2a_v2?GroupId=%s", p.baseURL, p.groupId)

	requestData := map[string]interface{}{
		"model": model,
		"text":  text,
		"voice_setting": map[string]interface{}{
			"voice_id": voiceId,
			"speed":    speed,
			"vol":      volume,
			"pitch":    int(pitch), // pitch 需要是整数
		},
		"audio_setting": map[string]interface{}{
			"audio_sample_rate": audioSampleRate,
			"bitrate":           bitRate,
			"format":            format,
			"channel":           1, // 单声道
		},
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+p.apiKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	global.GVA_LOG.Info("MiniMax T2A 响应", zap.String("response", string(body)))

	var response MinimaxT2AResponse
	err = json.Unmarshal(body, &response)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if response.BaseResp.StatusCode != 0 {
		return nil, fmt.Errorf("文本转语音失败: %s", response.BaseResp.StatusMsg)
	}

	return &response, nil
}

// GetOfficialVoices 获取官方音色列表
func (p *MinimaxProcessor) GetOfficialVoices() (*MinimaxVoiceListResponse, error) {
	url := "https://api.minimaxi.com/v1/get_voice"

	// 创建请求体
	requestData := map[string]interface{}{
		"voice_type": "system", // 获取官方的系统音色
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+p.apiKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	global.GVA_LOG.Info("MiniMax 音色列表响应", zap.String("response", string(body)))

	var response MinimaxVoiceListResponse
	err = json.Unmarshal(body, &response)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &response, nil
}

// ConvertToUnifiedVoiceList 将不同类型的音色转换为统一格式
func (p *MinimaxProcessor) ConvertToUnifiedVoiceList(response *MinimaxVoiceListResponse) []MinimaxVoiceInfo {
	var voices []MinimaxVoiceInfo

	// 处理系统音色
	for _, voice := range response.SystemVoice {
		voices = append(voices, MinimaxVoiceInfo{
			VoiceId:     voice.VoiceId,
			VoiceName:   voice.VoiceName,
			Type:        "系统音色",
			Description: strings.Join(voice.Description, ", "),
		})
	}

	// 处理音色克隆
	for _, voice := range response.VoiceCloning {
		voiceName := voice.VoiceId
		if len(voice.Description) > 0 && voice.Description[0] != "" {
			voiceName = voice.Description[0] // 使用第一个描述作为名称
		}
		voices = append(voices, MinimaxVoiceInfo{
			VoiceId:     voice.VoiceId,
			VoiceName:   voiceName,
			Type:        "音色克隆",
			Description: strings.Join(voice.Description, ", "),
			CreatedTime: voice.CreatedTime,
		})
	}

	// 处理音色生成
	for _, voice := range response.VoiceGeneration {
		voiceName := voice.VoiceId
		if len(voice.Description) > 0 && voice.Description[0] != "" {
			voiceName = voice.Description[0] // 使用第一个描述作为名称
		}
		voices = append(voices, MinimaxVoiceInfo{
			VoiceId:     voice.VoiceId,
			VoiceName:   voiceName,
			Type:        "音色生成",
			Description: strings.Join(voice.Description, ", "),
			CreatedTime: voice.CreatedTime,
		})
	}

	// 处理音乐生成
	for _, voice := range response.MusicGeneration {
		voiceName := voice.VoiceId
		voices = append(voices, MinimaxVoiceInfo{
			VoiceId:     voice.VoiceId,
			VoiceName:   voiceName,
			Type:        "音乐生成",
			Description: "音乐生成产生的人声或者伴奏音色",
			CreatedTime: voice.CreatedTime,
		})
	}

	return voices
}
