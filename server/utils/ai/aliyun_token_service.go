package ai

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	"github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"go.uber.org/zap"
)

// TokenResult 阿里云token API响应结构
type TokenResult struct {
	ErrMsg string `json:"errMsg"`
	Token  struct {
		UserId     string `json:"userId"`
		Id         string `json:"id"`
		ExpireTime int64  `json:"expireTime"`
	} `json:"token"`
}

// AliyunTokenService 阿里云token服务
type AliyunTokenService struct {
	accessKeyId     string
	accessKeySecret string
	region          string
	tokenCache      *TokenCache
	mutex           sync.RWMutex
}

// TokenCache token缓存结构
type TokenCache struct {
	Token      string
	ExpireTime int64
	CreatedAt  time.Time
}

// NewAliyunTokenService 创建阿里云token服务实例
func NewAliyunTokenService(accessKeyId, accessKeySecret string) *AliyunTokenService {
	return &AliyunTokenService{
		accessKeyId:     accessKeyId,
		accessKeySecret: accessKeySecret,
		region:          "cn-shanghai", // 阿里云语音识别服务的区域
	}
}

// GetToken 获取token，优先从缓存获取，缓存失效时重新请求
func (s *AliyunTokenService) GetToken() (string, error) {
	s.mutex.RLock()
	// 检查缓存是否有效
	if s.tokenCache != nil && s.isTokenValid() {
		token := s.tokenCache.Token
		s.mutex.RUnlock()
		global.GVA_LOG.Debug("使用缓存的token", zap.String("token", token[:10]+"..."))
		return token, nil
	}
	s.mutex.RUnlock()

	// 缓存无效，重新获取token
	return s.refreshToken()
}

// isTokenValid 检查token是否有效（未过期且还有至少5分钟有效期）
func (s *AliyunTokenService) isTokenValid() bool {
	if s.tokenCache == nil {
		return false
	}

	now := time.Now().Unix()
	// 提前5分钟刷新token，避免在使用过程中过期
	bufferTime := int64(5 * 60)

	return s.tokenCache.ExpireTime > (now + bufferTime)
}

// refreshToken 刷新token
func (s *AliyunTokenService) refreshToken() (string, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 双重检查，避免并发情况下重复请求
	if s.tokenCache != nil && s.isTokenValid() {
		global.GVA_LOG.Debug("并发检查：使用缓存的token")
		return s.tokenCache.Token, nil
	}

	global.GVA_LOG.Info("开始获取新的阿里云语音识别token")

	// 创建配置
	config := &openapi.Config{
		AccessKeyId:     tea.String(s.accessKeyId),
		AccessKeySecret: tea.String(s.accessKeySecret),
		RegionId:        tea.String(s.region),
		Endpoint:        tea.String("nls-meta.cn-shanghai.aliyuncs.com"),
	}

	// 创建客户端
	client, err := openapi.NewClient(config)
	if err != nil {
		global.GVA_LOG.Error("创建阿里云SDK客户端失败", zap.Error(err))
		return "", fmt.Errorf("创建阿里云SDK客户端失败: %v", err)
	}

	// 创建OpenAPI请求
	params := &openapi.Params{
		Action:      tea.String("CreateToken"),
		Version:     tea.String("2019-02-28"),
		Protocol:    tea.String("HTTPS"),
		Method:      tea.String("POST"),
		AuthType:    tea.String("AK"),
		Style:       tea.String("RPC"),
		Pathname:    tea.String("/"),
		ReqBodyType: tea.String("formData"),
		BodyType:    tea.String("json"),
	}

	// 创建runtime对象
	runtime := &service.RuntimeOptions{}

	// 发送请求
	response, err := client.CallApi(params, &openapi.OpenApiRequest{}, runtime)
	if err != nil {
		global.GVA_LOG.Error("调用阿里云token API失败", zap.Error(err))
		return "", fmt.Errorf("调用阿里云token API失败: %v", err)
	}

	global.GVA_LOG.Debug("阿里云token API响应", zap.Any("response", response))

	// 获取响应body
	responseBody, ok := response["body"].(map[string]interface{})
	if !ok {
		global.GVA_LOG.Error("响应格式错误")
		return "", fmt.Errorf("响应格式错误")
	}

	// 转换为JSON进行解析
	responseBytes, err := json.Marshal(responseBody)
	if err != nil {
		global.GVA_LOG.Error("响应序列化失败", zap.Error(err))
		return "", fmt.Errorf("响应序列化失败: %v", err)
	}

	// 解析响应
	var tokenResult TokenResult
	err = json.Unmarshal(responseBytes, &tokenResult)
	if err != nil {
		global.GVA_LOG.Error("解析token响应失败", zap.Error(err), zap.String("content", string(responseBytes)))
		return "", fmt.Errorf("解析token响应失败: %v", err)
	}

	// 检查业务错误
	if tokenResult.ErrMsg != "" {
		global.GVA_LOG.Error("阿里云token API返回业务错误", zap.String("errMsg", tokenResult.ErrMsg))
		return "", fmt.Errorf("阿里云token API返回业务错误: %s", tokenResult.ErrMsg)
	}

	// 检查token是否有效
	if tokenResult.Token.Id == "" {
		global.GVA_LOG.Error("获取到空的token")
		return "", fmt.Errorf("获取到空的token")
	}

	// 更新缓存
	s.tokenCache = &TokenCache{
		Token:      tokenResult.Token.Id,
		ExpireTime: tokenResult.Token.ExpireTime,
		CreatedAt:  time.Now(),
	}

	global.GVA_LOG.Info("成功获取新的阿里云语音识别token",
		zap.String("token", tokenResult.Token.Id[:10]+"..."),
		zap.Int64("expireTime", tokenResult.Token.ExpireTime),
		zap.String("userId", tokenResult.Token.UserId))

	return tokenResult.Token.Id, nil
}

// ClearCache 清除token缓存
func (s *AliyunTokenService) ClearCache() {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.tokenCache = nil
	global.GVA_LOG.Info("已清除阿里云语音识别token缓存")
}
