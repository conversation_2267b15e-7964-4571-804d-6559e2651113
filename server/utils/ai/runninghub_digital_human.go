package ai

import (
	"fmt"
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"go.uber.org/zap"
)

// 数字人生成的提示词常量
const (
	// 强化的负面提示词，重点避免多人脸伪影
	enhancedNegativePromptForDigitalHuman = "bright tones, overexposed, static, blurred details, subtitles, style, works, paintings, images, static, overall gray, worst quality, low quality, JPEG compression residue, ugly, incomplete, extra fingers, poorly drawn hands, poorly drawn faces, deformed, disfigured, misshapen limbs, fused fingers, still picture, messy background, three legs, many people in the background, walking backwards, multiple faces, multiple people, crowd, group of people, twins, duplicate person, clone, mirror reflection, double vision, split personality, multiple heads, two faces, several faces, background people, silhouettes, shadow people, ghost people, floating heads, face multiplication, crowd scene, party scene, meeting scene, multiple characters, other person, another person, second person, third person, bystanders, observers, audience, spectators"

	// 强化的正面提示词，强调单人特征
	singlePersonEnhancement = ", single person, one person only, individual portrait, solo character, alone, isolated subject, clean background, simple background, minimal background, focused on one face, single face, one clear face, main character, high quality, natural lighting, professional portrait, clear facial features, detailed facial expression"
)

// createDigitalHumanTask 创建数字人视频任务
func (p *RunningHubProcessor) createDigitalHumanTask(req DigitalHumanTaskRequest) (string, error) {
	// 验证必需参数
	if req.Script == "" {
		return "", fmt.Errorf("台词内容不能为空")
	}
	if req.Action == "" {
		return "", fmt.Errorf("人物动作不能为空")
	}

	// 根据工作流类型验证不同的媒体文件
	var mediaUrl string
	if req.WorkflowType == 3 {
		// HeyGem工作流使用视频
		if req.CharacterVideoUrl == "" {
			return "", fmt.Errorf("人物视频URL不能为空")
		}
		mediaUrl = req.CharacterVideoUrl
	} else {
		// 其他工作流使用图片
		if req.CharacterImageUrl == "" {
			return "", fmt.Errorf("人物图片URL不能为空")
		}
		mediaUrl = req.CharacterImageUrl
	}

	if req.ReferenceAudioUrl == "" {
		return "", fmt.Errorf("参考音频URL不能为空")
	}

	// 1. 上传人物媒体文件（图片或视频）到RunningHub
	var mediaTypeName string
	if req.WorkflowType == 3 {
		mediaTypeName = "人物视频"
	} else {
		mediaTypeName = "人物图片"
	}

	mediaUploadResp, err := p.uploadFileToRunningHubByUrl(mediaUrl)
	if err != nil {
		global.GVA_LOG.Error("上传"+mediaTypeName+"到RunningHub失败", zap.Error(err))
		return "", fmt.Errorf("上传%s失败: %v", mediaTypeName, err)
	}
	if mediaUploadResp.Code != 0 {
		return "", fmt.Errorf("上传%s失败: %v", mediaTypeName, mediaUploadResp.Msg)
	}

	// 2. 上传参考音频到RunningHub
	audioUploadResp, err := p.uploadFileToRunningHubByUrl(req.ReferenceAudioUrl)
	if err != nil {
		global.GVA_LOG.Error("上传参考音频到RunningHub失败", zap.Error(err))
		return "", fmt.Errorf("上传参考音频失败: %v", err)
	}
	if audioUploadResp.Code != 0 {
		return "", fmt.Errorf("上传参考音频失败: %v", audioUploadResp.Msg)
	}

	// 3. 使用上传后的文件名构建请求
	runningHubReq := p.buildDigitalHumanRequest(req, mediaUploadResp.Data.FileName, audioUploadResp.Data.FileName)
	runningHubResp, err := p.callRunningHubVideoAPI(runningHubReq)
	if err != nil {
		global.GVA_LOG.Error("调用RunningHub数字人API失败", zap.Error(err))
		return "", err
	}
	if runningHubResp.Code != 0 {
		return "", fmt.Errorf("调用RunningHub数字人API失败，Code: %v, Message: %v", runningHubResp.Code, runningHubResp.Message)
	}

	return runningHubResp.Data.TaskId, nil
}

// buildDigitalHumanRequest 构建数字人视频请求
func (p *RunningHubProcessor) buildDigitalHumanRequest(
	req DigitalHumanTaskRequest, mediaFileName, audioFileName string,
) RunningHubDigitalHumanRequest {
	// 从配置中获取API Key和工作流ID
	apiKey := global.GVA_CONFIG.RunningHub.ApiKey
	// 使用不同的数字人工作流：
	// 工作流1：不使用 plus 实例，ID=1955827510019854338
	// 工作流2：使用 plus 实例，沿用原有 ID=1952992438153580545
	// 工作流3：HeyGem数字人+IndexTTS快速语音迁移V3（修复报错，新模型），ID=1956196990470246401
	workflowId := "1952992438153580545"
	instanceType := "plus"
	if req.WorkflowType == 0 || req.WorkflowType == 1 {
		workflowId = "1955827510019854338"
		instanceType = ""
	} else if req.WorkflowType == 3 {
		workflowId = "1956196990470246401"
		instanceType = ""
	}

	// 设置默认值
	if req.Width == 0 {
		req.Width = 832
	}
	if req.Height == 0 {
		req.Height = 832
	}
	if req.Fps == 0 {
		req.Fps = 16
	}

	// 构建节点信息列表，根据工作流类型使用不同的节点配置
	var nodeInfoList []RunningHubNodeInfo

	if req.WorkflowType == 0 || req.WorkflowType == 1 {
		// 工作流1: HeyGem+index-tts+图片+wan视频模型 数字人工作流
		nodeInfoList = []RunningHubNodeInfo{
			// 节点16: WanVideoTextEncode - 强化negative prompt以避免多人脸伪影
			{
				NodeId:     "16",
				FieldName:  "negative_prompt",
				FieldValue: enhancedNegativePromptForDigitalHuman,
			},
			// 节点58: Load Image (人物图片)
			{
				NodeId:     "58",
				FieldName:  "image",
				FieldValue: mediaFileName,
			},
			// 节点70: INTConstant (输出分辨率设置，取宽高中较小值)
			{
				NodeId:    "70",
				FieldName: "value",
				FieldValue: func() string {
					if req.Width < req.Height {
						return strconv.Itoa(req.Width)
					}
					return strconv.Itoa(req.Height)
				}(),
			},
			// 节点72: TextInput (数字人说话内容)
			{
				NodeId:     "72",
				FieldName:  "text",
				FieldValue: req.Script,
			},
			// 节点77: Load Audio (参考音频)
			{
				NodeId:     "77",
				FieldName:  "audio",
				FieldValue: audioFileName,
			},
			// 节点81: PrimitiveFloat (帧率)
			{
				NodeId:     "81",
				FieldName:  "value",
				FieldValue: strconv.Itoa(req.Fps),
			},
			// 节点83: IndexTTSRun (TTS配置，使用修复后的参数)
			{
				NodeId:     "83",
				FieldName:  "num_beams",
				FieldValue: "1", // 修复后的参数，避免维度不一致
			},
			{
				NodeId:     "83",
				FieldName:  "sentences_bucket_max_size",
				FieldValue: "1", // 修复后的参数，避免维度不一致
			},
			// 节点89: TextInput (人物动作描述) - 强化单人特征
			{
				NodeId:     "89",
				FieldName:  "text",
				FieldValue: req.Action + singlePersonEnhancement,
			},
		}
	} else if req.WorkflowType == 3 {
		// 工作流3: HeyGem数字人+IndexTTS快速语音迁移V3（修复报错，新模型）
		if req.HeygemMode == 1 {
			// 模式1: 音频+视频（直接使用音频文件）
			nodeInfoList = []RunningHubNodeInfo{
				// 节点1: Load Video (人物视频)
				{
					NodeId:     "1",
					FieldName:  "file",
					FieldValue: mediaFileName,
				},
				// 节点4: Load Audio (直接音频)
				{
					NodeId:     "4",
					FieldName:  "audio",
					FieldValue: audioFileName,
				},
				// 节点14: ImpactSwitch (选择直接音频)
				{
					NodeId:     "14",
					FieldName:  "select",
					FieldValue: "1", // 选择input1（直接音频）
				},
			}
		} else if req.HeygemMode == 2 {
			// 模式2: 参考音频+台词+视频（使用TTS进行声音克隆）
			nodeInfoList = []RunningHubNodeInfo{
				// 节点1: Load Video (人物视频)
				{
					NodeId:     "1",
					FieldName:  "file",
					FieldValue: mediaFileName,
				},
				// 节点7: Load Audio (参考音频，用于声音克隆)
				{
					NodeId:     "7",
					FieldName:  "audio",
					FieldValue: audioFileName,
				},
				// 节点12: CR Prompt Text (台词内容)
				{
					NodeId:     "12",
					FieldName:  "prompt",
					FieldValue: req.Script,
				},
				// 节点14: ImpactSwitch (选择TTS生成的音频)
				{
					NodeId:     "14",
					FieldName:  "select",
					FieldValue: "2", // 选择input2（TTS生成的音频）
				},
				// 节点17: IndexTTSNode (TTS配置，使用修复后的参数避免维度不一致)
				{
					NodeId:     "17",
					FieldName:  "num_beams",
					FieldValue: "1", // 修复后的参数，避免维度不一致错误
				},
			}
		} else {
			// 默认使用模式1
			nodeInfoList = []RunningHubNodeInfo{
				{
					NodeId:     "1",
					FieldName:  "file",
					FieldValue: mediaFileName,
				},
				{
					NodeId:     "4",
					FieldName:  "audio",
					FieldValue: audioFileName,
				},
				{
					NodeId:     "14",
					FieldName:  "select",
					FieldValue: "1",
				},
			}
		}
	} else {
		// 工作流2: 原有的数字人工作流（plus模式）
		nodeInfoList = []RunningHubNodeInfo{
			// 节点16: WanVideoTextEncode - 强化negative prompt以避免多人脸伪影
			{
				NodeId:     "16",
				FieldName:  "negative_prompt",
				FieldValue: enhancedNegativePromptForDigitalHuman,
			},
			// 节点81: Float (FPS)
			{
				NodeId:     "81",
				FieldName:  "value",
				FieldValue: strconv.Itoa(req.Fps),
			},
			// 节点91: Resize Image
			{
				NodeId:     "91",
				FieldName:  "width",
				FieldValue: strconv.Itoa(req.Width),
			},
			{
				NodeId:     "91",
				FieldName:  "height",
				FieldValue: strconv.Itoa(req.Height),
			},
			// 节点92: Load Image (人物图片)
			{
				NodeId:     "92",
				FieldName:  "image",
				FieldValue: mediaFileName,
			},
			// 节点93: Load Audio (参考音频)
			{
				NodeId:     "93",
				FieldName:  "audio",
				FieldValue: audioFileName,
			},
			// 节点98: 台词内容
			{
				NodeId:     "98",
				FieldName:  "value",
				FieldValue: req.Script,
			},
			// 节点100: 动作提示 - 强化单人特征
			{
				NodeId:     "100",
				FieldName:  "value",
				FieldValue: req.Action + singlePersonEnhancement,
			},
			// 节点102: Video Combine
			{
				NodeId:     "102",
				FieldName:  "frame_rate",
				FieldValue: strconv.Itoa(req.Fps),
			},
		}
	}

	return RunningHubDigitalHumanRequest{
		ApiKey:       apiKey,
		WorkflowId:   workflowId,
		NodeInfoList: nodeInfoList,
		InstanceType: instanceType,
	}
}
