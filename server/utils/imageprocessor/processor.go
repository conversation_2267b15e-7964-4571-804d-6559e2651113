package imageprocessor

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai/request"
)

// ImageProcessor 图像处理器接口
type ImageProcessor interface {
	// ProcessImage 处理图片，添加标题和贴纸
	ProcessImage(config ProcessConfig) (string, error)

	// SupportsTextRendering 是否支持文字渲染
	SupportsTextRendering() bool

	// SupportsStickerOverlay 是否支持贴纸叠加
	SupportsStickerOverlay() bool

	// GetName 获取处理器名称
	GetName() string
}

// ProcessConfig 图像处理配置
type ProcessConfig struct {
	// 输入图片路径
	InputImagePath string

	// 输出图片路径
	OutputImagePath string

	// 标题设置
	TitleSettings []request.TitleSetting

	// 贴纸设置
	StickerSettings []request.StickerSetting

	// 是否需要添加标题
	NeedTitle bool

	// 是否需要添加贴纸
	NeedSticker bool
}

// ProcessorFactory 图像处理器工厂
type ProcessorFactory struct {
	processors []ImageProcessor
}

// NewProcessorFactory 创建处理器工厂
func NewProcessorFactory() *ProcessorFactory {
	factory := &ProcessorFactory{}

	// 注册处理器，优先级从高到低
	// 改为优先使用 Go 图像库（贴纸 + 文字全部由 Go 处理）
	factory.RegisterProcessor(NewGoImagingProcessor())
	// FFmpeg 仅作为其他任务（如伪原创等）的备用处理器
	factory.RegisterProcessor(NewFFmpegProcessor())

	return factory
}

// RegisterProcessor 注册处理器
func (f *ProcessorFactory) RegisterProcessor(processor ImageProcessor) {
	f.processors = append(f.processors, processor)
}

// GetBestProcessor 根据需求获取最佳处理器
func (f *ProcessorFactory) GetBestProcessor(needTitle, needSticker bool) ImageProcessor {
	// 首先尝试找到完全匹配的处理器
	for _, processor := range f.processors {
		if (!needTitle || processor.SupportsTextRendering()) &&
			(!needSticker || processor.SupportsStickerOverlay()) {
			return processor
		}
	}

	// 如果没有完全匹配的，优先满足文字需求
	if needTitle {
		for _, processor := range f.processors {
			if processor.SupportsTextRendering() {
				return processor
			}
		}
	}

	// 如果文字需求无法满足，尝试满足贴纸需求
	if needSticker {
		for _, processor := range f.processors {
			if processor.SupportsStickerOverlay() {
				return processor
			}
		}
	}

	// 如果都无法满足，返回第一个可用的处理器
	if len(f.processors) > 0 {
		return f.processors[0]
	}

	return nil
}

// GetAllProcessors 获取所有处理器
func (f *ProcessorFactory) GetAllProcessors() []ImageProcessor {
	return f.processors
}
