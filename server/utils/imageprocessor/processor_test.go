package imageprocessor

import (
	"strings"
	"testing"

	"github.com/flipped-aurora/gin-vue-admin/server/model/ai/request"
)

// TestProcessorFactory 测试处理器工厂
func TestProcessorFactory(t *testing.T) {
	factory := NewProcessorFactory()

	processors := factory.GetAllProcessors()
	if len(processors) == 0 {
		t.<PERSON>rror("处理器工厂应该至少有一个处理器")
	}

	// 测试获取最佳处理器
	processor := factory.GetBestProcessor(true, true)
	if processor == nil {
		t.<PERSON>rror("应该能够获取到处理器")
	}

	t.Logf("获取到处理器: %s", processor.GetName())
	t.Logf("支持文字渲染: %v", processor.SupportsTextRendering())
	t.Logf("支持贴纸叠加: %v", processor.SupportsStickerOverlay())
}

// TestFFmpegProcessor 测试FFmpeg处理器
func TestFFmpegProcessor(t *testing.T) {
	processor := NewFFmpegProcessor()

	if processor.GetName() != "FFmpeg" {
		t.<PERSON><PERSON><PERSON>("期望处理器名称为FFmpeg，实际为: %s", processor.GetName())
	}

	// 测试支持情况
	t.Logf("FFmpeg支持文字渲染: %v", processor.SupportsTextRendering())
	t.Logf("FFmpeg支持贴纸叠加: %v", processor.SupportsStickerOverlay())
}

// TestGoImagingProcessor 测试Go图像库处理器
func TestGoImagingProcessor(t *testing.T) {
	processor := NewGoImagingProcessor()

	if processor.GetName() != "GoImaging" {
		t.Errorf("期望处理器名称为GoImaging，实际为: %s", processor.GetName())
	}

	// 测试支持情况
	t.Logf("GoImaging支持文字渲染: %v", processor.SupportsTextRendering())
	t.Logf("GoImaging支持贴纸叠加: %v", processor.SupportsStickerOverlay())
}

// TestProcessConfig 测试处理配置
func TestProcessConfig(t *testing.T) {
	config := ProcessConfig{
		InputImagePath:  "/tmp/test_input.jpg",
		OutputImagePath: "/tmp/test_output.jpg",
		TitleSettings: []request.TitleSetting{
			{
				Content:   "测试标题",
				FontSize:  24,
				FontColor: "white",
				Alignment: "center",
			},
		},
		StickerSettings: []request.StickerSetting{},
		NeedTitle:       true,
		NeedSticker:     false,
	}

	if config.InputImagePath != "/tmp/test_input.jpg" {
		t.Error("配置设置失败")
	}

	if len(config.TitleSettings) != 1 {
		t.Error("标题设置数量不正确")
	}

	if config.TitleSettings[0].Content != "测试标题" {
		t.Error("标题内容设置不正确")
	}
}

// TestFontPathCleaning 测试字体路径清理逻辑
func TestFontPathCleaning(t *testing.T) {
	// 创建FFmpeg处理器进行测试（用于确保处理器能正常创建）
	_ = NewFFmpegProcessor()

	// 测试用例：模拟字体路径清理逻辑
	testCases := []struct {
		input    string
		expected string
	}{
		{"/fonts/AlibabaPuHuiTi-3-85-Bold.ttf", "AlibabaPuHuiTi-3-85-Bold.ttf"},
		{"fonts/Arial.ttf", "Arial.ttf"},
		{"/Arial.ttf", "Arial.ttf"},
		{"Arial.ttf", "Arial.ttf"},
		{"", ""},
	}

	ossBaseURL := "https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/font/"

	for _, tc := range testCases {
		t.Run(tc.input, func(t *testing.T) {
			// 模拟字体路径清理逻辑
			var fontURL string
			if tc.input != "" && !strings.HasPrefix(tc.input, "http") {
				cleanFontName := strings.TrimPrefix(tc.input, "/")
				cleanFontName = strings.TrimPrefix(cleanFontName, "fonts/")
				fontURL = ossBaseURL + cleanFontName
			}

			expectedURL := ""
			if tc.expected != "" {
				expectedURL = ossBaseURL + tc.expected
			}

			if fontURL != expectedURL {
				t.Errorf("字体路径清理失败: 输入='%s', 期望='%s', 实际='%s'",
					tc.input, expectedURL, fontURL)
			} else {
				t.Logf("字体路径清理成功: '%s' -> '%s'", tc.input, fontURL)

				// 额外验证：确保文件名也是正确的
				if tc.expected != "" {
					cleanFontName := strings.TrimPrefix(tc.input, "/")
					cleanFontName = strings.TrimPrefix(cleanFontName, "fonts/")
					if cleanFontName != tc.expected {
						t.Errorf("文件名清理失败: 输入='%s', 期望='%s', 实际='%s'",
							tc.input, tc.expected, cleanFontName)
					}
				}
			}
		})
	}
}
