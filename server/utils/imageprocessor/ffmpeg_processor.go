package imageprocessor

import (
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai/request"
	"go.uber.org/zap"
)

// FFmpegProcessor FFmpeg图像处理器
type FFmpegProcessor struct {
	supportsDrawtext bool
	ffmpegMajor      int
	ffmpegMinor      int
	supportsRefScale bool // 是否支持scale滤镜的rw/rh（两输入scale）
}

// NewFFmpegProcessor 创建FFmpeg处理器
func NewFFmpegProcessor() *FFmpegProcessor {
	processor := &FFmpegProcessor{}
	processor.supportsDrawtext = processor.checkDrawtextSupport()
	processor.detectFFmpegCapabilities()
	return processor
}

// ProcessImage 处理图片
func (p *FFmpegProcessor) ProcessImage(config ProcessConfig) (string, error) {
	// 如果不需要添加任何效果，直接复制文件
	if !config.NeedTitle && !config.NeedSticker {
		return p.copyFile(config.InputImagePath, config.OutputImagePath)
	}

	// 构建FFmpeg命令参数
	var args []string
	var filterComplexParts []string
	var inputCount int

	// 主图片输入
	args = append(args, "-i", config.InputImagePath)
	inputCount++

	// 探测主图片尺寸，用于跨版本安全地计算贴纸缩放像素
	mainW, mainH := p.probeImageSize(config.InputImagePath)
	if mainW <= 0 || mainH <= 0 {
		// 回退一个保守尺寸，避免后续计算为0
		mainW, mainH = 1080, 1080
		global.GVA_LOG.Warn("探测主图尺寸失败，使用回退尺寸", zap.Int("w", mainW), zap.Int("h", mainH))
	}

	// 下载并添加贴纸文件作为输入
	var stickerPaths []string
	if config.NeedSticker && len(config.StickerSettings) > 0 {
		for _, sticker := range config.StickerSettings {
			if sticker.MediaUrl == "" && sticker.MediaId == "" {
				continue
			}

			// 获取贴纸URL
			stickerUrl := sticker.MediaUrl
			if stickerUrl == "" {
				global.GVA_LOG.Warn("贴纸没有有效的URL，跳过", zap.String("mediaId", sticker.MediaId))
				continue
			}

			stickerPath, err := p.downloadImage(stickerUrl)
			if err != nil {
				global.GVA_LOG.Error("下载贴纸失败", zap.Error(err), zap.String("stickerUrl", stickerUrl))
				continue
			}

			stickerPaths = append(stickerPaths, stickerPath)
			args = append(args, "-i", stickerPath)
			inputCount++
		}
	}

	// 构建滤镜链
	currentInput := "[0:v]"

	// 处理贴纸叠加
	if config.NeedSticker && len(stickerPaths) > 0 {
		currentInput = p.buildStickerFilters(&filterComplexParts, currentInput, config.StickerSettings, stickerPaths, mainW, mainH)
	}

	// 添加文字滤镜
	if config.NeedTitle && len(config.TitleSettings) > 0 {
		if p.supportsDrawtext {
			currentInput = p.buildTextFilters(&filterComplexParts, currentInput, config.TitleSettings)
		} else {
			// 如果不支持drawtext，记录警告但继续处理
			global.GVA_LOG.Warn("FFmpeg不支持drawtext滤镜，将跳过文字处理")
		}
	}

	// 构建完整的FFmpeg命令
	if len(filterComplexParts) > 0 {
		// 使用filter_complex
		filterComplex := strings.Join(filterComplexParts, ";")

		// 如果最终输出有标签，需要用-map映射输出
		if strings.Contains(currentInput, "[") && strings.Contains(currentInput, "]") {
			args = append(args, "-filter_complex", filterComplex)
			args = append(args, "-map", currentInput)
		} else {
			args = append(args, "-filter_complex", filterComplex)
		}
	} else {
		// 如果没有滤镜，直接复制
		return p.copyFile(config.InputImagePath, config.OutputImagePath)
	}

	// 输出设置 - 添加帧数限制，防止GIF等动画文件输出多帧
	args = append(args, "-frames:v", "1", "-y", config.OutputImagePath)

	// 确保输出目录存在
	if err := os.MkdirAll(filepath.Dir(config.OutputImagePath), 0755); err != nil {
		return "", fmt.Errorf("创建输出目录失败: %w", err)
	}

	// 执行FFmpeg命令
	cmd := exec.Command("ffmpeg", args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		global.GVA_LOG.Error("FFmpeg处理失败",
			zap.String("cmd", cmd.String()),
			zap.String("output", string(output)),
			zap.Error(err))
		return "", fmt.Errorf("FFmpeg处理失败: %w", err)
	}

	// 检查输出文件是否生成
	if _, err := os.Stat(config.OutputImagePath); os.IsNotExist(err) {
		// 尝试使用显式图片格式重试一次
		global.GVA_LOG.Warn("FFmpeg执行成功但未生成文件，尝试使用image2重试",
			zap.String("cmd", cmd.String()),
			zap.String("output", string(output)))

		// 插入 -f image2 到 -y 之前
		retryArgs := make([]string, 0, len(args)+2)
		for i := 0; i < len(args); i++ {
			if args[i] == "-y" && i == len(args)-2 { // 形如 ... -y outputPath
				retryArgs = append(retryArgs, "-f", "image2")
			}
			retryArgs = append(retryArgs, args[i])
		}

		retryCmd := exec.Command("ffmpeg", retryArgs...)
		retryOut, retryErr := retryCmd.CombinedOutput()
		if retryErr != nil {
			global.GVA_LOG.Error("FFmpeg重试失败",
				zap.String("cmd", retryCmd.String()),
				zap.String("output", string(retryOut)),
				zap.Error(retryErr))
			return "", fmt.Errorf("处理后的图片文件未生成")
		}
		if _, statErr := os.Stat(config.OutputImagePath); os.IsNotExist(statErr) {
			global.GVA_LOG.Error("FFmpeg重试后仍未生成文件",
				zap.String("cmd", retryCmd.String()),
				zap.String("output", string(retryOut)))
			return "", fmt.Errorf("处理后的图片文件未生成")
		}
	}

	// 清理临时贴纸文件
	for _, stickerPath := range stickerPaths {
		stickerDir := filepath.Dir(stickerPath)
		os.RemoveAll(stickerDir)
	}

	global.GVA_LOG.Info("FFmpeg图片处理完成", zap.String("outputPath", config.OutputImagePath))
	return config.OutputImagePath, nil
}

// SupportsTextRendering 是否支持文字渲染
func (p *FFmpegProcessor) SupportsTextRendering() bool {
	return p.supportsDrawtext
}

// SupportsStickerOverlay 是否支持贴纸叠加
func (p *FFmpegProcessor) SupportsStickerOverlay() bool {
	return true // FFmpeg始终支持overlay
}

// GetName 获取处理器名称
func (p *FFmpegProcessor) GetName() string {
	return "FFmpeg"
}

// checkDrawtextSupport 检查FFmpeg是否支持drawtext滤镜
func (p *FFmpegProcessor) checkDrawtextSupport() bool {
	cmd := exec.Command("ffmpeg", "-filters")
	output, err := cmd.Output()
	if err != nil {
		global.GVA_LOG.Error("检查FFmpeg滤镜失败", zap.Error(err))
		return false
	}

	// 检查输出中是否包含drawtext
	return strings.Contains(string(output), "drawtext")
}

// probeImageSize 读取图片宽高（通过ffprobe或magick-identify，不可用则返回0）
func (p *FFmpegProcessor) probeImageSize(path string) (int, int) {
	// 优先尝试 ffprobe
	cmd := exec.Command("ffprobe", "-v", "error", "-select_streams", "v:0", "-show_entries", "stream=width,height", "-of", "csv=p=0:s=x", path)
	out, err := cmd.Output()
	if err == nil {
		var w, h int
		if _, err := fmt.Sscanf(string(out), "%dx%d", &w, &h); err == nil {
			return w, h
		}
	}
	return 0, 0
}

// detectFFmpegCapabilities 侦测ffmpeg版本与能力
func (p *FFmpegProcessor) detectFFmpegCapabilities() {
	cmd := exec.Command("ffmpeg", "-version")
	output, err := cmd.Output()
	if err != nil {
		global.GVA_LOG.Warn("读取ffmpeg版本失败", zap.Error(err))
		return
	}
	verStr := string(output)
	// 解析格式: ffmpeg version 7.1.1 ...
	var major, minor int
	if _, err := fmt.Sscanf(verStr, "ffmpeg version %d.%d", &major, &minor); err == nil {
		p.ffmpegMajor = major
		p.ffmpegMinor = minor
		// 7.1 起支持 scale 两输入引用变量 rw/rh
		if major > 7 || (major == 7 && minor >= 1) {
			p.supportsRefScale = true
		}
	} else {
		global.GVA_LOG.Warn("解析ffmpeg版本失败", zap.Error(err))
	}
}

// buildStickerFilters 构建贴纸滤镜
func (p *FFmpegProcessor) buildStickerFilters(
	filterComplexParts *[]string, currentInput string, stickerSettings []request.StickerSetting, stickerPaths []string, mainW int, mainH int,
) string {
	stickerIndex := 0
	for i, sticker := range stickerSettings {
		if sticker.MediaUrl == "" && sticker.MediaId == "" {
			continue
		}

		if sticker.MediaUrl == "" {
			continue
		}

		if stickerIndex >= len(stickerPaths) {
			break
		}

		// 构建贴纸overlay滤镜
		stickerInputIndex := 1 + stickerIndex // 主图片是0，贴纸从1开始

		// 计算贴纸的位置和大小
		scale := sticker.Scale
		if scale <= 0 {
			scale = 1.0 // 默认不缩放
		}

		// 使用前端计算的尺寸比例，确保预览与实际生成一致
		var useRatioScaling bool
		var widthRatio, heightRatio float64

		if sticker.WidthRatio > 0 && sticker.HeightRatio > 0 {
			// 前端已经计算了比例，直接使用
			useRatioScaling = true
			widthRatio = sticker.WidthRatio
			heightRatio = sticker.HeightRatio

			global.GVA_LOG.Info("使用前端计算的贴纸比例",
				zap.Int("贴纸索引", i),
				zap.Float64("宽度比例", widthRatio),
				zap.Float64("高度比例", heightRatio),
				zap.Float64("用户缩放", scale))
		} else {
			// 兼容旧版本，使用原有逻辑
			useRatioScaling = false
			global.GVA_LOG.Info("使用传统贴纸缩放",
				zap.Int("贴纸索引", i),
				zap.Float64("用户缩放", scale))
		}

		// 计算X和Y坐标，基于中心点定位
		var xPos, yPos string
		if sticker.X >= 0 && sticker.X <= 1 && sticker.Y >= 0 && sticker.Y <= 1 {
			// 使用具体的相对坐标（百分比），基于贴纸中心点定位
			xPos = fmt.Sprintf("main_w*%.3f-overlay_w/2", sticker.X)
			yPos = fmt.Sprintf("main_h*%.3f-overlay_h/2", sticker.Y)
		} else {
			// 使用预设位置，也基于中心点
			switch sticker.Position {
			case "top-left":
				xPos = "overlay_w/2+20"
				yPos = "overlay_h/2+20"
			case "top-right":
				xPos = "main_w-overlay_w/2-20"
				yPos = "overlay_h/2+20"
			case "bottom-left":
				xPos = "overlay_w/2+20"
				yPos = "main_h-overlay_h/2-20"
			case "bottom-right":
				xPos = "main_w-overlay_w/2-20"
				yPos = "main_h-overlay_h/2-20"
			case "center":
				xPos = "main_w/2"
				yPos = "main_h/2"
			default: // 默认居中
				xPos = "main_w/2"
				yPos = "main_h/2"
			}
		}

		// 生成当前步骤的输出标签
		outputLabel := fmt.Sprintf("[overlay%d]", i)

		// 处理旋转和缩放
		needTransform := sticker.Rotation != 0 || scale != 1.0 || (useRatioScaling && (sticker.Opacity > 0 && sticker.Opacity < 1.0))

		if useRatioScaling {
			// 按主图实际尺寸进行等比缩放，精确匹配前端比例
			// 前端给出的是贴纸相对于预览图的宽高比例，这里乘以用户缩放即可
			finalWidthRatio := widthRatio * scale
			finalHeightRatio := heightRatio * scale

			// 1) 选择本地/线上兼容的缩放方案
			scaledLabel := fmt.Sprintf("[st_scaled_%d]", i)
			refOutLabel := fmt.Sprintf("[st_ref_%d]", i)
			if p.supportsRefScale {
				// 本地新版本 (>=7.1) 优先使用两输入 scale 引用 rw/rh
				scaleStep := fmt.Sprintf("[%d:v][0:v]scale=w=rw*%.6f:h=rh*%.6f:force_original_aspect_ratio=decrease:flags=lanczos%s",
					stickerInputIndex, finalWidthRatio, finalHeightRatio, scaledLabel)
				*filterComplexParts = append(*filterComplexParts, scaleStep)
				// 作为ref输出，直接用主图
				refOutLabel = currentInput
			} else {
				// 线上旧版本使用scale2ref，先预处理贴纸确保透明度兼容
				formatLabel := fmt.Sprintf("[st_fmt_%d]", i)
				formatStep := fmt.Sprintf("[%d:v]format=rgba%s", stickerInputIndex, formatLabel)
				*filterComplexParts = append(*filterComplexParts, formatStep)

				// 然后scale2ref缩放（旧版FFmpeg不支持在此处直接引用main_w/main_h，改为使用探测到的像素尺寸）
				targetW := int(float64(mainW) * finalWidthRatio)
				if targetW < 1 {
					targetW = 1
				}
				targetH := int(float64(mainH) * finalHeightRatio)
				if targetH < 1 {
					targetH = 1
				}
				scaleStep := fmt.Sprintf("%s[0:v]scale2ref=w=%d:h=%d:force_original_aspect_ratio=decrease%s%s",
					formatLabel, targetW, targetH, scaledLabel, refOutLabel)
				*filterComplexParts = append(*filterComplexParts, scaleStep)
			}

			currentSticker := scaledLabel

			// 2) 旋转（如需）
			if sticker.Rotation != 0 {
				rotationRadians := sticker.Rotation * 3.14159 / 180
				rotLabel := fmt.Sprintf("[st_rot_%d]", i)
				rotStep := fmt.Sprintf("%srotate=%.6f%s", currentSticker, rotationRadians, rotLabel)
				*filterComplexParts = append(*filterComplexParts, rotStep)
				currentSticker = rotLabel
			}

			// 3) 不透明度（如需）
			if sticker.Opacity > 0 && sticker.Opacity < 1.0 {
				opaLabel := fmt.Sprintf("[st_opa_%d]", i)
				opaStep := fmt.Sprintf("%sformat=rgba,colorchannelmixer=aa=%.3f%s", currentSticker, sticker.Opacity, opaLabel)
				*filterComplexParts = append(*filterComplexParts, opaStep)
				currentSticker = opaLabel
			}

			// 4) 叠加到主图（当使用scale2ref时，以其参考输出为主输入）
			overlayMain := refOutLabel
			if p.supportsRefScale {
				overlayMain = currentInput
			}
			overlayFilter := fmt.Sprintf("%s%soverlay=%s:%s%s", overlayMain, currentSticker, xPos, yPos, outputLabel)
			*filterComplexParts = append(*filterComplexParts, overlayFilter)

			global.GVA_LOG.Info("按主图尺寸等比缩放贴纸",
				zap.Int("贴纸索引", i),
				zap.Float64("最终宽度比例", finalWidthRatio),
				zap.Float64("最终高度比例", finalHeightRatio))

		} else if needTransform {
			// 传统变换处理
			var transforms []string

			// 缩放
			if scale != 1.0 {
				transforms = append(transforms, fmt.Sprintf("scale=iw*%.3f:ih*%.3f", scale, scale))
			}

			// 旋转
			if sticker.Rotation != 0 {
				rotationRadians := sticker.Rotation * 3.14159 / 180
				transforms = append(transforms, fmt.Sprintf("rotate=%.6f", rotationRadians))
			}

			// 透明度
			if sticker.Opacity > 0 && sticker.Opacity < 1.0 {
				transforms = append(transforms, fmt.Sprintf("format=rgba,colorchannelmixer=aa=%.3f", sticker.Opacity))
			}

			if len(transforms) > 0 {
				transformFilter := strings.Join(transforms, ",")
				stickerLabel := fmt.Sprintf("[sticker%d]", i)
				stickerPreFilter := fmt.Sprintf("[%d:v]%s%s", stickerInputIndex, transformFilter, stickerLabel)
				*filterComplexParts = append(*filterComplexParts, stickerPreFilter)

				// overlay滤镜使用变换后的贴纸
				overlayFilter := fmt.Sprintf("%s%soverlay=%s:%s%s", currentInput, stickerLabel, xPos, yPos, outputLabel)
				*filterComplexParts = append(*filterComplexParts, overlayFilter)
			}
		} else {
			// 不需要变换，直接overlay
			overlayFilter := fmt.Sprintf("%s[%d:v]overlay=%s:%s%s", currentInput, stickerInputIndex, xPos, yPos, outputLabel)
			*filterComplexParts = append(*filterComplexParts, overlayFilter)
		}

		currentInput = outputLabel // 下一个操作的输入是当前操作的输出
		stickerIndex++
	}

	return currentInput
}

// buildTextFilters 构建文字滤镜
func (p *FFmpegProcessor) buildTextFilters(filterComplexParts *[]string, currentInput string, titleSettings []request.TitleSetting) string {
	for _, title := range titleSettings {
		if title.Content == "" {
			continue
		}

		// 计算文字位置 - 根据height字段计算Y位置
		var yPos string
		if title.Height > 0 {
			// height是相对比例，转换为像素位置
			yPosPercent := title.Height * 100
			yPos = fmt.Sprintf("h*%.2f/100", yPosPercent)
		} else if title.Position != "" {
			// 兼容旧的position字段
			switch title.Position {
			case "top":
				yPos = "50"
			case "bottom":
				yPos = "h-text_h-50"
			default: // middle
				yPos = "(h-text_h)/2"
			}
		} else {
			// 默认居中
			yPos = "(h-text_h)/2"
		}

		// 文字对齐
		var xPos string
		switch title.Alignment {
		case "left":
			xPos = "50"
		case "right":
			xPos = "w-text_w-50"
		default: // center
			xPos = "(w-text_w)/2"
		}

		// 构建文字滤镜 - 使用相对于图片高度的字体大小
		baseFontSize := title.FontSize
		if baseFontSize <= 0 {
			baseFontSize = 24
		}

		// 将字体大小转换为相对于图片高度的比例
		fontSizeRatio := float64(baseFontSize) / 1000.0 // 基准高度1000px，字体更小
		fontSizeExpression := fmt.Sprintf("h*%.4f", fontSizeRatio)

		// 优先使用FontStyle.Color，其次使用FontColor兼容字段
		fontColor := title.FontStyle.Color
		if fontColor == "" && title.FontColor != "" {
			fontColor = title.FontColor
		}
		if fontColor == "" {
			fontColor = "white"
		}

		// 转义特殊字符
		content := strings.ReplaceAll(title.Content, "'", "\\'")
		content = strings.ReplaceAll(content, ":", "\\:")

		textFilter := fmt.Sprintf("%sdrawtext=text='%s':fontsize=%s:fontcolor=%s:x=%s:y=%s",
			currentInput, content, fontSizeExpression, fontColor, xPos, yPos)

		// 如果指定了字体
		if title.FontFamily != "" {
			fontPath := p.getFontPath(title.FontFamily)
			if fontPath != "" {
				textFilter += ":fontfile=" + fontPath
			}
		}

		// 处理字体样式
		if title.FontStyle.StyleType == "background" && title.FontStyle.BackgroundColor != "" {
			// 添加背景色 - 使用box参数
			textFilter += fmt.Sprintf(":box=1:boxcolor=%s@0.8:boxborderw=5", title.FontStyle.BackgroundColor)
		} else if title.FontStyle.StyleType == "border" && title.FontStyle.BorderColor != "" {
			// 添加描边效果
			borderWidth := title.FontStyle.BorderWidth
			if borderWidth <= 0 {
				borderWidth = 2
			}
			textFilter += fmt.Sprintf(":borderw=%d:bordercolor=%s", borderWidth, title.FontStyle.BorderColor)
		}

		// 生成输出标签
		outputLabel := fmt.Sprintf("[text%d]", len(*filterComplexParts))
		textFilter += outputLabel

		*filterComplexParts = append(*filterComplexParts, textFilter)
		currentInput = outputLabel // 下一个操作的输入是当前操作的输出
	}

	return currentInput
}

// downloadImage 下载图片到本地临时文件
func (p *FFmpegProcessor) downloadImage(imageUrl string) (string, error) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "ffmpeg-process")
	if err != nil {
		return "", fmt.Errorf("创建临时目录失败: %w", err)
	}

	// 下载图片
	resp, err := http.Get(imageUrl)
	if err != nil {
		os.RemoveAll(tempDir)
		return "", fmt.Errorf("下载图片失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		os.RemoveAll(tempDir)
		return "", fmt.Errorf("下载图片失败，状态码: %d", resp.StatusCode)
	}

	// 从URL中获取文件扩展名，如果无法获取则默认为.jpg
	var ext string
	if u, err := url.Parse(imageUrl); err == nil {
		if path.Ext(u.Path) != "" {
			ext = path.Ext(u.Path)
		} else {
			ext = ".jpg" // 默认扩展名
		}
	} else {
		ext = ".jpg"
	}

	// 生成临时文件名，保持原始扩展名
	fileName := fmt.Sprintf("sticker_%d%s", time.Now().UnixNano(), ext)
	localPath := filepath.Join(tempDir, fileName)

	// 创建本地文件
	file, err := os.Create(localPath)
	if err != nil {
		os.RemoveAll(tempDir)
		return "", fmt.Errorf("创建本地文件失败: %w", err)
	}
	defer file.Close()

	// 保存图片内容
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		os.RemoveAll(tempDir)
		return "", fmt.Errorf("保存图片失败: %w", err)
	}

	return localPath, nil
}

// getFontPath 获取字体文件路径
func (p *FFmpegProcessor) getFontPath(fontFamily string) string {
	if fontFamily == "" {
		return ""
	}

	// 处理字体文件名，移除可能的前缀斜杠或/fonts/前缀
	cleanFontName := strings.TrimPrefix(fontFamily, "/")
	cleanFontName = strings.TrimPrefix(cleanFontName, "fonts/")

	// OSS 字体基础 URL
	ossBaseURL := "https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/font/"
	var fontURL string
	if !strings.HasPrefix(fontFamily, "http") {
		fontURL = ossBaseURL + cleanFontName
	} else {
		fontURL = fontFamily
	}

	// 创建字体缓存目录
	fontCacheDir := filepath.Join(os.TempDir(), "font_cache")
	if err := os.MkdirAll(fontCacheDir, 0755); err != nil {
		global.GVA_LOG.Error("创建字体缓存目录失败", zap.Error(err))
		return ""
	}

	// 从fontFamily中提取实际的文件名（使用清理后的名称）
	var actualFileName string
	if strings.HasPrefix(fontFamily, "http") {
		if u, err := url.Parse(fontFamily); err == nil {
			actualFileName = filepath.Base(u.Path)
		} else {
			actualFileName = fontFamily
		}
	} else {
		// 使用清理后的字体名称作为文件名
		actualFileName = cleanFontName
	}

	// 本地字体文件路径
	localFontPath := filepath.Join(fontCacheDir, actualFileName)

	// 如果本地已存在该字体文件，直接返回
	if _, err := os.Stat(localFontPath); err == nil {
		return localFontPath
	}

	// 下载字体文件
	resp, err := http.Get(fontURL)
	if err != nil {
		global.GVA_LOG.Error("下载字体文件失败", zap.Error(err), zap.String("fontURL", fontURL))
		return ""
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		global.GVA_LOG.Error("字体文件下载失败", zap.Int("statusCode", resp.StatusCode), zap.String("fontURL", fontURL))
		return ""
	}

	// 创建本地字体文件
	fontFile, err := os.Create(localFontPath)
	if err != nil {
		global.GVA_LOG.Error("创建本地字体文件失败", zap.Error(err), zap.String("localFontPath", localFontPath))
		return ""
	}
	defer fontFile.Close()

	// 保存字体文件内容
	_, err = io.Copy(fontFile, resp.Body)
	if err != nil {
		global.GVA_LOG.Error("保存字体文件失败", zap.Error(err), zap.String("localFontPath", localFontPath))
		os.Remove(localFontPath) // 清理失败的文件
		return ""
	}

	global.GVA_LOG.Info("字体文件下载成功", zap.String("fontFamily", fontFamily), zap.String("localPath", localFontPath))
	return localFontPath
}

// copyFile 复制文件（当不需要处理时使用）
func (p *FFmpegProcessor) copyFile(src, dst string) (string, error) {
	srcFile, err := os.Open(src)
	if err != nil {
		return "", err
	}
	defer srcFile.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return "", err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	if err != nil {
		return "", err
	}

	return dst, nil
}
