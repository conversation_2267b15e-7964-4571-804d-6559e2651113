package imageprocessor

import (
	"bytes"
	"fmt"
	"image"
	"image/color"
	"image/draw"
	"image/jpeg"
	"image/png"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/disintegration/imaging"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai/request"
	"github.com/golang/freetype"
	"github.com/golang/freetype/truetype"
	"go.uber.org/zap"
	"golang.org/x/image/font"
	"golang.org/x/image/math/fixed"
)

// GoImagingProcessor Go图像库处理器
type GoImagingProcessor struct{}

// NewGoImagingProcessor 创建Go图像库处理器
func NewGoImagingProcessor() *GoImagingProcessor {
	return &GoImagingProcessor{}
}

// ProcessImage 处理图片
func (p *GoImagingProcessor) ProcessImage(config ProcessConfig) (string, error) {
	// 如果不需要任何处理，直接复制
	if !config.NeedTitle && !config.NeedSticker {
		return p.copyFile(config.InputImagePath, config.OutputImagePath)
	}

	// 统一改为：贴纸与文字均走Go图像库
	return p.processWithGoImaging(config)
}

// SupportsTextRendering 是否支持文字渲染
func (p *GoImagingProcessor) SupportsTextRendering() bool {
	return true // Go图像库始终支持文字渲染
}

// SupportsStickerOverlay 是否支持贴纸叠加
func (p *GoImagingProcessor) SupportsStickerOverlay() bool {
	// 现在贴纸叠加也由Go图像库完成
	return true
}

// GetName 获取处理器名称
func (p *GoImagingProcessor) GetName() string {
	return "GoImaging"
}

// processWithGoImaging 使用Go图像库处理图片
func (p *GoImagingProcessor) processWithGoImaging(config ProcessConfig) (string, error) {
	// 首先处理贴纸（如果需要）
	var intermediateImagePath string
	var err error

	if config.NeedSticker && len(config.StickerSettings) > 0 {
		// 直接使用Go图像库叠加贴纸
		fallbackPath := strings.Replace(config.OutputImagePath, "processed_", "sticker_", 1)
		if path, fbErr := p.overlayStickersWithGo(config.InputImagePath, config.StickerSettings, fallbackPath); fbErr == nil {
			intermediateImagePath = path
		} else {
			global.GVA_LOG.Error("Go贴纸叠加失败", zap.Error(fbErr))
			intermediateImagePath = config.InputImagePath
		}
	} else {
		intermediateImagePath = config.InputImagePath
	}

	// 如果不需要文字，直接返回贴纸处理的结果
	if !config.NeedTitle || len(config.TitleSettings) == 0 {
		if intermediateImagePath != config.InputImagePath {
			return intermediateImagePath, nil
		}
		return p.copyFile(config.InputImagePath, config.OutputImagePath)
	}

	// 使用Go图像库添加文字
	processedImagePath, err := p.addTextWithGoImaging(intermediateImagePath, config.TitleSettings, config.OutputImagePath)
	if err != nil {
		global.GVA_LOG.Error("Go图像库添加文字失败", zap.Error(err))
		// 如果文字处理失败，返回中间结果
		if intermediateImagePath != config.InputImagePath {
			return intermediateImagePath, nil
		}
		return p.copyFile(config.InputImagePath, config.OutputImagePath)
	}

	// 清理中间文件
	if intermediateImagePath != config.InputImagePath && intermediateImagePath != processedImagePath {
		os.Remove(intermediateImagePath)
	}

	return processedImagePath, nil
}

// overlayStickersWithGo 使用Go图像库简单叠加贴纸（兜底用）
func (p *GoImagingProcessor) overlayStickersWithGo(basePath string, stickerSettings []request.StickerSetting, outputPath string) (string, error) {
	// 打开底图
	baseImg, err := imaging.Open(basePath)
	if err != nil {
		return "", fmt.Errorf("打开底图失败: %w", err)
	}

	// RGBA画布
	canvas := imaging.Clone(baseImg)
	rgba := image.NewRGBA(canvas.Bounds())
	draw.Draw(rgba, rgba.Bounds(), canvas, canvas.Bounds().Min, draw.Src)

	for _, st := range stickerSettings {
		if st.MediaUrl == "" {
			continue
		}
		// 下载贴纸
		resp, err := http.Get(st.MediaUrl)
		if err != nil {
			continue
		}
		data, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			continue
		}
		// 解码贴纸（支持png/jpeg）
		stickerImg, _, err := image.Decode(bytes.NewReader(data))
		if err != nil {
			continue
		}
		// 目标尺寸（使用底图像素×比例）
		bw := rgba.Bounds().Dx()
		bh := rgba.Bounds().Dy()
		targetW := int(float64(bw) * st.WidthRatio)
		targetH := int(float64(bh) * st.HeightRatio)
		if targetW < 1 {
			targetW = 1
		}
		if targetH < 1 {
			targetH = 1
		}
		// 等比缩放到“收缩适配盒”
		stickerImg = imaging.Fit(stickerImg, targetW, targetH, imaging.Lanczos)

		// 位置（基于中心点）
		x := int(float64(bw)*st.X) - stickerImg.Bounds().Dx()/2
		y := int(float64(bh)*st.Y) - stickerImg.Bounds().Dy()/2
		// 叠加
		draw.Draw(rgba, image.Rect(x, y, x+stickerImg.Bounds().Dx(), y+stickerImg.Bounds().Dy()), stickerImg, image.Point{}, draw.Over)
	}

	// 保存
	if err := p.saveProcessedImage(rgba, outputPath); err != nil {
		return "", err
	}
	return outputPath, nil
}

// addTextWithGoImaging 使用Go图像库添加文字
func (p *GoImagingProcessor) addTextWithGoImaging(imagePath string, titleSettings []request.TitleSetting, outputPath string) (string, error) {
	// 打开图片
	img, err := imaging.Open(imagePath)
	if err != nil {
		return "", fmt.Errorf("打开图片失败: %w", err)
	}

	// 转换为RGBA格式以支持文字绘制
	bounds := img.Bounds()
	rgba := image.NewRGBA(bounds)
	draw.Draw(rgba, bounds, img, bounds.Min, draw.Src)

	// 为每个标题设置绘制文字
	for _, title := range titleSettings {
		if title.Content == "" {
			continue
		}

		err := p.drawTextOnImage(rgba, title)
		if err != nil {
			global.GVA_LOG.Error("绘制文字失败", zap.Error(err), zap.String("text", title.Content))
			// 继续处理其他文字，不中断整个流程
		}
	}

	// 保存处理后的图片
	err = p.saveProcessedImage(rgba, outputPath)
	if err != nil {
		return "", fmt.Errorf("保存图片失败: %w", err)
	}

	return outputPath, nil
}

// drawTextOnImage 在图片上绘制文字
func (p *GoImagingProcessor) drawTextOnImage(img *image.RGBA, title request.TitleSetting) error {
	bounds := img.Bounds()
	imgWidth := bounds.Max.X
	imgHeight := bounds.Max.Y

	// 设置字体
	fontPath := p.getFontPath(title.FontFamily)
	if fontPath == "" {
		// 无法获取字体文件，跳过文字绘制
		global.GVA_LOG.Warn("无法获取字体文件，跳过文字绘制", zap.String("fontFamily", title.FontFamily))
		return nil
	}

	fontBytes, err := ioutil.ReadFile(fontPath)
	if err != nil {
		global.GVA_LOG.Error("读取字体文件失败", zap.Error(err), zap.String("fontPath", fontPath))
		return nil // 不返回错误，继续处理其他文字
	}

	f, err := truetype.Parse(fontBytes)
	if err != nil {
		global.GVA_LOG.Error("解析字体文件失败", zap.Error(err), zap.String("fontPath", fontPath))
		return nil
	}

	// 创建字体上下文
	ctx := freetype.NewContext()
	ctx.SetDst(img)
	ctx.SetClip(bounds)
	ctx.SetFont(f)
	ctx.SetDPI(72) // 设置DPI

	// 计算字体大小
	baseFontSize := title.FontSize
	if baseFontSize <= 0 {
		baseFontSize = 24
	}
	// 将字体大小转换为相对于图片高度的大小
	fontSize := float64(baseFontSize) * float64(imgHeight) / 1000.0
	ctx.SetFontSize(fontSize)

	// 设置文字颜色 - 优先使用FontColor，其次使用FontStyle.Color
	var colorToUse string
	if title.FontColor != "" {
		colorToUse = title.FontColor
	} else if title.FontStyle.Color != "" {
		colorToUse = title.FontStyle.Color
	} else {
		colorToUse = "white" // 默认白色
	}

	global.GVA_LOG.Debug("GoImaging颜色设置",
		zap.String("FontColor", title.FontColor),
		zap.String("StyleColor", title.FontStyle.Color),
		zap.String("最终使用颜色", colorToUse))

	fontColor := p.parseColor(colorToUse, "white")
	if rgba, ok := fontColor.(color.RGBA); ok {
		global.GVA_LOG.Info("GoImaging颜色解析详情",
			zap.String("输入颜色", colorToUse),
			zap.Uint8("R", rgba.R),
			zap.Uint8("G", rgba.G),
			zap.Uint8("B", rgba.B),
			zap.Uint8("A", rgba.A))
	} else {
		global.GVA_LOG.Warn("颜色类型不是RGBA", zap.Any("color", fontColor))
	}

	// 设置绘制上下文
	ctx.SetSrc(image.NewUniform(fontColor))
	ctx.SetHinting(font.HintingNone)

	// 重要：确保使用正确的绘制操作
	// freetype默认使用draw.Over，这对于彩色文字很重要
	// 让我们先检查目标图像是否支持适当的颜色模式

	// 计算文字位置
	textWidth, textHeight := p.getTextDimensions(title.Content, f, fontSize)

	var x, y int

	// 计算Y位置
	// freetype的Y坐标是基线位置，需要加上文字高度的一定比例来调整到正确位置
	// 文字高度的约0.8倍是从顶部到基线的距离
	baselineOffset := int(textHeight * 0.8)

	if title.Height > 0 {
		// height是相对比例，转换为像素位置
		y = int(title.Height*float64(imgHeight)) + baselineOffset
	} else if title.Position != "" {
		// 兼容旧的position字段
		switch title.Position {
		case "top":
			y = 50 + baselineOffset
		case "bottom":
			y = imgHeight - 50 - int(textHeight) + baselineOffset
		default: // middle
			y = imgHeight/2 + baselineOffset
		}
	} else {
		// 默认居中
		y = imgHeight/2 + baselineOffset
	}

	// 计算X位置
	switch title.Alignment {
	case "left":
		x = 50
	case "right":
		x = imgWidth - int(textWidth) - 50
	default: // center
		x = (imgWidth - int(textWidth)) / 2
	}

	// 绘制背景或描边（如果需要）
	if title.FontStyle.StyleType == "background" && title.FontStyle.BackgroundColor != "" {
		p.drawTextBackground(img, x, y, int(textWidth), int(textHeight), title.FontStyle.BackgroundColor)
	} else if title.FontStyle.StyleType == "border" && title.FontStyle.BorderColor != "" {
		p.drawTextBorder(img, title.Content, f, fontSize, x, y, title.FontStyle.BorderColor, title.FontStyle.BorderWidth)
	}

	// 尝试使用golang.org/x/image/font包的Drawer来绘制文字
	drawer := &font.Drawer{
		Dst:  img,
		Src:  image.NewUniform(fontColor),
		Face: truetype.NewFace(f, &truetype.Options{Size: fontSize}),
		Dot:  fixed.Point26_6{X: fixed.Int26_6(x << 6), Y: fixed.Int26_6(y << 6)},
	}
	drawer.DrawString(title.Content)

	global.GVA_LOG.Info("使用Drawer绘制文字完成",
		zap.String("text", title.Content),
		zap.Int("x", x),
		zap.Int("y", y))

	return nil
}

// getFontPath 获取字体文件路径
func (p *GoImagingProcessor) getFontPath(fontFamily string) string {
	if fontFamily == "" {
		return ""
	}

	// 处理字体文件名，移除可能的前缀斜杠或/fonts/前缀
	cleanFontName := strings.TrimPrefix(fontFamily, "/")
	cleanFontName = strings.TrimPrefix(cleanFontName, "fonts/")

	// OSS 字体基础 URL
	ossBaseURL := "https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/font/"
	var fontURL string
	if !strings.HasPrefix(fontFamily, "http") {
		fontURL = ossBaseURL + cleanFontName
	} else {
		fontURL = fontFamily
	}

	// 创建字体缓存目录
	fontCacheDir := filepath.Join(os.TempDir(), "font_cache")
	if err := os.MkdirAll(fontCacheDir, 0755); err != nil {
		global.GVA_LOG.Error("创建字体缓存目录失败", zap.Error(err))
		return ""
	}

	// 从fontFamily中提取实际的文件名（使用清理后的名称）
	var actualFileName string
	if strings.HasPrefix(fontFamily, "http") {
		if u, err := url.Parse(fontFamily); err == nil {
			actualFileName = filepath.Base(u.Path)
		} else {
			actualFileName = fontFamily
		}
	} else {
		// 使用清理后的字体名称作为文件名
		actualFileName = cleanFontName
	}

	// 本地字体文件路径
	localFontPath := filepath.Join(fontCacheDir, actualFileName)

	// 如果本地已存在该字体文件，直接返回
	if _, err := os.Stat(localFontPath); err == nil {
		return localFontPath
	}

	// 下载字体文件
	resp, err := http.Get(fontURL)
	if err != nil {
		global.GVA_LOG.Error("下载字体文件失败", zap.Error(err), zap.String("fontURL", fontURL))
		return ""
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		global.GVA_LOG.Error("字体文件下载失败", zap.Int("statusCode", resp.StatusCode), zap.String("fontURL", fontURL))
		return ""
	}

	// 创建本地字体文件
	fontFile, err := os.Create(localFontPath)
	if err != nil {
		global.GVA_LOG.Error("创建本地字体文件失败", zap.Error(err), zap.String("localFontPath", localFontPath))
		return ""
	}
	defer fontFile.Close()

	// 保存字体文件内容
	_, err = io.Copy(fontFile, resp.Body)
	if err != nil {
		global.GVA_LOG.Error("保存字体文件失败", zap.Error(err), zap.String("localFontPath", localFontPath))
		os.Remove(localFontPath) // 清理失败的文件
		return ""
	}

	global.GVA_LOG.Info("字体文件下载成功", zap.String("fontFamily", fontFamily), zap.String("localPath", localFontPath))
	return localFontPath
}

// parseColor 解析颜色字符串
func (p *GoImagingProcessor) parseColor(colorStr, fallbackColor string) color.Color {
	if colorStr == "" {
		colorStr = fallbackColor
	}
	if colorStr == "" {
		colorStr = "white"
	}

	// 移除#符号
	colorStr = strings.TrimPrefix(colorStr, "#")

	// 支持常见颜色名称
	switch strings.ToLower(colorStr) {
	case "white":
		return color.RGBA{255, 255, 255, 255}
	case "black":
		return color.RGBA{0, 0, 0, 255}
	case "red":
		return color.RGBA{255, 0, 0, 255}
	case "green":
		return color.RGBA{0, 255, 0, 255}
	case "blue":
		return color.RGBA{0, 0, 255, 255}
	case "yellow":
		return color.RGBA{255, 255, 0, 255}
	}

	// 解析十六进制颜色
	if len(colorStr) == 6 {
		// RGB格式: #RRGGBB
		if r, err := strconv.ParseUint(colorStr[0:2], 16, 8); err == nil {
			if g, err := strconv.ParseUint(colorStr[2:4], 16, 8); err == nil {
				if b, err := strconv.ParseUint(colorStr[4:6], 16, 8); err == nil {
					return color.RGBA{uint8(r), uint8(g), uint8(b), 255}
				}
			}
		}
	} else if len(colorStr) == 8 {
		// RGBA格式: #RRGGBBAA
		if r, err := strconv.ParseUint(colorStr[0:2], 16, 8); err == nil {
			if g, err := strconv.ParseUint(colorStr[2:4], 16, 8); err == nil {
				if b, err := strconv.ParseUint(colorStr[4:6], 16, 8); err == nil {
					if a, err := strconv.ParseUint(colorStr[6:8], 16, 8); err == nil {
						return color.RGBA{uint8(r), uint8(g), uint8(b), uint8(a)}
					}
				}
			}
		}
	}

	// 默认白色
	return color.RGBA{255, 255, 255, 255}
}

// getTextDimensions 获取文字尺寸
func (p *GoImagingProcessor) getTextDimensions(text string, f *truetype.Font, fontSize float64) (float64, float64) {
	opts := truetype.Options{Size: fontSize}
	face := truetype.NewFace(f, &opts)

	// 测量文字尺寸
	bounds, _ := font.BoundString(face, text)
	width := float64(bounds.Max.X-bounds.Min.X) / 64
	height := float64(bounds.Max.Y-bounds.Min.Y) / 64

	return width, height
}

// drawTextBackground 绘制文字背景
func (p *GoImagingProcessor) drawTextBackground(img *image.RGBA, x, y, width, height int, bgColorStr string) {
	bgColor := p.parseColor(bgColorStr, "black")

	// 添加一些边距
	padding := 10
	x -= padding
	y -= int(float64(height) * 1.2) // 调整Y位置，因为文字基线问题
	width += padding * 2
	height = int(float64(height) * 1.5)

	// 绘制背景矩形
	for py := y; py < y+height && py < img.Bounds().Max.Y; py++ {
		if py < 0 {
			continue
		}
		for px := x; px < x+width && px < img.Bounds().Max.X; px++ {
			if px < 0 {
				continue
			}
			img.Set(px, py, bgColor)
		}
	}
}

// drawTextBorder 绘制文字描边
func (p *GoImagingProcessor) drawTextBorder(img *image.RGBA, text string, f *truetype.Font, fontSize float64, x, y int, borderColorStr string, borderWidth int) {
	if borderWidth <= 0 {
		borderWidth = 2
	}

	borderColor := p.parseColor(borderColorStr, "black")

	// 创建用于描边的上下文
	ctx := freetype.NewContext()
	ctx.SetDst(img)
	ctx.SetClip(img.Bounds())
	ctx.SetFont(f)
	ctx.SetFontSize(fontSize)
	ctx.SetSrc(image.NewUniform(borderColor))

	// 绘制多个偏移的文字来模拟描边效果
	for dx := -borderWidth; dx <= borderWidth; dx++ {
		for dy := -borderWidth; dy <= borderWidth; dy++ {
			if dx == 0 && dy == 0 {
				continue // 跳过中心位置，这里会绘制主文字
			}
			pt := freetype.Pt(x+dx, y+dy)
			ctx.DrawString(text, pt)
		}
	}
}

// saveProcessedImage 保存处理后的图片
func (p *GoImagingProcessor) saveProcessedImage(img image.Image, outputPath string) error {
	outFile, err := os.Create(outputPath)
	if err != nil {
		return err
	}
	defer outFile.Close()

	// 获取文件扩展名
	ext := strings.ToLower(filepath.Ext(outputPath))

	switch ext {
	case ".jpg", ".jpeg":
		// JPEG格式
		err = jpeg.Encode(outFile, img, &jpeg.Options{Quality: 90})
	case ".png":
		// PNG格式
		err = png.Encode(outFile, img)
	default:
		// 默认使用JPEG格式
		err = jpeg.Encode(outFile, img, &jpeg.Options{Quality: 90})
	}

	return err
}

// copyFile 复制文件（当不需要处理时使用）
func (p *GoImagingProcessor) copyFile(src, dst string) (string, error) {
	srcFile, err := os.Open(src)
	if err != nil {
		return "", err
	}
	defer srcFile.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return "", err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	if err != nil {
		return "", err
	}

	return dst, nil
}
