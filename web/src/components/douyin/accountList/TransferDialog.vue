<template>
  <el-dialog v-model="visible" title="账号转移" width="500px" center draggable>
    <div class="transfer-content">
      <div class="transfer-info mb-4">
        <el-alert
          title="转移说明"
          type="info"
          show-icon
          :closable="false"
          description="转移后，该账号及其绑定的手机号归属将转移至选择的系统用户下，转移的账号将自动排在新用户的账号列表首位。"
        />
      </div>

      <el-form :model="formData" label-width="100px">
        <el-form-item label="转移账号">
          <div class="flex items-center gap-2">
            <el-avatar :size="40" :src="getAvatarUrl(formData.avatar)" />
            <div>
              <div class="font-medium">{{ formData.nickname }}</div>
              <div class="text-sm text-gray-500">{{ formData.uniqueId }}</div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="当前归属">
          <span>{{ formData.sysUserName || '未知用户' }}</span>
        </el-form-item>

        <el-form-item label="绑定手机">
          <span v-if="formData.bindPhone">
            {{ formData.bindPhone }}
            <el-tag size="small" type="info" class="ml-2">
              {{ formData.phoneOperator }}-{{ formData.phoneUserName || '未实名' }}
            </el-tag>
          </span>
          <span v-else class="text-gray-500">未绑定</span>
        </el-form-item>

        <el-form-item label="转移到" required>
          <el-select
            v-model="formData.targetUserId"
            placeholder="请选择目标系统用户"
            style="width: 100%"
            filterable
            loading-text="加载中..."
          >
            <el-option
              v-for="user in systemUserList"
              :key="user.id"
              :label="user.nickName || user.userName"
              :value="user.id"
              :disabled="user.id === formData.currentUserId"
            >
              <div class="flex justify-between">
                <span>{{ user.nickName || user.userName }}</span>
                <span class="text-gray-400" v-if="user.id === formData.currentUserId">(当前用户)</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :disabled="!formData.targetUserId" :loading="loading">
        确定转移
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, computed, watch } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { transferUser, getSystemUserList } from '@/api/douyin/dyUser'

  defineOptions({
    name: 'TransferDialog'
  })

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    },
    userData: {
      type: Object,
      default: () => ({})
    }
  })

  const emit = defineEmits(['update:modelValue', 'success'])

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  const loading = ref(false)
  const systemUserList = ref([])

  const formData = ref({
    id: null,
    nickname: '',
    uniqueId: '',
    avatar: '',
    sysUserName: '',
    bindPhone: '',
    phoneOperator: '',
    phoneUserName: '',
    currentUserId: null,
    targetUserId: null
  })

  // 监听用户数据变化，更新表单
  watch(
    () => props.userData,
    async (newData) => {
      if (newData && Object.keys(newData).length > 0) {
        formData.value = {
          id: newData.ID,
          nickname: newData.nickname,
          uniqueId: newData.uniqueId,
          avatar: newData.avatar,
          sysUserName: newData.sysUserName,
          bindPhone: newData.bindPhone,
          phoneOperator: newData.phoneOperator,
          phoneUserName: newData.phoneUserName,
          currentUserId: newData.sysUserId,
          targetUserId: null
        }

        // 获取系统用户列表
        await loadSystemUserList()
      }
    },
    { immediate: true }
  )

  // 获取头像URL
  const getAvatarUrl = (avatarJson) => {
    try {
      if (!avatarJson) return ''
      const avatarData = JSON.parse(avatarJson)
      return avatarData.url_list?.[0] || ''
    } catch (err) {
      console.error('解析头像JSON失败:', err)
      return ''
    }
  }

  // 加载系统用户列表
  const loadSystemUserList = async () => {
    try {
      const res = await getSystemUserList()
      if (res.code === 0) {
        systemUserList.value = res.data || []
      } else {
        ElMessage.error('获取系统用户列表失败: ' + res.msg)
      }
    } catch (err) {
      console.error('获取系统用户列表失败:', err)
      ElMessage.error('获取系统用户列表失败')
    }
  }

  const handleCancel = () => {
    visible.value = false
  }

  const handleConfirm = async () => {
    if (!formData.value.targetUserId) {
      ElMessage.warning('请选择目标用户')
      return
    }

    if (formData.value.targetUserId === formData.value.currentUserId) {
      ElMessage.warning('不能转移到当前用户')
      return
    }

    // 确认对话框
    try {
      await ElMessageBox.confirm(
        `确定要将账号 "${formData.value.nickname}" 转移到选择的系统用户下吗？转移后该账号将排在新用户的账号列表首位。`,
        '确认转移',
        {
          confirmButtonText: '确定转移',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      loading.value = true
      const res = await transferUser({
        id: formData.value.id,
        targetUserId: formData.value.targetUserId
      })

      if (res.code === 0) {
        ElMessage.success('账号转移成功')
        visible.value = false
        emit('success')
      } else {
        ElMessage.error('转移失败: ' + res.msg)
      }
    } catch (err) {
      if (err !== 'cancel') {
        console.error('转移失败:', err)
        ElMessage.error('转移失败: ' + (err.message || err))
      }
    } finally {
      loading.value = false
    }
  }
</script>

<style scoped>
  .transfer-content {
    padding: 16px;
  }

  .transfer-info {
    margin-bottom: 20px;
  }
</style>
