<template>
  <div class="flex flex-wrap items-center gap-3 mb-4">
    <el-button type="primary" icon="Key" @click="$emit('show-auth-dialog')" class="flex-none"> 账号授权 </el-button>
    <div class="flex-1 flex flex-wrap gap-3">
      <el-input
        v-model="searchData.nickname"
        placeholder="请输入用户昵称"
        class="w-full sm:w-64 md:w-56 lg:w-64"
        clearable
        @input="handleInput"
      />
      <el-input
        v-model="searchData.uniqueId"
        placeholder="请输入抖音号"
        class="w-full sm:w-64 md:w-56 lg:w-64"
        clearable
        @input="handleInput"
      />
      <el-button type="primary" icon="search" @click="handleSubmit">查询</el-button>
      <el-button type="success" icon="Refresh" @click="$emit('refresh-all')">刷新</el-button>
      <el-button type="danger" icon="Refresh" @click="$emit('filter-invalid-login')">筛选未登录</el-button>
      <el-button type="warning" icon="Download" @click="$emit('export-uid')">导出UID</el-button>
    </div>
  </div>
</template>

<script setup>
  import { reactive, watch } from 'vue'

  // 定义props
  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => ({
        nickname: null,
        uniqueId: null
      })
    }
  })

  // 定义事件
  const emit = defineEmits([
    'update:modelValue',
    'submit',
    'show-auth-dialog',
    'refresh-all',
    'filter-invalid-login',
    'export-uid'
  ])

  // 本地搜索数据
  const searchData = reactive({
    nickname: props.modelValue.nickname,
    uniqueId: props.modelValue.uniqueId
  })

  // 监听props变化
  watch(
    () => props.modelValue,
    (newVal) => {
      searchData.nickname = newVal.nickname
      searchData.uniqueId = newVal.uniqueId
    },
    { deep: true }
  )

  // 处理输入变化
  const handleInput = () => {
    emit('update:modelValue', {
      nickname: searchData.nickname,
      uniqueId: searchData.uniqueId
    })
  }

  // 处理提交
  const handleSubmit = () => {
    emit('submit')
  }
</script>

<style scoped>
  /* 组件特有样式可以在这里添加 */
</style>
