<template>
  <el-dialog v-model="visible" title="实名认证" width="400px" center draggable>
    <el-form :model="formData" label-width="80px">
      <el-form-item label="用户">
        <span>{{ formData.nickname }}</span>
      </el-form-item>
      <el-form-item label="姓名">
        <div class="flex gap-2 items-start">
          <el-input v-model="formData.realName" placeholder="请输入真实姓名，留空表示清空" class="flex-1" />
          <el-button type="warning" size="default" @click="clearRealNameInput" :disabled="!formData.realName">
            清空
          </el-button>
        </div>
        <div class="text-gray-500 text-sm mt-1">请输入真实姓名，或点击清空按钮清除当前实名认证</div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { bindDevice } from '@/api/douyin/dyUser'

  defineOptions({
    name: 'RealNameDialog'
  })

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    },
    userData: {
      type: Object,
      default: () => ({})
    }
  })

  const emit = defineEmits(['update:modelValue', 'success'])

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  const formData = ref({
    id: null,
    nickname: '',
    realName: ''
  })

  // 监听用户数据变化，更新表单
  watch(
    () => props.userData,
    (newData) => {
      if (newData && Object.keys(newData).length > 0) {
        formData.value = {
          id: newData.ID,
          nickname: newData.nickname,
          realName: newData.realName || ''
        }
      }
    },
    { immediate: true }
  )

  const clearRealNameInput = () => {
    formData.value.realName = ''
  }

  const handleCancel = () => {
    visible.value = false
  }

  const handleConfirm = async () => {
    try {
      const res = await bindDevice({
        id: formData.value.id,
        realName: formData.value.realName
      })

      if (res.code === 0) {
        ElMessage.success(res.msg || '操作成功')
        visible.value = false
        emit('success')
      }
    } catch (err) {
      console.error('操作失败:', err.message)
      ElMessage.error('操作失败：' + err.message)
    }
  }
</script>
