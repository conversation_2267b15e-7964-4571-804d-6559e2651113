<template>
  <el-dialog v-model="visible" title="绑定手机号" width="400px" center draggable>
    <el-form :model="formData" label-width="80px">
      <el-form-item label="用户">
        <span>{{ formData.nickname }}</span>
      </el-form-item>
      <el-form-item label="手机号">
        <div class="flex gap-2 items-start">
          <el-autocomplete
            v-model="formData.phone"
            :fetch-suggestions="phoneSearch"
            placeholder="请输入手机号搜索，留空表示解绑"
            @select="phoneSelected"
            :trigger-on-focus="true"
            popper-class="phone-suggestions"
            class="flex-1"
          >
            <template #default="{ item }">
              <div class="flex flex-col">
                <span>{{ item.phoneNumber }}</span>
                <span class="text-gray-500 text-sm"
                  >{{ item.operatorType === 'mobile' ? '移动' : item.operatorType === 'unicom' ? '联通' : '电信' }} -
                  {{ item.realName || '未实名' }}</span
                >
              </div>
            </template>
          </el-autocomplete>
          <el-button type="warning" size="default" @click="clearPhoneInput" :disabled="!formData.phone">
            清空
          </el-button>
        </div>
        <div class="text-gray-500 text-sm mt-1">
          {{ formData.phone ? '请从下拉列表中选择手机号' : '清空手机号后确定将解绑当前手机号' }}
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">
        {{ formData.phone ? '绑定' : '解绑' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { bindDevice } from '@/api/douyin/dyUser'
  import { findPhone } from '@/api/douyin/phoneBalance'

  defineOptions({
    name: 'PhoneBindDialog'
  })

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    },
    userData: {
      type: Object,
      default: () => ({})
    }
  })

  const emit = defineEmits(['update:modelValue', 'success'])

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  const formData = ref({
    id: null,
    nickname: '',
    phone: '',
    selectedPhone: null
  })

  // 监听用户数据变化，更新表单
  watch(
    () => props.userData,
    (newData) => {
      if (newData && Object.keys(newData).length > 0) {
        formData.value = {
          id: newData.ID,
          nickname: newData.nickname,
          phone: newData.bindPhone || '',
          selectedPhone: null
        }
      }
    },
    { immediate: true }
  )

  const phoneSearch = async (phone, cb) => {
    try {
      if (phone.length < 3) {
        cb([])
        return
      }
      const res = await findPhone({ phoneNumber: phone })
      if (res.code === 0) {
        cb(res.data)
      } else {
        ElMessage.error('模糊查找手机号失败：' + res.msg)
        cb([])
      }
    } catch (err) {
      console.error('模糊查找手机号请求出错：', err.message)
      ElMessage.error('模糊查找手机号请求出错：' + err.message)
      cb([])
    }
  }

  const phoneSelected = (item) => {
    formData.value.phone = item.phoneNumber
    formData.value.selectedPhone = item.phoneNumber
  }

  const clearPhoneInput = () => {
    formData.value.phone = ''
    formData.value.selectedPhone = null
  }

  const handleCancel = () => {
    visible.value = false
  }

  const handleConfirm = async () => {
    // 如果手机号为空，表示解绑操作
    if (!formData.value.phone || formData.value.phone.trim() === '') {
      try {
        const res = await bindDevice({
          id: formData.value.id,
          phone: '' // 传空字符串表示解绑
        })

        if (res.code === 0) {
          ElMessage.success('解绑成功')
          visible.value = false
          emit('success')
        }
      } catch (err) {
        console.error('解绑失败:', err.message)
        ElMessage.error('解绑失败：' + err.message)
      }
      return
    }

    // 如果手动修改了手机号(即当前输入的手机号与选中的手机号不一致)
    if (formData.value.phone !== formData.value.selectedPhone) {
      try {
        // 检查手动输入的手机号是否存在于数据库
        const res = await findPhone({ phoneNumber: formData.value.phone })
        if (res.code === 0 && res.data && res.data.length > 0) {
          // 如果找到了手机号，更新selectedPhone
          formData.value.selectedPhone = formData.value.phone
        } else {
          ElMessage.warning('该手机号不在系统中，请从下拉列表中选择')
          return
        }
      } catch (err) {
        console.error('验证手机号失败:', err.message)
        ElMessage.error('验证手机号失败：' + err.message)
        return
      }
    }

    try {
      const res = await bindDevice({
        id: formData.value.id,
        phone: formData.value.selectedPhone
      })

      if (res.code === 0) {
        ElMessage.success('绑定成功')
        visible.value = false
        emit('success')
      }
    } catch (err) {
      console.error('绑定失败:', err.message)
      ElMessage.error('绑定失败：' + err.message)
    }
  }
</script>

<style scoped>
  .phone-suggestions {
    max-width: 300px;
  }
</style>
