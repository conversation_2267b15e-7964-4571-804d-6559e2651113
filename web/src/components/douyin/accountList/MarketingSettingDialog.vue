<template>
  <el-dialog
    :model-value="visible"
    title="营销设定"
    width="600px"
    center
    draggable
    @close="handleClose"
    @update:model-value="$emit('update:visible', $event)"
  >
    <el-form :model="form" label-width="120px">
      <!-- 增加文字说明："评论设定：命中关键词->回复->关注->私信" -->
      <div class="bg-emerald-100 text-emerald-700 p-3 rounded mb-4">追踪一级评论：命中关键词->回复->关注->发私信</div>
      <!-- 评论设定部分 -->
      <el-form-item label="评论模板">
        <el-input
          v-model="form.commentTemplates"
          type="textarea"
          :rows="5"
          placeholder="请输入评论模板（一个换行表示一条）"
          style="width: calc(100% - 80px)"
        />
      </el-form-item>
      <el-form-item label="关键词">
        <el-input v-model="form.commentKeyword" placeholder="多个关键词用逗号分隔" style="width: calc(100% - 80px)" />
      </el-form-item>

      <el-divider />

      <!-- 新增回复设定部分 -->
      <div class="bg-emerald-100 text-emerald-700 p-3 rounded mb-4">私信授权：关注->发私信</div>
      <el-form-item label="第一条私信">
        <el-button type="primary" @click="handlePromotionLink(form)">获取推广链接</el-button>
        <span class="ml-2 text-sm" :class="form.promotionLink ? 'text-green-500' : 'text-red-500'">
          {{ form.promotionLink ? '已获取' : '未获取' }}
        </span>
      </el-form-item>
      <el-form-item label="第二条私信">
        <el-input
          v-model="form.replyTemplates"
          type="textarea"
          :rows="5"
          placeholder="请输入自动回复模板（一个换行表示一条）"
          style="width: calc(100% - 80px)"
        />
      </el-form-item>
      <el-form-item label="营销授权状态">
        <span v-if="form.talkAuthStatus === 0" class="text-gray-500">
          <el-icon>
            <CircleClose />
          </el-icon>
          未授权
        </span>
        <span v-else-if="form.talkAuthStatus === 1" class="text-green-500">
          <el-icon>
            <CircleCheck />
          </el-icon>
          正常
        </span>
        <span v-else-if="form.talkAuthStatus === 2" class="text-red-500">
          <el-icon>
            <Warning />
          </el-icon>
          授权失效
        </span>
        <span v-else-if="form.talkAuthStatus === 3" class="text-gray-500">
          <el-icon>
            <Close />
          </el-icon>
          禁用
        </span>
        <span v-else class="text-gray-500">
          <el-icon>
            <QuestionFilled />
          </el-icon>
          未知状态
        </span>
      </el-form-item>
      <el-form-item label="授权操作">
        <div class="flex items-center gap-4">
          <div class="flex gap-2">
            <el-button
              v-if="form.talkAuthStatus !== 3"
              type="primary"
              size="small"
              @click="handleTalkAuth(form, 1)"
              :loading="openBrowserLoading"
            >
              前往授权
            </el-button>
            <el-button v-if="form.biteBrowserId !== ''" type="warning" size="small" @click="clearTalkAuth(form)">
              清空授权
            </el-button>
            <el-button v-if="form.talkAuthStatus === 1" type="danger" size="small" @click="handleTalkAuth(form, 3)">
              禁用
            </el-button>
            <el-button
              v-if="form.talkAuthStatus === 3"
              type="success"
              size="small"
              @click="handleTalkAuth(form, 1)"
              :loading="openBrowserLoading"
            >
              启用
            </el-button>
          </div>
        </div>
      </el-form-item>
      <!-- 增加一列按钮"在比特浏览器登录抖音成功后，请按下此按钮"，当isOpenBrowser为true时显示 -->
      <el-form-item v-if="isOpenBrowser" label="">
        <el-button type="primary" size="small" @click="handlConfirmLoginBite">
          在比特浏览器成功登录抖音后，请按下此按钮
        </el-button>
      </el-form-item>

      <el-divider />
      <div>
        <span>营销状态：</span>
        <span v-if="form?.commentTraceStatus === 0" class="text-gray-500"
          ><el-icon>
            <CircleClose />
          </el-icon>
          未配置</span
        >
        <span v-else-if="form?.commentTraceStatus === 1" class="text-green-500"
          ><el-icon>
            <CircleCheck />
          </el-icon>
          已配置</span
        >
        <span v-else-if="form?.commentTraceStatus === 2" class="text-red-500"
          ><el-icon>
            <Warning />
          </el-icon>
          禁用</span
        >
      </div>
      <div>
        <span>状态设置：</span>
        <el-button-group v-if="form.commentTraceStatus === 1">
          <el-button
            size="small"
            type="warning"
            @click="
              ElMessageBox.confirm('确定要禁用该账号的自动营销功能吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => handleUpdateTrackStatus(2))
            "
            style="margin-right: 1px"
            >禁用</el-button
          >
        </el-button-group>
        <el-button-group v-else-if="form?.commentTraceStatus === 2">
          <el-button
            size="small"
            type="success"
            @click="
              ElMessageBox.confirm('确定要启用该账号的自动营销功能吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => handleUpdateTrackStatus(1))
            "
            style="margin-right: 1px"
            >启用</el-button
          >
        </el-button-group>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="saveContentSettings">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { CircleClose, CircleCheck, Warning, Close, QuestionFilled } from '@element-plus/icons-vue'
  import {
    updateCommentSetting,
    updateUserTalkAuthStatus,
    clearUserTalkAuthStatus,
    updateUserBiteBrowserId,
    updateUserPromotionLink,
    updateUserTraceStatus
  } from '@/api/douyin/dyUser'
  import {
    createBrowser,
    openBrowser,
    deleteBrowser,
    getBrowserInfo,
    updateBrowserInfo,
    updateBrowserProxy,
    getCookie
  } from '@/api/biteBrowser'
  import { saveDouyinWebCookie } from '@/api/douyin/chat'

  defineOptions({
    name: 'MarketingSettingDialog'
  })

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    userData: {
      type: Object,
      default: () => ({})
    }
  })

  const emit = defineEmits(['update:visible', 'success'])

  // 表单数据
  const form = ref({
    ID: null,
    commentTemplates: '',
    commentTraceStatus: 0,
    commentKeyword: '',
    talkAuthStatus: 0, // 私信授权状态
    promotionLink: '', // 推广链接
    biteBrowserId: '', // 比特浏览器ID
    proxy: '', // 代理
    uniqueId: '', // 抖音号
    replyTemplates: '' // 回复模板
  })

  //是否成功打开比特浏览器
  const isOpenBrowser = ref(false)
  // 打开比特浏览器loading效果
  const openBrowserLoading = ref(false)
  // 比特浏览器相关信息
  const biteBrowserInfo = ref({
    proxyMethod: 2,
    proxyType: 'http',
    proxyUserName: 'smarwpto',
    proxyPassword: '9a07e0r3',
    url: 'https://www.douyin.com'
  })

  // 监听用户数据变化，更新表单
  watch(
    () => props.userData,
    (newData) => {
      if (newData && Object.keys(newData).length > 0) {
        form.value = {
          ...newData,
          commentKeyword: newData.commentKeyword || '1,2,3,4,5,6,7,8,9'
        }
      }
    },
    { immediate: true, deep: true }
  )

  // 关闭弹窗
  const handleClose = () => {
    isOpenBrowser.value = false
    form.value = {
      ID: null,
      commentTemplates: '',
      commentTraceStatus: 0,
      commentKeyword: '',
      talkAuthStatus: 0,
      promotionLink: '',
      biteBrowserId: '',
      proxy: '',
      uniqueId: '',
      replyTemplates: ''
    }
    emit('update:visible', false)
  }

  // 保存设置
  const saveContentSettings = async () => {
    try {
      // 如果首次设置，自动开启
      if (form.value.commentTraceStatus === 0) {
        form.value.commentTraceStatus = 1
      }
      const res = await updateCommentSetting(form.value)
      if (res.code === 0) {
        ElMessage.success('设置保存成功')
        emit('success')
        handleClose()
      }
    } catch (err) {
      ElMessage.error('保存失败：' + err.message)
    }
  }

  // 营销授权
  const handleTalkAuth = async (row, opt) => {
    try {
      // 当opt为1时，进行授权操作；为3时，进行禁止授权操作
      if (opt === 1) {
        if (!row.biteBrowserId) {
          const [host, port] = row.bindIP.split(':')
          if (!host || !port) {
            ElMessage.error('无效的代理地址')
            return
          }
          // 创建浏览器配置
          const cData = await createBrowser({
            browserFingerPrint: {},
            proxyMethod: biteBrowserInfo.value.proxyMethod,
            proxyType: biteBrowserInfo.value.proxyType,
            host: host,
            port: port,
            proxyUserName: biteBrowserInfo.value.proxyUserName,
            proxyPassword: biteBrowserInfo.value.proxyPassword,
            url: biteBrowserInfo.value.url,
            name: `${row.nickname}(${row.uniqueId})`
          })
          if (!cData) {
            ElMessage.error('创建浏览器失败,请确保比特浏览器已开启')
            return
          }
          if (cData.data?.id) {
            form.value.biteBrowserId = cData.data.id
            // 发起请求,更新用户的浏览器id
            const res = await updateUserBiteBrowserId({
              id: row.ID,
              biteBrowserId: cData.data.id
            })
            if (res.code !== 0) {
              ElMessage.error('更新浏览器ID失败:', res.msg)
              return
            }
          } else {
            ElMessage.error('创建浏览器失败:', cData.msg)
            return
          }
        }
        // 执行浏览器打开操作
        openBrowserLoading.value = true
        ElMessage.warning('正在打开比特浏览器窗口，请稍后')
        await openBiteBrowser(row)
        // 关闭比特浏览器loading效果
        openBrowserLoading.value = false
      } else if (opt === 3) {
        // 发起请求,更新用户的授权状态,不是禁用时，需要等到获取到cookie后，再更新状态
        const res = await updateUserTalkAuthStatus({
          id: row.ID,
          talkAuthStatus: opt
        })
        if (res.code !== 0) {
          ElMessage.error('更新授权状态失败:', res.msg)
          return
        }
        ElMessage.success('操作成功')
        form.value.talkAuthStatus = 3
      } else {
        ElMessage.error('无效操作类型:', opt)
        return
      }
    } catch (err) {
      ElMessage.error(`操作失败: ${err.message}`)
    }
  }

  // 清空营销授权
  const clearTalkAuth = async (row) => {
    // 增加提示框
    const confirm = await ElMessageBox.confirm('确认清空营销授权吗？该操作不可恢复', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    if (confirm !== 'confirm') {
      return
    }

    // 获取浏览器窗口详情
    try {
      const infoRes = await getBrowserInfo({
        id: row.biteBrowserId
      })
      if (!infoRes) {
        ElMessage.error('获取浏览器窗口失败，请确保已打开比特浏览器')
        return
      }
    } catch (error) {
      console.error('获取浏览器窗口异常:', error)
      ElMessage.error('打开浏览器异常，请确保已打开比特浏览器')
      return
    }

    // 先修改数据库状态
    const res = await clearUserTalkAuthStatus({
      id: row.ID
    })
    if (res.code !== 0) {
      ElMessage.error('更新授权状态失败:', res.msg)
      return
    }
    const biteRes = await deleteBrowser({
      id: row.biteBrowserId
    })
    console.log('比特浏览器打开结果', biteRes)

    ElMessage.success('已清空营销授权')
    emit('success')
    handleClose()
  }

  // 打开比特浏览器
  const openBiteBrowser = async (row) => {
    try {
      console.log('用户信息', row)
      // 更新窗口标题，稳定后可以删除
      await updateBrowserInfo({
        ids: [row.biteBrowserId],
        name: `${row.nickname}(${row.uniqueId})`
      })

      const [host, port] = row.bindIP.split(':')
      if (!host || !port) {
        ElMessage.error('无效的代理地址')
        return
      }
      // 更新浏览器代理ip
      await updateBrowserProxy({
        ids: [row.biteBrowserId],
        proxyMethod: biteBrowserInfo.value.proxyMethod,
        proxyType: biteBrowserInfo.value.proxyType,
        host,
        port,
        proxyUserName: biteBrowserInfo.value.proxyUserName,
        proxyPassword: biteBrowserInfo.value.proxyPassword
      })

      const res = await openBrowser({ id: row.biteBrowserId })
      if (!res.success) {
        ElMessage.error('打开浏览器失败：第三方报错：' + res.msg)
        isOpenBrowser.value = false
        return null
      }
      isOpenBrowser.value = true
      return res
    } catch (err) {
      isOpenBrowser.value = false
      ElMessage.error('打开比特浏览器失败：请重启后重试：' + err.message)
      return null
    }
  }

  // 确认登录比特浏览器
  const handlConfirmLoginBite = async () => {
    try {
      if (!form.value.biteBrowserId) {
        ElMessage.warning('浏览器ID不存在，请先创建浏览器配置')
        return
      }

      const res = await getCookie({
        browserId: form.value.biteBrowserId
      })
      if (!res.success) {
        ElMessage.error('比特浏览器获取Cookie失败:', res.msg)
        return
      }
      let cookie = ''
      let ok = false
      for (const o of res.data) {
        if (o.name == 'sessionid') ok = true
        cookie += `${o.name}=${o.value}; `
      }
      // 对cookie过滤掉前后空格
      cookie = cookie.trim()
      if (!ok) {
        ElMessage.warning('请在比特浏览器登录抖音后再确认')
        return
      }
      // 发起请求saveDouyinWebCookie, 保存cookie
      const sRes = await saveDouyinWebCookie({
        id: form.value.ID,
        cookie: cookie
      })
      if (sRes.code !== 0) {
        ElMessage.error('保存web cookie失败:', sRes.msg)
        return
      }

      const uRes = await updateUserTalkAuthStatus({
        id: form.value.ID,
        talkAuthStatus: 1
      })
      console.log('更新授权状态', uRes)
      if (uRes.code !== 0) {
        ElMessage.error('更新授权状态失败:', uRes.msg)
        return
      }
      form.value.talkAuthStatus = 1
      ElMessage.success('授权成功')
    } catch (err) {
      ElMessage.error('获取Cookie失败:', err)
    }
  }

  // 获取推广链接
  const handlePromotionLink = async (row) => {
    try {
      if (row.promotionLink) {
        ElMessage.success('已设置推广链接')
        return
      }

      // 暂时写死推广链接
      const link = 'https://work.weixin.qq.com/kfid/kfc1ad32a44fefafc3f'
      console.log('请求参数', row.ID, link)
      const res = await updateUserPromotionLink({
        id: row.ID,
        promotionLink: link
      })
      if (res.code !== 0) {
        ElMessage.error('更新推广链接失败:', res.msg)
        return
      }

      form.value.promotionLink = link
    } catch (err) {
      ElMessage.error('获取推广链接失败:', err.message)
    }
  }

  // 更新用户追踪状态
  const handleUpdateTrackStatus = async (opt) => {
    try {
      const res = await updateUserTraceStatus({
        id: form.value.ID,
        commentTraceStatus: opt
      })
      if (res.code !== 0) {
        ElMessage.error('更新追踪状态失败:', res.msg)
        return
      }

      form.value.commentTraceStatus = opt
    } catch (err) {
      ElMessage.error('更新追踪状态失败:', err.message)
    }
  }
</script>
