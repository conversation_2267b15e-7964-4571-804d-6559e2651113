<template>
  <el-dialog
    :model-value="visible"
    title="绑定信息"
    width="800px"
    center
    draggable
    @close="handleClose"
    @update:model-value="updateVisible"
  >
    <div class="bind-info-container">
      <!-- 基础信息模块 -->
      <el-card shadow="hover" class="info-section mb-4">
        <template #header>
          <div class="section-header">
            <h4>基础信息</h4>
          </div>
        </template>
        <div class="info-content">
          <!-- 序号/WIFI信息 -->
          <div class="info-item-compact">
            <span class="info-label">序号/WIFI:</span>
            <span class="info-value">{{ localBindInfo.ipSort || '未知' }}</span>
          </div>

          <!-- 代理IP信息 -->
          <div class="info-item-compact">
            <span class="info-label">代理IP:</span>
            <div class="flex items-center gap-2 flex-1">
              <span class="ip-address">{{ localBindInfo.bindIP || '未绑定' }}</span>
              <el-tag
                v-if="localBindInfo.bindIP && localBindInfo.ipHealthStatus"
                :type="getIPHealthStatusType(localBindInfo.ipHealthStatus)"
                size="small"
              >
                {{ getIPHealthStatusText(localBindInfo.ipHealthStatus) }}
              </el-tag>
              <!-- 操作按钮直接放在IP旁边 -->
              <div class="flex gap-1 ml-auto">
                <el-button size="small" type="primary" @click="handleUpdateIP">
                  {{ localBindInfo.bindIP ? '更换' : '绑定' }}
                </el-button>
                <el-button v-if="localBindInfo.bindIP" size="small" type="success" @click="handleCheckIPHealth">
                  检查健康
                </el-button>
              </div>
            </div>
          </div>

          <!-- 设备信息 -->
          <div class="info-item-compact">
            <span class="info-label">设备ID (DID):</span>
            <span class="info-value">{{ localBindInfo.did || '未配置' }}</span>
          </div>
          <div class="info-item-compact">
            <span class="info-label">实例ID (IID):</span>
            <span class="info-value">{{ localBindInfo.iid || '未配置' }}</span>
          </div>
        </div>
      </el-card>

      <!-- 同IP用户列表 -->
      <el-card shadow="hover" class="info-section" v-if="localBindInfo.bindIP">
        <template #header>
          <div class="section-header">
            <h4>同IP用户列表</h4>
            <el-button size="small" type="primary" @click="handleRefreshSameIPUsers" :loading="sameIPUsersLoading">
              刷新
            </el-button>
          </div>
        </template>
        <div class="same-ip-users">
          <el-tabs v-model="activeUserTab">
            <el-tab-pane label="抖音用户" name="douyin">
              <el-table :data="sameIPUsers.dyUsers" border size="small" v-if="sameIPUsers.dyUsers.length > 0">
                <el-table-column label="昵称" min-width="100">
                  <template #default="scope">
                    <div class="flex flex-col">
                      <div class="font-medium">{{ scope.row.nickname }}</div>
                      <div class="text-sm text-gray-500">{{ scope.row.bindPhone || '未绑定' }}</div>
                      <div v-if="scope.row.bindPhone" class="text-xs text-gray-400">
                        {{ scope.row.phoneOperator }}-{{ scope.row.phoneUserName || '未实名' }}
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="uniqueId" label="抖音号" min-width="120" />
                <el-table-column label="备注" min-width="150">
                  <template #default="scope">
                    <div class="whitespace-pre-line text-sm">
                      {{ scope.row.remark || '无备注' }}
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <el-empty v-else description="暂无抖音用户" :image-size="80" />
            </el-tab-pane>
            <el-tab-pane label="火山版用户" name="flame">
              <el-table :data="sameIPUsers.flameUsers" border size="small" v-if="sameIPUsers.flameUsers.length > 0">
                <el-table-column label="昵称" min-width="100">
                  <template #default="scope">
                    <div class="flex flex-col">
                      <div class="font-medium">{{ scope.row.nickname }}</div>
                      <div class="text-sm text-gray-500">{{ scope.row.bindPhone || '未绑定' }}</div>
                      <div v-if="scope.row.bindPhone" class="text-xs text-gray-400">
                        {{ scope.row.phoneOperator }}-{{ scope.row.phoneUserName || '未实名' }}
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="isCollectEnabled" label="采集状态" min-width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.isCollectEnabled ? 'success' : 'info'" size="small">
                      {{ scope.row.isCollectEnabled ? '已开启' : '未开启' }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
              <el-empty v-else description="暂无火山版用户" :image-size="80" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </div>
  </el-dialog>

  <!-- 更新IP弹窗 -->
  <el-dialog
    :model-value="updateIPDialogVisible"
    title="更新绑定IP"
    width="400px"
    center
    draggable
    @update:model-value="updateIPDialogVisible = $event"
  >
    <el-form :model="updateIPForm" label-width="80px">
      <el-form-item label="用户">
        <span>{{ updateIPForm.nickname }}</span>
      </el-form-item>
      <el-form-item label="当前IP">
        <el-tag size="small" type="info" v-if="!updateIPForm.currentIP">未绑定</el-tag>
        <span v-else>{{ updateIPForm.currentIP }}</span>
      </el-form-item>
      <el-form-item label="新IP" prop="newIP">
        <div class="flex gap-2 items-start">
          <el-select
            v-model="updateIPForm.newIP"
            placeholder="请选择或搜索新的IP地址"
            class="flex-1"
            filterable
            clearable
          >
            <el-option v-for="ip in updateIPForm.availableIPs" :key="ip" :label="ip" :value="ip" />
          </el-select>
          <el-button type="warning" size="default" @click="clearIPInput" :disabled="!updateIPForm.newIP">
            清空
          </el-button>
        </div>
        <div class="text-gray-500 text-sm mt-1">
          {{ updateIPForm.newIP ? '请选择新的IP地址' : '清空IP后确定将解绑当前IP' }}
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateIPDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmUpdateIP">
        {{ updateIPForm.newIP ? '更新' : '解绑' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { getIPList, getIPBindings, getAvailableIP } from '@/api/douyin/ip'
  import { updateUserIP } from '@/api/douyin/dyUser'

  defineOptions({
    name: 'BindInfoDialog'
  })

  // Props
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    bindInfo: {
      type: Object,
      default: () => ({})
    }
  })

  // Emits
  const emit = defineEmits(['update:visible', 'update-ip', 'check-ip-health', 'refresh-data'])

  // 响应式数据
  const sameIPUsers = ref({
    dyUsers: [],
    flameUsers: []
  })
  const sameIPUsersLoading = ref(false)
  const activeUserTab = ref('douyin')

  // 本地绑定信息副本（用于实时更新显示）
  const localBindInfo = ref({})

  // 更新IP弹窗相关数据
  const updateIPDialogVisible = ref(false)
  const updateIPForm = ref({
    id: null,
    nickname: '',
    currentIP: '',
    newIP: '',
    availableIPs: []
  })

  // 监听绑定信息变化，更新本地副本
  watch(
    () => props.bindInfo,
    (newVal) => {
      localBindInfo.value = { ...newVal }
    },
    { immediate: true, deep: true }
  )

  // 监听IP变化，重新加载同IP用户列表
  watch(
    () => localBindInfo.value.bindIP,
    async (newIP, oldIP) => {
      // 只有在弹窗显示状态下且IP真正发生变化时才重新加载
      if (props.visible && newIP && newIP !== oldIP) {
        // 取消之前的请求
        if (loadingController) {
          loadingController.cancelled = true
        }

        // 重置数据
        sameIPUsers.value = { dyUsers: [], flameUsers: [] }

        // 创建新的控制器并加载数据
        loadingController = { cancelled: false }
        await loadIPInfoAndUsers(newIP, loadingController)
      }
    }
  )

  // 用于取消之前的请求
  let loadingController = null

  // 监听弹窗显示状态变化
  watch(
    () => props.visible,
    async (newVal) => {
      // 取消之前的请求
      if (loadingController) {
        loadingController.cancelled = true
      }

      if (newVal) {
        // 重置数据
        sameIPUsers.value = { dyUsers: [], flameUsers: [] }
        activeUserTab.value = 'douyin'

        if (localBindInfo.value.bindIP) {
          // 创建新的控制器
          loadingController = { cancelled: false }
          await loadIPInfoAndUsers(localBindInfo.value.bindIP, loadingController)
        }
      } else {
        // 弹窗关闭时清理数据
        sameIPUsers.value = { dyUsers: [], flameUsers: [] }
        if (loadingController) {
          loadingController.cancelled = true
          loadingController = null
        }
      }
    }
  )

  // 更新弹窗显示状态
  const updateVisible = (value) => {
    emit('update:visible', value)
  }

  // 关闭弹窗
  const handleClose = () => {
    emit('update:visible', false)
    // 数据清理在watch中处理
  }

  // 更换IP
  const handleUpdateIP = async () => {
    updateIPForm.value = {
      id: localBindInfo.value.ID,
      nickname: localBindInfo.value.nickname,
      currentIP: localBindInfo.value.bindIP || '',
      newIP: '',
      availableIPs: []
    }

    // 获取可用IP列表
    try {
      const res = await getAvailableIP()
      if (res.code === 0) {
        updateIPForm.value.availableIPs = res.data || []
        if (updateIPForm.value.availableIPs.length > 0) {
          updateIPForm.value.newIP = updateIPForm.value.availableIPs[0]
        }
      }
    } catch (err) {
      console.error('获取可用IP失败:', err)
      ElMessage.error('获取可用IP失败: ' + err.message)
    }

    updateIPDialogVisible.value = true
  }

  // 检查IP健康状态
  const handleCheckIPHealth = () => {
    emit('check-ip-health', localBindInfo.value)
  }

  // 刷新同IP用户列表
  const handleRefreshSameIPUsers = async () => {
    // 取消之前的加载
    if (loadingController) {
      loadingController.cancelled = true
    }
    // 创建新的控制器
    loadingController = { cancelled: false }
    await refreshSameIPUsers(null, loadingController)
  }

  // 加载IP信息和同IP用户列表
  const loadIPInfoAndUsers = async (ip, controller = null) => {
    try {
      // 检查是否已被取消
      if (controller?.cancelled) return

      // 首先获取IP列表来找到对应IP的序号和ID
      const ipListRes = await getIPList({ ip, pageSize: 1 })

      // 再次检查是否已被取消
      if (controller?.cancelled) return

      if (ipListRes.code === 0 && ipListRes.data.list.length > 0) {
        const ipInfo = ipListRes.data.list[0]

        // 更新本地绑定信息中的序号和ID
        localBindInfo.value.ipSort = ipInfo.sort || ipInfo.ID
        localBindInfo.value.ipId = ipInfo.ID

        // 获取同IP用户列表
        await refreshSameIPUsers(ipInfo.ID, controller)
      }
    } catch (err) {
      console.error('获取IP信息失败:', err)
    }
  }

  // 刷新同IP用户列表
  const refreshSameIPUsers = async (ipId = null, controller = null) => {
    const targetIpId = ipId || localBindInfo.value.ipId
    if (!targetIpId) return

    // 检查是否已被取消
    if (controller?.cancelled) return

    sameIPUsersLoading.value = true
    try {
      const res = await getIPBindings(targetIpId)

      // 再次检查是否已被取消
      if (controller?.cancelled) return

      if (res.code === 0) {
        sameIPUsers.value = {
          dyUsers: res.data.dyUsers || [],
          flameUsers: res.data.flameUsers || []
        }
      }
    } catch (err) {
      console.error('获取同IP用户失败:', err)
      // 只有在请求没有被取消时才显示错误消息
      if (!controller?.cancelled) {
        ElMessage.error('获取同IP用户失败')
      }
    } finally {
      sameIPUsersLoading.value = false
    }
  }

  // IP健康状态相关工具方法
  const getIPHealthStatusType = (status) => {
    switch (status) {
      case 'healthy':
        return 'success'
      case 'unhealthy':
        return 'danger'
      default:
        return 'info'
    }
  }

  const getIPHealthStatusText = (status) => {
    switch (status) {
      case 'healthy':
        return '健康'
      case 'unhealthy':
        return '不健康'
      default:
        return '未检查'
    }
  }

  // 清空IP输入
  const clearIPInput = () => {
    updateIPForm.value.newIP = ''
  }

  // 确认更新IP
  const confirmUpdateIP = async () => {
    try {
      const res = await updateUserIP({
        id: updateIPForm.value.id,
        bindIP: updateIPForm.value.newIP || '' // 允许传空字符串
      })

      if (res.code === 0) {
        const actionText = updateIPForm.value.newIP ? '更新IP' : '解绑IP'
        ElMessage.success(`${actionText}成功`)
        updateIPDialogVisible.value = false

        // 立即更新本地副本
        localBindInfo.value.bindIP = updateIPForm.value.newIP || ''
        if (!updateIPForm.value.newIP) {
          // 如果解绑了IP，清空相关信息
          localBindInfo.value.ipSort = null
          localBindInfo.value.ipId = null
          localBindInfo.value.ipHealthStatus = null
        }

        // 触发数据刷新
        emit('refresh-data')

        // 重新打开绑定信息弹窗
        emit('update:visible', true)
      }
    } catch (err) {
      console.error('操作失败:', err.message)
      ElMessage.error('操作失败：' + err.message)
    }
  }
</script>

<style scoped>
  /* 绑定信息弹窗样式 */
  .bind-info-container {
    padding: 16px;
    max-height: 75vh;
    overflow-y: auto;
  }

  .info-section {
    border-radius: 12px;
    transition: all 0.3s ease;
  }

  .info-section:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .section-header h4 {
    margin: 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }

  .info-content {
    padding: 16px;
  }

  .info-item-compact {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 6px 8px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #409eff;
  }

  .info-label {
    font-weight: 600;
    color: #606266;
    min-width: 120px;
    margin-right: 12px;
  }

  .info-value {
    color: #303133;
    font-weight: 500;
  }

  .ip-address {
    font-family: 'Monaco', 'Consolas', monospace;
    background: #f0f2f5;
    padding: 4px 8px;
    border-radius: 4px;
    color: #409eff;
    font-weight: 600;
  }

  .same-ip-users {
    padding: 16px 0;
  }

  .same-ip-users .el-table {
    border-radius: 8px;
    overflow: hidden;
  }

  .same-ip-users .el-table th {
    background: #f8f9fa;
    color: #606266;
    font-weight: 600;
  }

  .same-ip-users .el-empty {
    padding: 40px 20px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .bind-info-container {
      padding: 12px;
    }

    .info-item-compact {
      flex-direction: column;
      align-items: flex-start;
    }

    .info-label {
      min-width: auto;
      margin-bottom: 4px;
    }
  }
</style>
