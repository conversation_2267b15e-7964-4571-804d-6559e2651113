<template>
  <div
    class="w-full md:w-72 lg:w-80 bg-white text-slate-700 dark:text-slate-400 dark:bg-slate-900 rounded-lg shadow-sm p-4"
  >
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-medium">账号分类</h3>
    </div>
    <el-scrollbar style="height: calc(100vh - 320px)">
      <el-tree
        :data="categories"
        node-key="id"
        :props="defaultProps"
        @node-click="handleNodeClick"
        default-expand-all
        highlight-current
      >
        <template #default="{ data }">
          <div class="flex items-center justify-between w-full py-1">
            <div class="truncate" :class="selectedCategoryId === data.ID ? 'text-blue-500 font-bold' : ''">
              {{ data.name }}
            </div>
            <el-dropdown>
              <el-icon class="ml-3 cursor-pointer" v-if="data.ID > 0">
                <MoreFilled />
              </el-icon>
              <el-icon class="ml-3 cursor-pointer" v-else>
                <Plus />
              </el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="addCategoryFun(data)">添加分类</el-dropdown-item>
                  <el-dropdown-item @click="editCategory(data)" v-if="data.ID > 0">编辑分类</el-dropdown-item>
                  <el-dropdown-item @click="deleteCategoryFun(data.ID)" v-if="data.ID > 0">删除分类</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-tree>
    </el-scrollbar>

    <!-- 添加分类弹窗 -->
    <el-dialog
      v-model="categoryDialogVisible"
      @close="closeAddCategoryDialog"
      width="520"
      :title="(categoryFormData.ID === 0 ? '添加' : '编辑') + '分类'"
      draggable
    >
      <el-form ref="categoryForm" :rules="rules" :model="categoryFormData" label-width="80px">
        <el-form-item label="上级分类">
          <el-tree-select
            v-model="categoryFormData.pid"
            :data="categories"
            check-strictly
            :props="defaultProps"
            :render-after-expand="false"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="分类名称" prop="name">
          <el-input v-model.trim="categoryFormData.name" placeholder="分类名称"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="closeAddCategoryDialog">取消</el-button>
        <el-button type="primary" @click="confirmAddCategory">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import { MoreFilled, Plus } from '@element-plus/icons-vue'
  import { addCategory, deleteCategory, getCategoryList } from '@/api/dyUserCategory'

  defineOptions({
    name: 'CategoryTree'
  })

  // Props
  const { selectedCategoryId } = defineProps({
    selectedCategoryId: {
      type: Number,
      default: 0
    }
  })

  // Emits
  const emits = defineEmits(['node-click', 'category-updated'])

  // 响应式数据
  const categories = ref([])
  const categoryDialogVisible = ref(false)
  const categoryFormData = ref({
    ID: 0,
    pid: 0,
    name: ''
  })

  const categoryForm = ref(null)
  const rules = ref({
    name: [
      { required: true, message: '请输入分类名称', trigger: 'blur' },
      { max: 20, message: '最多20位字符', trigger: 'blur' }
    ]
  })

  const defaultProps = {
    children: 'children',
    label: 'name',
    value: 'ID'
  }

  // 方法
  const fetchCategories = async () => {
    const res = await getCategoryList()
    let data = {
      name: '全部分类',
      ID: 0,
      pid: 0,
      children: []
    }
    if (res.code === 0) {
      categories.value = res.data || []
      categories.value.unshift(data)
    }
  }

  const handleNodeClick = (node) => {
    emits('node-click', node)
  }

  const addCategoryFun = (category) => {
    categoryDialogVisible.value = true
    categoryFormData.value.ID = 0
    categoryFormData.value.pid = category.ID
  }

  const editCategory = (category) => {
    categoryFormData.value = {
      ID: category.ID,
      pid: category.pid,
      name: category.name
    }
    categoryDialogVisible.value = true
  }

  const deleteCategoryFun = async (id) => {
    const res = await deleteCategory({ id: id })
    if (res.code === 0) {
      ElMessage.success({ type: 'success', message: '删除成功' })
      await fetchCategories()
      emits('category-updated')
    }
  }

  const confirmAddCategory = async () => {
    categoryForm.value.validate(async (valid) => {
      if (valid) {
        const res = await addCategory(categoryFormData.value)
        if (res.code === 0) {
          ElMessage({ type: 'success', message: '操作成功' })
          await fetchCategories()
          closeAddCategoryDialog()
          emits('category-updated')
        }
      }
    })
  }

  const closeAddCategoryDialog = () => {
    categoryDialogVisible.value = false
    categoryFormData.value = {
      ID: 0,
      pid: 0,
      name: ''
    }
  }

  // 生命周期
  onMounted(() => {
    fetchCategories()
  })

  // 暴露方法和数据给父组件
  defineExpose({
    fetchCategories,
    categories
  })
</script>
