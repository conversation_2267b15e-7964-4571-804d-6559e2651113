<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline search-form-inline">
        <el-form-item label="抖音用户" v-if="!hideUserSelector">
          <el-select
            v-model="searchInfo.checkedDyUserIds"
            multiple
            collapse-tags
            collapse-tags-tooltip
            placeholder="请选择抖音用户"
            style="width: 350px"
          >
            <el-option-group v-for="group in dyUserGroups" :key="group.sysNickname" :label="group.sysNickname">
              <el-option v-for="user in group.dyUsers" :key="user.id" :label="user.nickname" :value="user.id" />
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
          <el-button type="success" @click="asyncAweme">同步作品</el-button>
        </el-form-item>
      </el-form>

      <div class="view-toggle-top">
        <el-radio-group v-model="viewMode" size="default">
          <el-radio-button label="table">表格视图</el-radio-button>
          <el-radio-button label="card">卡片视图</el-radio-button>
        </el-radio-group>
        <div class="batch-actions" v-if="viewMode === 'table'">
          <el-button type="danger" :disabled="selectedVideos.length === 0" @click="batchDispose(12)">
            批量删除 ({{ selectedVideos.length }})
          </el-button>
          <el-button type="warning" :disabled="selectedVideos.length === 0" @click="batchDispose(10)">
            批量隐藏 ({{ selectedVideos.length }})
          </el-button>
        </div>
      </div>
    </div>

    <!-- 卡片视图 -->
    <div class="video-card-view" v-if="viewMode === 'card'">
      <div class="batch-select-header" v-if="tableData.length > 0">
        <div class="left-section">
          <el-checkbox v-model="selectAll" :indeterminate="isIndeterminate" @change="handleSelectAllChange">
            全选当页
          </el-checkbox>
          <span class="select-count">已选择 {{ selectedVideos.length }} 项</span>
        </div>
        <div class="batch-actions-card">
          <el-button type="danger" :disabled="selectedVideos.length === 0" @click="batchDispose(12)">
            批量删除 ({{ selectedVideos.length }})
          </el-button>
          <el-button type="warning" :disabled="selectedVideos.length === 0" @click="batchDispose(10)">
            批量隐藏 ({{ selectedVideos.length }})
          </el-button>
        </div>
      </div>
      <el-row :gutter="20">
        <el-col
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
          v-for="(item, index) in tableData"
          :key="index"
          class="video-card-col"
        >
          <el-card shadow="hover" class="video-card" :class="{ selected: selectedVideos.includes(item.ID) }">
            <div class="card-select-box">
              <el-checkbox
                :model-value="selectedVideos.includes(item.ID)"
                @change="(checked) => handleCardSelectChange(item.ID, checked)"
              />
            </div>
            <div class="video-cover" @click="playVideo(item)">
              <el-image :src="item.cover" fit="contain">
                <template #error>
                  <div class="image-placeholder">
                    <el-icon><picture-filled /></el-icon>
                  </div>
                </template>
              </el-image>
              <div class="play-icon">
                <el-icon><video-play /></el-icon>
              </div>
            </div>
            <div class="video-info">
              <h3 class="video-title" :title="item.desc">{{ item.desc }}</h3>
              <div class="video-meta">
                <el-tag size="small" :type="getStatusType(item.private_status)" class="status-tag">
                  <span class="tag-content">
                    <span>{{ getStatusText(item.private_status) }}</span>
                  </span>
                </el-tag>
                <el-tag size="small" :type="item.in_reviewing === 1 ? 'info' : 'success'" class="source-tag">
                  {{ item.in_reviewing === 1 ? '审核中' : '已发布' }}
                </el-tag>
              </div>
              <div class="video-topics">
                <span
                  >播放 {{ item.play_count }} | 点赞 {{ item.digg_count }} | 评论 {{ item.comment_count }} | 分享
                  {{ item.share_count }}
                </span>
              </div>
              <div class="video-time">{{ formatTimestamp(item.createTime) }}</div>
              <div class="video-review-status">
                <el-tag size="small" type="info" class="review-status-tag">
                  <span class="tag-content">
                    <span>{{ convertStatusDesc(item.review_status_desc) }}</span>
                  </span>
                </el-tag>
              </div>
              <div class="video-review-status">
                <el-tag size="small" type="info" class="review-status-tag">
                  <span class="tag-content">
                    <span style="color: red">
                      {{ convertDisposeText(item.sys_dispose) }}
                    </span>
                  </span>
                </el-tag>
              </div>
              <div class="video-actions">
                <el-button type="primary" link @click="editVideo(item)">
                  <el-icon><edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="danger" link @click="deleteVideo(item)">
                  <el-icon><delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 表格视图 -->
    <div class="video-table-view" v-else>
      <el-table :data="tableData" style="width: 100%" v-loading="loading" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" :selectable="() => true" />
        <el-table-column prop="title" label="视频标题" min-width="180">
          <template #default="scope">
            <div class="table-title-cell">
              <el-image class="table-thumb" :src="scope.row.cover" fit="contain" @click="playVideo(scope.row)">
                <template #error>
                  <div class="image-placeholder">
                    <el-icon><picture-filled /></el-icon>
                  </div>
                </template>
              </el-image>
              <span class="table-title">{{ scope.row.desc }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="账号信息" width="120">
          <template #default="scope">
            <div class="flex items-center space-x-2">
              <div class="flex flex-col items-start overflow-hidden">
                <span class="font-medium truncate w-full"
                  >{{ scope.row.nickname }}
                  <el-tag v-if="scope.row.accountType === 2" size="mini" type="primary" class="ml-1">V</el-tag>
                </span>
                <span class="text-gray-500 text-sm truncate w-full">{{ scope.row.uniqueId }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="source" label="统计信息" width="100">
          <template #default="scope">
            <div class="flex items-center space-x-2">
              <div class="flex flex-col items-start overflow-hidden">
                <span class="text-gray-500 text-sm truncate w-full">播放 {{ scope.row.play_count }}</span>
                <span class="text-gray-500 text-sm truncate w-full">点赞 {{ scope.row.digg_count }}</span>
                <span class="text-gray-500 text-sm truncate w-full">评论 {{ scope.row.comment_count }}</span>
                <span class="text-gray-500 text-sm truncate w-full">转发 {{ scope.row.share_count }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="private_status" label="权限" width="100">
          <template #default="scope">
            <span class="text-green-500 text-sm truncate w-full" v-if="scope.row.private_status === 0">公开</span>
            <span class="text-orange-500 text-sm truncate w-full" v-if="scope.row.private_status === 1">私密</span>
            <span class="text-gray-500 text-sm truncate w-full" v-if="scope.row.private_status === 2">好友可见</span>
            <span class="text-red-500 text-sm truncate w-full" v-if="scope.row.private_status === 4">删除</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="发布时间" width="180">
          <template #default="scope">{{ formatTimestamp(scope.row.createTime) }}</template>
        </el-table-column>
        <el-table-column prop="createTime" label="同步时间" width="180">
          <template #default="scope">{{ formatTimestamp(scope.row.updatedAt) }}</template>
        </el-table-column>
        <el-table-column prop="in_reviewing" label="状态" width="140">
          <template #default="scope">
            <span class="text-gray-500 text-sm truncate w-full" v-if="scope.row.in_reviewing === 0">已发布</span>
            <span class="text-gray-500 text-sm truncate w-full" v-if="scope.row.in_reviewing === 1">审核中</span>
          </template>
        </el-table-column>
        <el-table-column prop="review_status_desc" label="异常提醒" width="140">
          <template #default="scope">
            <span>{{ convertStatusDesc(scope.row.review_status_desc) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="dispose" label="处理方式" width="180">
          <template #default="scope">
            <span v-if="scope.row.sys_dispose === 0">-</span>
            <span v-else>
              <div class="flex items-center space-x-2">
                <div class="flex flex-col items-start overflow-hidden">
                  <span class="text-red-500 font-medium truncate w-full">{{
                    convertDisposeText(scope.row.sys_dispose)
                  }}</span>
                  <span class="text-gray-500 text-sm truncate w-full">{{
                    formatTimestamp(scope.row.sys_dispose_time)
                  }}</span>
                </div>
              </div>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300">
          <template #default="scope">
            <el-button type="primary" link @click="playVideo(scope.row)">
              <el-icon><video-play /></el-icon>播放
            </el-button>
            <el-button type="primary" link @click="editVideo(scope.row)">
              <el-icon><edit /></el-icon>编辑
            </el-button>
            <el-button type="primary" link v-if="scope.row.status == 0" @click="handlePublishVideo(scope.row)"
              >立即发布</el-button
            >
            <el-button type="danger" link @click="deleteVideo(scope.row)">
              <el-icon><delete /></el-icon>删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100]"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 编辑作品对话框 -->
    <el-dialog v-model="uploadDialogVisible" title="编辑作品" width="800px" destroy-on-close>
      <el-form :model="form" label-width="80px" ref="uploadForm" :rules="formRules">
        <el-form-item label="作品标题" prop="desc">
          <el-input v-model="form.desc" placeholder="请输入视频标题" disabled />
        </el-form-item>
        <el-form-item label="作品权限" prop="private_status">
          <el-radio-group v-model="form.private_status">
            <el-radio :label="0">公开</el-radio>
            <el-radio :label="1">私密</el-radio>
            <el-radio :label="2">好友可见</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <div class="dialog-actions">
            <el-button @click="uploadDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitUpload">提交</el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 视频播放器对话框 -->
    <el-dialog
      v-model="playerDialogVisible"
      :fullscreen="true"
      :destroy-on-close="true"
      class="video-player-dialog"
      :show-close="false"
    >
      <div class="video-player-container">
        <div v-if="videoLoading" class="video-loading">
          <el-icon class="loading-icon">
            <loading />
          </el-icon>
          <div class="loading-text">视频加载中...</div>
        </div>
        <div class="video-wrapper">
          <video
            :src="currentVideoUrl"
            ref="videoPlayer"
            controls
            class="video-player"
            @loadstart="videoLoading = true"
            @canplay="videoLoading = false"
            @error="handleVideoError($event)"
          ></video>
        </div>

        <!-- 添加自定义控制栏 -->
        <div class="custom-controls" v-if="!videoLoading">
          <el-button type="primary" circle @click="toggleFullscreen">
            <el-icon><full-screen /></el-icon>
          </el-button>
          <el-button type="danger" circle @click="playerDialogVisible = false">
            <el-icon><close /></el-icon>
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 立即发布视频对话框 -->
    <el-dialog v-model="publishDialogVisible" title="立即发布" width="30%">
      <el-form :model="publishForm" label-width="100px">
        <el-form-item label="抖音号" v-model="publishForm.uniqueId" style="display: flex; align-items: center">
          <UniqueIdMatcher style="flex: 1" @select-user="onUserSelected" />
        </el-form-item>
        <!-- 视频标题 -->
        <el-form-item label="视频标题">
          <el-input v-model="publishForm.desc" disabled="" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closePublishDialog">取消</el-button>
          <el-button type="primary" @click="submitPublish">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, onMounted, watch, computed } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { formatTimestamp } from '@/utils/format'
  import { publishVideo } from '@/api/creative/video'
  import { getAwemeList, manageAweme, updateAweme, deleteAweme, batchDisposeAweme } from '@/api/douyin/aweme'
  import { getVideoCategoryList } from '@/api/creative/videoCategory'
  import { getProductUserList } from '@/api/douyin/product'
  import UniqueIdMatcher from '@/components/douyin/uniqueIdMatcher.vue'

  const props = defineProps({
    initialDyUserIds: {
      type: Array,
      default: () => null
    },
    hideUserSelector: {
      type: Boolean,
      default: false
    },
    dyUserId: {
      type: Number,
      default: null
    }
  })

  // 基础数据
  const tableData = ref([])
  const page = ref(1)
  const pageSize = ref(50)
  const total = ref(0)
  const loading = ref(false)
  const dyUserGroups = ref([])
  const searchInfo = ref({
    title: '',
    checkedDyUserIds: props.initialDyUserIds || (props.dyUserId ? [props.dyUserId] : null),
    dyUserIds: ''
  })

  // 分类选项
  const categoryOptions = ref([])

  // 视图模式
  const viewMode = ref(localStorage.getItem('videoViewMode') || 'table')

  // 选择相关
  const selectedVideos = ref([])
  const selectAll = ref(false)
  const isIndeterminate = computed(() => {
    return selectedVideos.value.length > 0 && selectedVideos.value.length < tableData.value.length
  })

  // 上传表单
  const uploadForm = ref(null)
  const uploadDialogVisible = ref(false)
  const coverUrl = ref('')
  const form = ref({
    id: null,
    desc: '',
    private_status: null
  })

  // 视频播放器
  const playerDialogVisible = ref(false)
  const currentVideoUrl = ref('')
  const currentVideoTitle = ref('')
  const videoPlayer = ref(null)
  const videoLoading = ref(false)
  const isFullscreen = ref(false)

  // 立即发布视频对话框
  const publishDialogVisible = ref(false)
  const publishForm = ref({
    uniqueId: null,
    videoId: null,
    desc: null
  })
  const currentPublishVideo = ref(null)
  const selectedUserInfo = ref(null)

  // 表单验证规则
  const formRules = {
    desc: [{ required: true, message: '请输入标题', trigger: 'blur' }],
    private_status: [{ required: true, message: '请选择权限级别', trigger: 'change' }]
  }

  // 监听视图模式变化
  watch(viewMode, (newMode) => {
    localStorage.setItem('videoViewMode', newMode)
  })

  // 监听选择状态变化，同步全选状态
  watch(
    [selectedVideos, tableData],
    ([selected, data]) => {
      if (data.length === 0) {
        selectAll.value = false
      } else {
        selectAll.value = selected.length === data.length
      }
    },
    { deep: true }
  )

  // 获取分类列表
  const getCategoryList = async () => {
    try {
      const res = await getVideoCategoryList({ page: 1, pageSize: 100 })
      if (res.code === 0) {
        categoryOptions.value = res.data.list
      }
    } catch (err) {
      console.error(err)
      ElMessage.error('获取分类列表失败')
    }
  }

  // 获取抖音用户列表
  const getDyUserList = async () => {
    try {
      const res = await getProductUserList()
      if (res.code === 0) {
        dyUserGroups.value = res.data.list
      }
    } catch (err) {
      console.error('获取抖音用户列表失败:', err)
      ElMessage.error('获取抖音用户列表失败')
    }
  }

  // 获取视频列表
  const getTableData = async () => {
    loading.value = true
    try {
      const params = {
        page: page.value,
        pageSize: pageSize.value
      }
      if (searchInfo.value.checkedDyUserIds && searchInfo.value.checkedDyUserIds.length > 0) {
        params.dyUserIds = searchInfo.value.checkedDyUserIds.join(',')
      }
      const res = await getAwemeList(params)
      if (res.code === 0) {
        tableData.value = res.data.list
        total.value = res.data.total
        page.value = res.data.page
        pageSize.value = res.data.pageSize
        // 清空选择状态
        selectedVideos.value = []
        selectAll.value = false
      }
    } catch (err) {
      console.error(err)
      ElMessage.error('获取视频列表失败')
    } finally {
      loading.value = false
    }
  }

  // 搜索
  const onSubmit = () => {
    page.value = 1
    getTableData()
  }

  // 同步作品
  const asyncAweme = async () => {
    try {
      const req = {}
      if (searchInfo.value.checkedDyUserIds && searchInfo.value.checkedDyUserIds.length > 0) {
        if (searchInfo.value.checkedDyUserIds.length > 1) {
          ElMessage.warning('最多只能选择1个用户')
          return
        }
        req.dyUserIds = searchInfo.value.checkedDyUserIds.join(',')
      } else {
        ElMessage.warning('请选择要同步的抖音用户')
        return
      }

      await ElMessageBox.confirm('确定要远程同步选中用户的作品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      ElMessage.warning('请稍等...')
      const res = await manageAweme(req)
      if (res.code === 0) {
        ElMessage.success(res.message || '同步成功')
        getTableData()
      }
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('同步失败，请稍后重试')
      }
    }
  }

  // 播放视频
  const playVideo = (row) => {
    console.log('视频地址:', row.playUrl)
    currentVideoUrl.value = row.playUrl
    currentVideoTitle.value = row.title
    playerDialogVisible.value = true

    // 监听弹窗关闭事件，停止视频播放
    watch(playerDialogVisible, (newVal) => {
      if (!newVal && videoPlayer.value) {
        videoPlayer.value.pause()
        videoPlayer.value.currentTime = 0
        isFullscreen.value = false
      }
    })

    // 等待DOM更新后设置视频尺寸
    setTimeout(() => {
      if (videoPlayer.value) {
        adjustVideoPlayerSize()
      }
    }, 300)
  }

  // 编辑视频
  const editVideo = (row) => {
    form.value = {
      id: row.ID,
      desc: row.desc,
      private_status: row.private_status
    }
    coverUrl.value = row.cover
    uploadDialogVisible.value = true
  }

  // 提交编辑
  const submitUpload = async () => {
    try {
      if (!form.value.id) {
        ElMessage.error('请选择要编辑的作品')
        return
      }
      if (!form.value.private_status && form.value.private_status != 0) {
        ElMessage.error('请选择编辑的权限')
        return
      }
      const res = await updateAweme({
        dyAwemeId: form.value.id,
        private_status: form.value.private_status
      })
      if (res.code === 0) {
        ElMessage.success('编辑成功')
        uploadDialogVisible.value = false
        getTableData()
      } else {
        ElMessage.error(res.message || '编辑失败')
        getTableData()
      }
    } catch (err) {
      console.error(err)
      ElMessage.error('视频更新失败')
    }
  }

  // 删除视频
  const deleteVideo = (row) => {
    ElMessageBox.confirm('确定要删除该视频吗？此操作不可恢复', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        const res = await deleteAweme({ dyAwemeId: row.ID })
        if (res.code === 0) {
          ElMessage.success('删除成功')
          if (tableData.value.length === 1 && page.value > 1) {
            page.value--
          }
          getTableData()
        } else {
          ElMessage.error(res.message || '删除失败')
        }
      } catch (err) {
        console.error(err)
        ElMessage.error('删除失败')
      }
    })
  }

  // 处理发布视频
  const handlePublishVideo = (item) => {
    console.log('处理发布视频:', item)
    currentPublishVideo.value = item
    publishDialogVisible.value = true
    publishForm.value.title = item.title
    publishForm.value.videoId = item.id
  }

  // 视频播放器尺寸调整
  const adjustVideoPlayerSize = () => {
    if (!videoPlayer.value) return

    const videoElement = videoPlayer.value
    const containerElement = videoElement.parentElement

    // 根据视频比例设置容器尺寸
    if (videoElement.videoWidth && videoElement.videoHeight) {
      const aspectRatio = videoElement.videoWidth / videoElement.videoHeight
      if (aspectRatio > 1) {
        // 横向视频
        containerElement.style.width = '90vw'
        containerElement.style.height = `${90 / aspectRatio}vw`
        containerElement.style.maxHeight = '80vh'
      } else {
        // 竖向视频
        containerElement.style.height = '80vh'
        containerElement.style.width = `${80 * aspectRatio}vh`
        containerElement.style.maxWidth = '90vw'
      }
    }
  }

  // 处理视频加载错误
  const handleVideoError = (error) => {
    videoLoading.value = false
    console.error('视频加载错误:', error)
    ElMessage.error('视频加载失败，请检查网络连接或视频地址')
  }

  // 切换全屏模式
  const toggleFullscreen = () => {
    if (!videoPlayer.value) return

    if (!document.fullscreenElement) {
      videoPlayer.value
        .requestFullscreen()
        .then(() => {
          isFullscreen.value = true
        })
        .catch((err) => {
          ElMessage.warning('无法进入全屏模式: ' + err.message)
        })
    } else {
      document.exitFullscreen()
      isFullscreen.value = false
    }
  }

  // 关闭发布对话框
  const closePublishDialog = () => {
    currentPublishVideo.value = null
    publishDialogVisible.value = false
    publishForm.value.uniqueId = null
    publishForm.value.videoId = null
    selectedUserInfo.value = null
  }

  // 提交发布
  const submitPublish = async () => {
    if (!publishForm.value.uniqueId) {
      ElMessage.warning('请选择发布的目标账号')
      return
    }

    const res = await publishVideo({
      uniqueId: selectedUserInfo.value.uniqueId,
      videoId: publishForm.value.videoId
    })
    if (res.code !== 0) {
      ElMessage.error(res.message || '发布失败')
      return
    }

    publishDialogVisible.value = false
    getTableData()
    ElMessage.success('发布请求已提交')
  }

  // 用户选择
  const onUserSelected = (user) => {
    selectedUserInfo.value = user
    publishForm.value.uniqueId = selectedUserInfo.value.uniqueId
  }

  // 批量处理函数
  const batchDispose = async (action) => {
    // 过滤掉空值和无效值
    const validSelectedVideos = selectedVideos.value.filter((id) => id != null && id !== '' && id !== undefined)

    if (validSelectedVideos.length === 0) {
      ElMessage.warning('请先选择要处理的作品')
      return
    }

    await ElMessageBox.confirm(
      `确定要${action === 10 ? '隐藏' : '删除'}选中的 ${validSelectedVideos.length} 个视频吗？`,
      '批量处理确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    try {
      const res = await batchDisposeAweme({
        dyAwemeIds: validSelectedVideos.join(','),
        action
      })
      if (res.code !== 0) {
        ElMessage.error(`批量${action === 10 ? '隐藏' : '删除'}失败`)
        return
      }

      ElMessage.success(`成功${action === 10 ? '隐藏' : '删除'} ${validSelectedVideos.length} 个视频`)
      selectedVideos.value = []

      if (tableData.value.length <= validSelectedVideos.length && page.value > 1) {
        page.value--
      }
      getTableData()
    } catch (error) {
      console.error(error)
      ElMessage.error(`批量${action === 10 ? '隐藏' : '删除'}失败`)
    }
  }

  // 选择相关方法
  const handleSelectionChange = (selectedRows) => {
    selectedVideos.value = selectedRows.map((row) => row.ID).filter((id) => id != null && id !== '' && id !== undefined)
  }

  // 分页相关
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }

  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }

  // 状态处理函数
  const getStatusType = (status) => {
    switch (status) {
      case 0:
        return 'success' // 公开
      case 1:
        return 'warning' // 私密
      case 2:
        return 'info' // 好友可见
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 0:
        return '公开'
      case 1:
        return '私密'
      case 2:
        return '好友可见'
      default:
        return '未知状态'
    }
  }

  // 全选相关方法
  const handleSelectAllChange = () => {
    if (selectAll.value) {
      selectedVideos.value = tableData.value
        .map((item) => item.ID)
        .filter((id) => id != null && id !== '' && id !== undefined)
    } else {
      selectedVideos.value = []
    }
  }

  const handleCardSelectChange = (itemId, checked) => {
    // 确保itemId是有效的
    if (itemId == null || itemId === '' || itemId === undefined) {
      return
    }

    if (checked) {
      if (!selectedVideos.value.includes(itemId)) {
        selectedVideos.value.push(itemId)
      }
    } else {
      selectedVideos.value = selectedVideos.value.filter((ID) => ID !== itemId)
    }
  }

  // 工具函数
  const convertStatusDesc = (arrStr) => {
    if (!arrStr) return '-'

    try {
      const arr = JSON.parse(arrStr)
      if (Array.isArray(arr) && arr.length) {
        return arr.join(' ')
      } else {
        return arrStr
      }
    } catch {
      return arrStr
    }
  }

  const convertDisposeText = (dispose) => {
    const disposeMap = {
      1: '公开[系统自动]',
      2: '私密[系统自动]',
      3: '好友可见[系统自动]',
      4: '删除[系统自动]',
      5: '公开[人工处理]',
      6: '私密[人工处理]',
      7: '好友可见[人工处理]',
      8: '删除[人工处理]',
      9: '公开[等待执行]',
      10: '私密[等待执行]',
      11: '好友可见[等待执行]',
      12: '删除[等待执行]'
    }
    return disposeMap[dispose] || `未知方式${dispose}`
  }

  // 初始化
  onMounted(() => {
    if (!props.hideUserSelector) {
      getDyUserList()
    }
    getCategoryList()
    getTableData()

    // 添加全屏变化监听
    document.addEventListener('fullscreenchange', () => {
      isFullscreen.value = !!document.fullscreenElement
    })
  })
</script>

<style scoped>
  .table-title-cell {
    display: flex;
    align-items: center;
  }

  .table-thumb {
    width: 60px;
    height: 40px;
    margin-right: 10px;
    border-radius: 4px;
    cursor: pointer;
    background-color: #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .table-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .image-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #f0f0f0;
    color: #909399;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    padding: 10px 0;
  }

  .view-toggle-top {
    margin-top: 10px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .batch-actions {
    display: flex;
    gap: 10px;
  }

  .search-form-inline {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
  }

  .search-form-inline .el-form-item {
    margin-right: 10px;
    flex-shrink: 0;
  }

  /* 卡片视图样式 */
  .video-card-view {
    margin-bottom: 20px;
  }

  .video-card-col {
    margin-bottom: 20px;
  }

  .video-card {
    position: relative;
    transition: all 0.3s;
  }

  .video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }

  .video-card.selected {
    border-color: var(--el-color-primary);
    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
  }

  .video-card.selected::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid var(--el-color-primary);
    border-radius: 6px;
    pointer-events: none;
  }

  .video-cover {
    position: relative;
    height: 160px;
    overflow: hidden;
    border-radius: 4px;
    cursor: pointer;
    background-color: #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .video-cover .el-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 48px;
    opacity: 0;
    transition: opacity 0.3s;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .video-cover:hover .play-icon {
    opacity: 1;
  }

  .video-info {
    padding: 10px 0;
  }

  .video-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .video-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }

  .source-tag {
    margin-left: 8px;
  }

  .status-tag {
    display: inline-flex;
    align-items: center;
  }

  .video-topics {
    margin: 8px 0;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  .video-time {
    font-size: 12px;
    color: #909399;
    margin-bottom: 8px;
  }

  .video-review-status {
    margin: 8px 0;
  }

  .review-status-tag {
    display: inline-flex;
    align-items: center;
  }

  .video-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 8px;
  }

  /* 标签内容样式 */
  .tag-content {
    display: flex;
    align-items: center;
    line-height: normal;
  }

  /* 批量选择样式 */
  .batch-select-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
  }

  .left-section {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .select-count {
    font-size: 14px;
    color: #606266;
  }

  .batch-actions-card {
    display: flex;
    gap: 10px;
  }

  .card-select-box {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 10;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    padding: 2px;
  }

  /* 对话框样式 */
  .dialog-footer {
    padding: 20px;
    text-align: right;
  }

  .dialog-actions {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }

  /* 视频播放器样式 */
  .video-player-container {
    width: 100%;
    height: 100vh;
    background-color: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
  }

  .video-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .video-player {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    background-color: #000;
    object-fit: contain;
    z-index: 1;
  }

  .video-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    z-index: 3;
  }

  .loading-icon {
    animation: rotating 2s linear infinite;
    margin-right: 4px;
    font-size: 28px;
  }

  .loading-text {
    margin-top: 15px;
    font-size: 18px;
  }

  .video-player-dialog :deep(.el-dialog__body) {
    padding: 0;
    background-color: #000;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
  }

  .video-player-dialog :deep(.el-dialog__header) {
    display: none;
  }

  .video-player-dialog :deep(.el-dialog__footer) {
    display: none;
  }

  /* 自定义控制栏样式 */
  .custom-controls {
    position: absolute;
    bottom: 30px;
    right: 30px;
    display: flex;
    gap: 15px;
    z-index: 2;
    opacity: 0;
    transition: opacity 0.3s;
  }

  .video-player-container:hover .custom-controls {
    opacity: 1;
  }

  /* 旋转动画 */
  @keyframes rotating {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .view-toggle-top {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }

    .batch-actions {
      margin-top: 10px;
    }
  }
</style>
