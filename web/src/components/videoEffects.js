// 辅助函数：获取分类标签
export const getCategoryLabel = (category) => {
  const categoryLabels = {
    // 普通特效
    basic: '基础',
    atmosphere: '氛围',
    dynamic: '动感',
    shadow: '光影',
    retro: '复古',
    dream: '梦幻',
    natural: '自然',
    split: '分屏',
    color: '彩色',
    deformation: '变形',

    // 高级特效
    'signal-jitter': '信号抖动特效',
    view: '视角类特效',
    'flow-light': '流光特效',
    'center-light': '中心光特效',
    word: '文字特效',
    particles: '粒子特效',
    'color-bubble': '彩色泡泡特效',
    weather: '天气与自然特效',
    'dynamic-shape': '动态形状类特效'
  }

  return categoryLabels[category] || category
}

// 辅助函数：处理图片路径，使其相对于 src/assets
const processImagePaths = (options) => {
  if (Array.isArray(options)) {
    options.forEach(option => {
      if (option.imageUrl) {
        // Check if imageUrl is already a full URL
        if (typeof option.imageUrl === 'string' && !option.imageUrl.startsWith('http://') && !option.imageUrl.startsWith('https://')) {
          option.imageUrl = new URL(`../assets/${option.imageUrl}`, import.meta.url).href;
        }
      }
      if (option.filterImages) {
        option.filterImages.forEach(img => {
          if (img.url) {
            // Check if img.url is already a full URL
            if (typeof img.url === 'string' && !img.url.startsWith('http://') && !img.url.startsWith('https://')) {
              img.url = new URL(`../assets/${img.url}`, import.meta.url).href;
            }
          }
        });
      }
      if (option.image) { // Handle flowerFontStyles image paths
        // Check if option.image is already a full URL
        if (typeof option.image === 'string' && !option.image.startsWith('http://') && !option.image.startsWith('https://')) {
          option.image = new URL(`../assets/${option.image}`, import.meta.url).href;
        }
      }
    });
  } else if (typeof options === 'object' && options !== null) {
    Object.values(options).forEach(value => processImagePaths(value));
  }
};


// 转场效果选项
const rawTransitionOptions = {
  // 普通转场
  normal: [
    { label: '时钟旋转', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/angular.webp', subType: 'angular' },
    { label: '向下弹跳', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/bounce_down.webp', subType: 'bounce_down' },
    { label: '向上弹跳', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/bounce_up.webp', subType: 'bounce_up' },
    { label: '水平领结', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/bowTieHorizontal.webp', subType: 'bowTieHorizontal' },
    { label: '垂直领结', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/bowTieVertical.webp', subType: 'bowTieVertical' },
    { label: '燃烧', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/burn.webp', subType: 'burn' },
    { label: '圆形', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/circle.webp', subType: 'circle' },
    { label: '圆形裁剪', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/circlecrop.webp', subType: 'circlecrop' },
    { label: '圆形打开', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/circleopen.webp', subType: 'circleopen' },
    { label: '颜色距离', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/colordistance.webp', subType: 'colordistance' },
    { label: '颜色相位', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/colorphase.webp', subType: 'colorphase' },
    { label: '太空波纹', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/crazyparametricfun.webp', subType: 'crazyparametricfun' },
    { label: '线性溶解', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/crosshatch.webp', subType: 'crosshatch' },
    { label: '交叉扭曲', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/crosswarp.webp', subType: 'crosswarp' },
    { label: '立方体', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/cube.webp', subType: 'cube' },
    { label: '对角切换', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/directional.webp', subType: 'directional' },
    { label: '扭曲旋转', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/directionalwarp.webp', subType: 'directionalwarp' },
    { label: '渐变擦除', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/directionalwipe.webp', subType: 'directionalwipe' },
    { label: '旋涡', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/displacement.webp', subType: 'displacement' },
    { label: '开幕', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/doorway.webp', subType: 'doorway' },
    { label: '齿状下落', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/doomscreentransition_down.webp', subType: 'doomscreentransition_down' },
    { label: '齿状上升', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/doomscreentransition_up.webp', subType: 'doomscreentransition_up' },
    { label: '梦幻', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/dreamy.webp', subType: 'dreamy' },
    { label: '炫境', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/dreamyzoom.webp', subType: 'dreamyzoom' },
    { label: '淡入淡出', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/fade.webp', subType: 'fade' },
    { label: '颜色淡入淡出', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/fadecolor.webp', subType: 'fadecolor' },
    { label: '灰度淡入淡出', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/fadegrayscale.webp', subType: 'fadegrayscale' },
    { label: '飞眼', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/flyeye.webp', subType: 'flyeye' },
    { label: '故障交替', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/glitchdisplace.webp', subType: 'glitchdisplace' },
    { label: '故障', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/glitchmemories.webp', subType: 'glitchmemories' },
    { label: '网格翻转', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/gridflip.webp', subType: 'gridflip' },
    { label: '心形', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/heart.webp', subType: 'heart' },
    { label: '蜂巢溶解', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/hexagonalize.webp', subType: 'hexagonalize' },
    { label: '万花筒', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/kaleidoscope.webp', subType: 'kaleidoscope' },
    { label: '线性模糊', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/linearblur.webp', subType: 'linearblur' },
    { label: '对角开幕', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/luma.webp', subType: 'luma' },
    { label: '雪花消除', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/morph.webp', subType: 'morph' },
    { label: '相册', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/mosaic.webp', subType: 'mosaic' },
    { label: '多层混合', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/multiplyblend.webp', subType: 'multiplyblend' },
    { label: '蔓延', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/perlin.webp', subType: 'perlin' },
    { label: '风车', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/pinwheel.webp', subType: 'pinwheel' },
    { label: '像素溶解', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/pixelize.webp', subType: 'pixelize' },
    { label: '花瓣遮罩', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/polarfunction.webp', subType: 'polarfunction' },
    { label: '波点', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/polka.webp', subType: 'polka' },
    { label: '圆形扫描', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/radial.webp', subType: 'radial' },
    { label: '随机方块', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/randomsquares.webp', subType: 'randomsquares' },
    { label: '波纹', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/ripple.webp', subType: 'ripple' },
    { label: '旋转缩放淡出', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/rotatescalefade.webp', subType: 'rotatescalefade' },
    { label: '放大消失', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/simplezoom.webp', subType: 'simplezoom' },
    { label: '向内推入', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/squeeze.webp', subType: 'squeeze' },
    { label: '方块替换', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/squareswire.webp', subType: 'squareswire' },
    { label: '切入', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/swap.webp', subType: 'swap' },
    { label: '中心旋转', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/swirl.webp', subType: 'swirl' },
    { label: '波形放大', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/undulatingburnout.webp', subType: 'undulatingburnout' },
    { label: '水滴', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/waterdrop.webp', subType: 'waterdrop' },
    { label: '线性擦除', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/wind.webp', subType: 'wind' },
    { label: '百叶窗', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/windowblinds.webp', subType: 'windowblinds' },
    { label: '栅格', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/windowslice.webp', subType: 'windowslice' },
    { label: '向下擦除', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/wipedown.webp', subType: 'wipedown' },
    { label: '向左擦除', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/wipeleft.webp', subType: 'wipeleft' },
    { label: '向右擦除', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/wiperight.webp', subType: 'wiperight' },
    { label: '向上擦除', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/wipeup.webp', subType: 'wipeup' },
    { label: '圆形放大', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/normal/zoomincircles.webp', subType: 'zoomincircles' }
  ],
  // 高级转场
  advanced: [
    { label: '运镜II', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_camera_move2.webp', subType: 'OT0001-atom_camera_move2' },
    { label: '下滑', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_down_glide.webp', subType: 'OT0001-atom_down_glide' },
    { label: '向下拖拽', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_drag_down.webp', subType: 'OT0001-atom_drag_down' },
    { label: '向上飞出', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_flying_up.webp', subType: 'OT0001-atom_flying_up' },
    { label: '横移模糊2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_horizontal_move_blur.webp', subType: 'OT0001-atom_horizontal_move_blur' },
    { label: '融解', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_melt_down.webp', subType: 'OT0001-atom_melt_down' },
    { label: '回忆拉屏1', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_memoirs_pull_screen1.webp', subType: 'OT0001-atom_memoirs_pull_screen1' },
    { label: '回忆拉屏2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_memoirs_pull_screen2.webp', subType: 'OT0001-atom_memoirs_pull_screen2' },
    { label: '回忆拉屏3', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_memoirs_pull_screen3.webp', subType: 'OT0001-atom_memoirs_pull_screen3' },
    { label: '横移模糊1', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_move_blur1.webp', subType: 'OT0001-atom_move_blur1' },
    { label: '推远II', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_push_away2.webp', subType: 'OT0001-atom_push_away2' },
    { label: '扇形', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_sector_shape.webp', subType: 'OT0001-atom_sector_shape' },
    { label: '镜头摇晃', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_shaking_camera.webp', subType: 'OT0001-atom_shaking_camera' },
    { label: '两侧分割', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_side_segmentation.webp', subType: 'OT0001-atom_side_segmentation' },
    { label: '空间旋转', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_space_rotation.webp', subType: 'OT0001-atom_space_rotation' },
    { label: '翻页', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_turn_page.webp', subType: 'OT0001-atom_turn_page' },
    { label: '无限穿越1', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_unlimited_wear1.webp', subType: 'OT0001-atom_unlimited_wear1' },
    { label: '无限穿越2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_unlimited_wear2.webp', subType: 'OT0001-atom_unlimited_wear2' },
    { label: '拉近', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_zoom_in.webp', subType: 'OT0001-atom_zoom_in' },
    { label: '拉远', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-atom_zoom_out.webp', subType: 'OT0001-atom_zoom_out' },
    { label: '眨眼', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-blink.webp', subType: 'OT0001-blink' },
    { label: '弹动羽化（照相机）', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-bounce_feather_camera.webp', subType: 'OT0001-bounce_feather_camera' },
    { label: '弹动闪光', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-bounce_flash.webp', subType: 'OT0001-bounce_flash' },
    { label: '中心径向模糊', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-center_radial_blur.webp', subType: 'OT0001-center_radial_blur' },
    { label: '颜色故障', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-color_glitch.webp', subType: 'OT0001-color_glitch' },
    { label: '颜色故障2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-color_glitch_2.webp', subType: 'OT0001-color_glitch_2' },
    { label: '颜色故障3', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-color_glitch_3.webp', subType: 'OT0001-color_glitch_3' },
    { label: '颜色向上', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-color_up.webp', subType: 'OT0001-color_up' },
    { label: '压缩', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-compression.webp', subType: 'OT0001-compression' },
    { label: '溶解', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-dissolve.webp', subType: 'OT0001-dissolve' },
    { label: '色散摇镜', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-dispersion_pan.webp', subType: 'OT0001-dispersion_pan' },
    { label: '扭曲溶解', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-distortion_dissolve.webp', subType: 'OT0001-distortion_dissolve' },
    { label: '扭曲遮罩', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-distortion_mask.webp', subType: 'OT0001-distortion_mask' },
    { label: '弹性缓出', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-elastic_ease_out.webp', subType: 'OT0001-elastic_ease_out' },
    { label: '曝光', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-exposure.webp', subType: 'OT0001-exposure' },
    { label: '羽化溶解', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-feather_dissolve.webp', subType: 'OT0001-feather_dissolve' },
    { label: '黑色闪光', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-flash_black.webp', subType: 'OT0001-flash_black' },
    { label: '白色闪光', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-flash_white.webp', subType: 'OT0001-flash_white' },
    { label: '闪回', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-flashback.webp', subType: 'OT0001-flashback' },
    { label: '热浪', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-heat_wave.webp', subType: 'OT0001-heat_wave' },
    { label: '水平模糊', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-horizontal_blur.webp', subType: 'OT0001-horizontal_blur' },
    { label: '水平切片', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-horizontal_slice.webp', subType: 'OT0001-horizontal_slice' },
    { label: '抖动', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-jitter.webp', subType: 'OT0001-jitter' },
    { label: '抖动2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-jitter_2.webp', subType: 'OT0001-jitter_2' },
    { label: '弹跳闪光', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-jump_flash.webp', subType: 'OT0001-jump_flash' },
    { label: '光线抖动', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-light_shake.webp', subType: 'OT0001-light_shake' },
    { label: '左下', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-lower_left.webp', subType: 'OT0001-lower_left' },
    { label: '左下径向模糊', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-lower_left_radial_blur.webp', subType: 'OT0001-lower_left_radial_blur' },
    { label: '混合旋转', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-mixed_spin.webp', subType: 'OT0001-mixed_spin' },
    { label: '平移', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-normal_pan.webp', subType: 'OT0001-normal_pan' },
    { label: '叠加', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-overlay.webp', subType: 'OT0001-overlay' },
    { label: '抖动旋转', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-shake_spin.webp', subType: 'OT0001-shake_spin' },
    { label: '旋转闪光', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-spin_flash.webp', subType: 'OT0001-spin_flash' },
    { label: '旋转发光', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-spin_glow.webp', subType: 'OT0001-spin_glow' },
    { label: '旋转切片', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-spin_slice.webp', subType: 'OT0001-spin_slice' },
    { label: '旋转拉伸', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-spin_stretch.webp', subType: 'OT0001-spin_stretch' },
    { label: '平面模糊', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-surface_blur.webp', subType: 'OT0001-surface_blur' },
    { label: '梯形', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-trapezoid.webp', subType: 'OT0001-trapezoid' },
    { label: '右上', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-upper_right.webp', subType: 'OT0001-upper_right' },
    { label: '右上径向模糊', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-upper_right_radial_blur.webp', subType: 'OT0001-upper_right_radial_blur' },
    { label: '垂直模糊', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-vertical_blur.webp', subType: 'OT0001-vertical_blur' },
    { label: '水滴', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/transition/advanced/OT0001-water_drop.webp', subType: 'OT0001-water_drop' }
  ]
}

// 滤镜效果选项
const rawFilterOptions = [
  {
    value: '90s-modern-film',
    label: '90年代现代胶片',
    imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/90s-modern-film/m1.webp',
    filterImages: [
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/90s-modern-film/m1.webp', label: '复古', subType: 'm1' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/90s-modern-film/m2.webp', label: '灰调', subType: 'm2' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/90s-modern-film/m3.webp', label: '青阶', subType: 'm3' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/90s-modern-film/m4.webp', label: '蓝调', subType: 'm4' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/90s-modern-film/m5.webp', label: '暗红', subType: 'm5' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/90s-modern-film/m6.webp', label: '暗淡', subType: 'm6' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/90s-modern-film/m7.webp', label: '灰橙', subType: 'm7' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/90s-modern-film/m8.webp', label: '通透', subType: 'm8' }
    ]
  },
  {
    value: 'film',
    label: '经典胶片',
    imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/film/pf1.webp',
    filterImages: [
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/film/pf1.webp', label: '高调', subType: 'pf1' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/film/pf2.webp', label: '富士', subType: 'pf2' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/film/pf3.webp', label: '暖色', subType: 'pf3' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/film/pf4.webp', label: '柯达', subType: 'pf4' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/film/pf5.webp', label: '复古', subType: 'pf5' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/film/pf6.webp', label: '反转', subType: 'pf6' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/film/pf7.webp', label: '红外', subType: 'pf7' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/film/pf8.webp', label: '宝丽来', subType: 'pf8' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/film/pf9.webp', label: '禄来', subType: 'pf9' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/film/pfa.webp', label: '工业', subType: 'pfa' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/film/pfb.webp', label: '灰阶', subType: 'pfb' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/film/pfc.webp', label: '白阶', subType: 'pfc' }
    ]
  },
  {
    value: 'infrared',
    label: '红外摄影',
    imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/infrared/pi1.webp',
    filterImages: [
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/infrared/pi1.webp', label: '清透', subType: 'pi1' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/infrared/pi2.webp', label: '暮晚', subType: 'pi2' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/infrared/pi3.webp', label: '秋色', subType: 'pi3' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/infrared/pi4.webp', label: '暗调', subType: 'pi4' }
    ]
  },
  {
    value: 'fresh',
    label: '泼辣·清新',
    imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/fresh/pl1.webp',
    filterImages: [
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/fresh/pl1.webp', label: '影调', subType: 'pl1' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/fresh/pl2.webp', label: '柔和', subType: 'pl2' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/fresh/pl3.webp', label: '春芽', subType: 'pl3' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/fresh/pl4.webp', label: '明媚', subType: 'pl4' }
    ]
  },
  {
    value: 'japanese-style',
    label: '日系风格',
    imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/japanese-style/pj1.webp',
    filterImages: [
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/japanese-style/pj1.webp', label: '小森林', subType: 'pj1' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/japanese-style/pj2.webp', label: '童年', subType: 'pj2' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/japanese-style/pj3.webp', label: '午后', subType: 'pj3' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/japanese-style/pj4.webp', label: '花雾', subType: 'pj4' }
    ]
  },
  {
    value: 'unsplash',
    label: 'Unsplash',
    imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/unsplash/delta.webp',
    filterImages: [
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/unsplash/delta.webp', label: '白桃', subType: 'delta' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/unsplash/electric.webp', label: '林间', subType: 'electric' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/unsplash/faded.webp', label: '盐系', subType: 'faded' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/unsplash/slowlived.webp', label: '蓝雾', subType: 'slowlived' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/unsplash/tokoyo.webp', label: '东京', subType: 'tokoyo' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/unsplash/urbex.webp', label: '雨后', subType: 'urbex' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/unsplash/warm.webp', label: '温暖', subType: 'warm' }
    ]
  },
  {
    value: '80s-negative',
    label: '80s负片',
    imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/80s-negative/f1.webp',
    filterImages: [
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/80s-negative/f1.webp', label: '济州岛', subType: 'f1' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/80s-negative/f2.webp', label: '雪山', subType: 'f2' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/80s-negative/f3.webp', label: '布达佩斯', subType: 'f3' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/80s-negative/f4.webp', label: '蓝霜', subType: 'f4' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/80s-negative/f5.webp', label: '尤加利', subType: 'f5' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/80s-negative/f6.webp', label: '老街', subType: 'f6' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/80s-negative/f7.webp', label: '咖啡', subType: 'f7' }
    ]
  },
  {
    value: 'travel',
    label: '旅行',
    imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/travel/pv1.webp',
    filterImages: [
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/travel/pv1.webp', label: '质感', subType: 'pv1' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/travel/pv2.webp', label: '天色', subType: 'pv2' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/travel/pv3.webp', label: '清新', subType: 'pv3' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/travel/pv4.webp', label: '雾气', subType: 'pv4' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/travel/pv5.webp', label: '高调', subType: 'pv5' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/travel/pv6.webp', label: '黑白', subType: 'pv6' }
    ]
  },
  {
    value: '90s-art-film',
    label: '90s艺术胶片',
    imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/90s-art-film/a1.webp',
    filterImages: [
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/90s-art-film/a1.webp', label: '柔和', subType: 'a1' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/90s-art-film/a2.webp', label: '暗调', subType: 'a2' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/90s-art-film/a3.webp', label: '青空', subType: 'a3' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/90s-art-film/a4.webp', label: '蓝光', subType: 'a4' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/90s-art-film/a5.webp', label: '艳丽', subType: 'a5' },
      { url: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/filter/90s-art-film/a6.webp', label: '哑光', subType: 'a6' }
    ]
  }
]

// 特效选项
const rawEffectOptions = {
  // 普通特效
  normal: {
    // 基础特效
    basic: [
      { label: '开幕', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/open.webp', subType: 'open' },
      { label: '闭幕', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/close.webp', subType: 'close' },
      { label: '横向模糊', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/h_blur.webp', subType: 'h_blur' },
      { label: '纵向模糊', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/v_blur.webp', subType: 'v_blur' },
      { label: '模糊', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/blur.webp', subType: 'blur' },
      { label: '轻微抖动', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/slightshake.webp', subType: 'slightshake' },
      { label: '镜头变焦', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/zoominout.webp', subType: 'zoominout' },
      { label: '电影感', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/movie.webp', subType: 'movie' },
      { label: '轻微放大', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/zoomslight.webp', subType: 'zoomslight' },
      { label: '色差', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/color_difference.webp', subType: 'color_difference' },
      { label: '聚光灯打开', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/withcircleopen.webp', subType: 'withcircleopen' },
      { label: '聚光灯关闭', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/withcircleclose.webp', subType: 'withcircleclose' },
      { label: '聚光灯抖动', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/withcircleshake.webp', subType: 'withcircleshake' },
      { label: '手电筒', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/withcircleflashlight.webp', subType: 'withcircleflashlight' },
      { label: '滑动消失', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/disappear.webp', subType: 'disappear' },
      { label: '震惊', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/shock.webp', subType: 'shock' },
      { label: '模糊开幕', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/bluropen.webp', subType: 'bluropen' },
      { label: '模糊闭幕', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/blurclose.webp', subType: 'blurclose' },
      { label: '咔嚓', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/photograph.webp', subType: 'photograph' },
      { label: '曝光降低', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/black.webp', subType: 'black' },
      { label: '渐变复古虚化', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/blurring.webp', subType: 'blurring' },
      { label: '彩色渐变黑白', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/color_to_grey.webp', subType: 'color_to_grey' },
      { label: '黑白渐变彩色', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/grey_to_color.webp', subType: 'grey_to_color' },
      { label: '方形开幕', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/slightrectshow.webp', subType: 'slightrectshow' },
      { label: '缓慢清晰开幕', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/slightshow.webp', subType: 'slightshow' },
      { label: '交叉开幕', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/wipecross.webp', subType: 'wipecross' },
      { label: '渐显开幕', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/whiteshow.webp', subType: 'whiteshow' },
      { label: '画中画', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/basic/image_in_image.webp', subType: 'image_in_image' }
    ],
    // 氛围特效
    atmosphere: [
      { label: '彩虹射线', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/atmosphere/colorfulradial.webp', subType: 'colorfulradial' },
      { label: '绚烂星空', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/atmosphere/colorfulstarry.webp', subType: 'colorfulstarry' },
      { label: '萤火', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/atmosphere/flyfire.webp', subType: 'flyfire' },
      { label: '爱心烟花', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/atmosphere/heartfireworks.webp', subType: 'heartfireworks' },
      { label: '流星雨', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/atmosphere/meteorshower.webp', subType: 'meteorshower' },
      { label: '星月童话', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/atmosphere/moons_and_stars.webp', subType: 'moons_and_stars' },
      { label: '星星冲屏', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/atmosphere/sparklestarfield.webp', subType: 'sparklestarfield' },
      { label: '光斑飘落', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/atmosphere/spotfall.webp', subType: 'spotfall' },
      { label: '星光绽放', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/atmosphere/starexplosion.webp', subType: 'starexplosion' },
      { label: '繁星点点', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/atmosphere/starry.webp', subType: 'starry' }
    ],
    // 动感特效
    dynamic: [
      { label: '闪白', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/white.webp', subType: 'white' },
      { label: '负片闪烁', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/minus_glitter.webp', subType: 'minus_glitter' },
      { label: '抖动', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/jitter.webp', subType: 'jitter' },
      { label: '灵魂出窍', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/soulout.webp', subType: 'soulout' },
      { label: '扫描条纹', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/scanlight.webp', subType: 'scanlight' },
      { label: '摇摆', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/swing.webp', subType: 'swing' },
      { label: '心跳', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/heartbeat.webp', subType: 'heartbeat' },
      { label: '闪屏', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/flashingscreen.webp', subType: 'flashingscreen' },
      { label: '幻觉', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/illusion.webp', subType: 'illusion' },
      { label: '视频分割', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/segmentation.webp', subType: 'segmentation' },
      { label: '霓虹灯', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/neolighting.webp', subType: 'neolighting' },
      { label: '卷动', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/curl.webp', subType: 'curl' },
      { label: '闪动', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/shine.webp', subType: 'shine' },
      { label: '毛刺', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/smalljitter.webp', subType: 'smalljitter' },
      { label: '闪光灯', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/flashinglight.webp', subType: 'flashinglight' },
      { label: '窗口过滤', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/windowblur.webp', subType: 'windowblur' },
      { label: '窗格', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/windowblur2.webp', subType: 'windowblur2' },
      { label: '万花筒', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dynamic/kaleidoscope.webp', subType: 'kaleidoscope' }
    ],
    // 光影特效
    shadow: [
      { label: '月亮投影', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/shadow/moon_projection.webp', subType: 'moon_projection' },
      { label: '星星投影', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/shadow/star_projection.webp', subType: 'star_projection' },
      { label: '爱心投影', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/shadow/heart_projection.webp', subType: 'heart_projection' },
      { label: '夕阳投影', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/shadow/sunset_projection.webp', subType: 'sunset_projection' },
      { label: '车窗投影', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/shadow/carwindow_projection.webp', subType: 'carwindow_projection' },
      { label: '闪烁的十字星', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/shadow/shinningstar_light.webp', subType: 'shinningstar_light' },
      { label: '天使光', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/shadow/anglelight.webp', subType: 'anglelight' },
      { label: '暗夜彩虹', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/shadow/darknight_rainbow.webp', subType: 'darknight_rainbow' },
      { label: '若隐若现', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/shadow/fallingcircle.webp', subType: 'fallingcircle' },
      { label: '中心光', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/shadow/lightcenter.webp', subType: 'lightcenter' },
      { label: '阳光经过', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/shadow/lightsweep.webp', subType: 'lightsweep' },
      { label: '月食', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/shadow/moon.webp', subType: 'moon' },
      { label: '荧幕照耀', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/shadow/rotationspotlight.webp', subType: 'rotationspotlight' }
    ],
    // 复古特效
    retro: [
      { label: '电视噪声', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/retro/blackwhitetv.webp', subType: 'blackwhitetv' },
      { label: '边界扫描', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/retro/edgescan.webp', subType: 'edgescan' },
      { label: '雪花故障', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/retro/oldtv.webp', subType: 'oldtv' },
      { label: '老电视闪烁', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/retro/oldtvshine.webp', subType: 'oldtvshine' },
      { label: '夜视仪', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/retro/nightvision.webp', subType: 'nightvision' },
      { label: 'TV show', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/retro/tvshow.webp', subType: 'tvshow' }
    ],
    // 梦幻特效
    dream: [
      { label: '彩色太阳', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dream/colorfulsun.webp', subType: 'colorfulsun' },
      { label: '大太阳', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dream/bigsun.webp', subType: 'bigsun' },
      { label: '心雨', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dream/fallingheart.webp', subType: 'fallingheart' },
      { label: '彩色烟花', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dream/colorfulfireworks.webp', subType: 'colorfulfireworks' },
      { label: '蹦爱心', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dream/heartshot.webp', subType: 'heartshot' },
      { label: '星星闪烁', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dream/starfieldshinee.webp', subType: 'starfieldshinee' },
      { label: '星星疯狂闪烁', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dream/starfieldshinee2.webp', subType: 'starfieldshinee2' },
      { label: '烟花爆炸', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dream/fireworks.webp', subType: 'fireworks' },
      { label: '爱心环绕', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dream/heartsurround.webp', subType: 'heartsurround' },
      { label: '爱心泡泡', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dream/risingheartbubble.webp', subType: 'risingheartbubble' },
      { label: '星河发射', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dream/starfield.webp', subType: 'starfield' },
      { label: '彩色涟漪', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dream/colorfulripples.webp', subType: 'colorfulripples' },
      { label: '彩色泡泡', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dream/colorfulbubbles.webp', subType: 'colorfulbubbles' },
      { label: '爱心闪烁', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dream/heartbubbleshinee.webp', subType: 'heartbubbleshinee' },
      { label: '星星花火', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/dream/starsparkle.webp', subType: 'starsparkle' }
    ],
    // 自然特效
    natural: [
      { label: '绵绵细雨', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/natural/rainy.webp', subType: 'rainy' },
      { label: '水波荡漾', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/natural/waterripple.webp', subType: 'waterripple' },
      { label: '下雪', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/natural/snow.webp', subType: 'snow' },
      { label: '起雾', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/natural/foggy.webp', subType: 'foggy' },
      { label: '下雨了', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/natural/meteor.webp', subType: 'meteor' },
      { label: '闪电', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/natural/stormlaser.webp', subType: 'stormlaser' },
      { label: '水中浸泡', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/natural/simpleripple.webp', subType: 'simpleripple' },
      { label: '黑色烟雾', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/natural/fadeshadow.webp', subType: 'fadeshadow' }
    ],
    // 分屏特效
    split: [
      { label: '跑马灯', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/split/marquee.webp', subType: 'marquee' },
      { label: '动态分屏', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/split/livesplit.webp', subType: 'livesplit' },
      { label: '二分屏', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/split/splitstill2.webp', subType: 'splitstill2' },
      { label: '三分屏', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/split/splitstill3.webp', subType: 'splitstill3' },
      { label: '四分屏', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/split/splitstill4.webp', subType: 'splitstill4' },
      { label: '九分屏', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/split/splitstill9.webp', subType: 'splitstill9' },
      { label: '六分屏', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/split/splitstill6.webp', subType: 'splitstill6' },
      { label: '黑白三格', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/split/blackwhitesplit.webp', subType: 'blackwhitesplit' },
      { label: '模糊分屏', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/split/blurthreesplit.webp', subType: 'blurthreesplit' }
    ],
    // 色彩特效
    color: [
      { label: '炫彩', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/color/colorful.webp', subType: 'colorful' },
      { label: '颜色吞噬', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/color/blackfade.webp', subType: 'blackfade' },
      { label: '彩虹过滤色', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/color/rainbowfilter.webp', subType: 'rainbowfilter' },
      { label: '移动彩虹色', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/color/movingrainbow.webp', subType: 'movingrainbow' },
      { label: 'disco', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/color/discolights.webp', subType: 'discolights' }
    ],
    // 变形特效
    deformation: [
      { label: '鱼眼', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/deformation/fisheye.webp', subType: 'fisheye' },
      { label: '马赛克', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/deformation/mosaic_rect.webp', subType: 'mosaic_rect' },
      { label: '毛玻璃', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/deformation/glass.webp', subType: 'glass' },
      { label: '全景图', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/normal/deformation/planet.webp', subType: 'planet' }
    ]
  },
  // 高级特效
  advanced: {
    // 信号抖动特效
    'signal-jitter': [
      { label: '信号抖动1', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/signal-jitter/OV0001-bad_singal_1.webp', subType: 'OV0001-bad_singal_1' },
      { label: '信号抖动2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/signal-jitter/OV0001-bad_singal_2.webp', subType: 'OV0001-bad_singal_2' },
      { label: '信号抖动3', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/signal-jitter/OV0001-bad_singal_3.webp', subType: 'OV0001-bad_singal_3' },
      { label: '信号抖动4', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/signal-jitter/OV0001-bad_singal_4.webp', subType: 'OV0001-bad_singal_4' },
      { label: '信号抖动5', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/signal-jitter/OV0001-bad_singal_5.webp', subType: 'OV0001-bad_singal_5' },
      { label: '信号抖动6', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/signal-jitter/OV0001-singal_glitch_1.webp', subType: 'OV0001-singal_glitch_1' },
      { label: '信号抖动7', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/signal-jitter/OV0001-singal_glitch_2.webp', subType: 'OV0001-singal_glitch_2' },
    ],
    // 视觉类特效
    view: [
      { label: '摄像机视角', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/view/OV0001-camera_viewer.webp', subType: 'OV0001-camera_viewer' },
      { label: '竖屏模式', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/view/OV0001-vertical_video.webp', subType: 'OV0001-vertical_video' },
      { label: '横屏模式', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/view/OV0001-vertical_video2.webp', subType: 'OV0001-vertical_video2' },
      { label: '复古电视1', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/view/OV0001-tv_crt_1.webp', subType: 'OV0001-tv_crt_1' },
      { label: '复古电视2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/view/OV0001-tv_crt_2.webp', subType: 'OV0001-tv_crt_2' },
      { label: '复古电视3', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/view/OV0001-tv_crt_3.webp', subType: 'OV0001-tv_crt_3' },
      { label: '复古电视4', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/view/OV0001-tv_crt_4.webp', subType: 'OV0001-tv_crt_4' },
      { label: '老式黑白电视', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/view/OV0001-tv_crt_old_1.webp', subType: 'OV0001-tv_crt_old_1' }
    ],
    // 流光特效
    'flow-light': [
      { label: '流光1', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/flow-light/OV0001-flow_move_light_1.webp', subType: 'OV0001-flow_move_light_1' },
      { label: '流光2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/flow-light/OV0001-flow_move_light_2.webp', subType: 'OV0001-flow_move_light_2' },
      { label: '流光3', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/flow-light/OV0001-flow_move_light_3.webp', subType: 'OV0001-flow_move_light_3' },
      { label: '流光4', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/flow-light/OV0001-flow_move_light_4.webp', subType: 'OV0001-flow_move_light_4' },
      { label: '流光5', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/flow-light/OV0001-flow_move_light_5.webp', subType: 'OV0001-flow_move_light_5' },
      { label: '流光6', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/flow-light/OV0001-flow_move_light_6.webp', subType: 'OV0001-flow_move_light_6' },
      { label: '流光7', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/flow-light/OV0001-flow_move_light_7.webp', subType: 'OV0001-flow_move_light_7' },
      { label: '流光8', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/flow-light/OV0001-flow_move_light_8.webp', subType: 'OV0001-flow_move_light_8' },
      { label: '流光9', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/flow-light/OV0001-flow_move_light_9.webp', subType: 'OV0001-flow_move_light_9' },
      { label: '流光10', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/flow-light/OV0001-flow_move_light_10.webp', subType: 'OV0001-flow_move_light_10' },
      { label: '流光11', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/flow-light/OV0001-flow_move_light_11.webp', subType: 'OV0001-flow_move_light_11' },
      { label: '流光12', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/flow-light/OV0001-flow_move_light_12.webp', subType: 'OV0001-flow_move_light_12' },
      { label: '流光13', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/flow-light/OV0001-flow_move_light_13.webp', subType: 'OV0001-flow_move_light_13' }
    ],
    // 中心光特效
    'center-light': [
      { label: '中心光1', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/center-light/OV0001-center_light_1.webp', subType: 'OV0001-center_light_1' },
      { label: '中心光2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/center-light/OV0001-center_light_2.webp', subType: 'OV0001-center_light_2' },
      { label: '中心光3', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/center-light/OV0001-center_light_3.webp', subType: 'OV0001-center_light_3' },
      { label: '中心光4', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/center-light/OV0001-center_light_4.webp', subType: 'OV0001-center_light_4' },
      { label: '中心光5', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/center-light/OV0001-center_light_5.webp', subType: 'OV0001-center_light_5' },
      { label: '中心光6', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/center-light/OV0001-center_light_6.webp', subType: 'OV0001-center_light_6' },
      { label: '中心光7', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/center-light/OV0001-center_light_7.webp', subType: 'OV0001-center_light_7' },
      { label: '中心光8', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/center-light/OV0001-center_light_8.webp', subType: 'OV0001-center_light_8' },
      { label: '中心光9', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/center-light/OV0001-center_light_9.webp', subType: 'OV0001-center_light_9' }
    ],
    // 文字特效
    word: [
      { label: '流动文字1', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/word/OV0001-hacker_world_1.webp', subType: 'OV0001-hacker_world_1' },
      { label: '流动文字2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/word/OV0001-hacker_world_2.webp', subType: 'OV0001-hacker_world_2' },
      { label: '流动文字3', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/word/OV0001-hacker_world_3.webp', subType: 'OV0001-hacker_world_3' }
    ],
    // 粒子特效
    particles: [
      { label: '粒子1', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/particles/OV0001-particles_1.webp', subType: 'OV0001-particles_1' },
      { label: '粒子2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/particles/OV0001-particles_2.webp', subType: 'OV0001-particles_2' },
      { label: '粒子3', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/particles/OV0001-particles_3.webp', subType: 'OV0001-particles_3' }
    ],
    // 彩色泡泡特效
    'color-bubble': [
      { label: '色彩泡泡1', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/color-bubble/OV0001-pop_up_color_1.webp', subType: 'OV0001-pop_up_color_1' },
      { label: '色彩泡泡2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/color-bubble/OV0001-pop_up_color_2.webp', subType: 'OV0001-pop_up_color_2' },
      { label: '色彩泡泡3', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/color-bubble/OV0001-pop_up_color_3.webp', subType: 'OV0001-pop_up_color_3' },
      { label: '色彩泡泡4', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/color-bubble/OV0001-pop_up_color_4.webp', subType: 'OV0001-pop_up_color_4' },
      { label: '色彩泡泡5', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/color-bubble/OV0001-pop_up_color_5.webp', subType: 'OV0001-pop_up_color_5' },
      { label: '色彩泡泡6', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/color-bubble/OV0001-pop_up_color_6.webp', subType: 'OV0001-pop_up_color_6' },
      { label: '色彩泡泡7', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/color-bubble/OV0001-pop_up_color_7.webp', subType: 'OV0001-pop_up_color_7' },
      { label: '色彩泡泡8', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/color-bubble/OV0001-pop_up_color_8.webp', subType: 'OV0001-pop_up_color_8' }
    ],
    // 天气与自然特效
    weather: [
      { label: '雨雪1', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/weather/OV0001-rain_1.webp', subType: 'OV0001-rain_1' },
      { label: '雨雪2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/weather/OV0001-rain_2.webp', subType: 'OV0001-rain_2' },
      { label: '雨雪3', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/weather/OV0001-snow_1.webp', subType: 'OV0001-snow_1' },
      { label: '雨雪4', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/weather/OV0001-snow_2.webp', subType: 'OV0001-snow_2' },
      { label: '雨雪5', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/weather/OV0001-snow_3.webp', subType: 'OV0001-snow_3' },
      { label: '雨雪6', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/weather/OV0001-snow_4.webp', subType: 'OV0001-snow_4' },
      { label: '下雨窗外1', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/weather/OV0001-rain_glass_2.webp', subType: 'OV0001-rain_glass_2' },
      { label: '下雨窗外2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/weather/OV0001-rain_glass_3.webp', subType: 'OV0001-rain_glass_3' },
      { label: '下雨窗外3', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/weather/OV0001-rain_glass_4.webp', subType: 'OV0001-rain_glass_4' },
      { label: '下雨窗外4', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/weather/OV0001-rain_glass_5.webp', subType: 'OV0001-rain_glass_5' },
      { label: '倒影', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/weather/OV0001-reflection_water.webp', subType: 'OV0001-reflection_water' }
    ],
    // 动态形状类特效
    'dynamic-shape': [
      { label: '波浪1', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/dynamic-shape/OV0001-wave_1.webp', subType: 'OV0001-wave_1' },
      { label: '波浪2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/dynamic-shape/OV0001-wave_2.webp', subType: 'OV0001-wave_2' },
      { label: '波浪3', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/dynamic-shape/OV0001-wave_3.webp', subType: 'OV0001-wave_3' },
      { label: '格子1', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/dynamic-shape/OV0001-box_1.webp', subType: 'OV0001-box_1' },
      { label: '格子2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/dynamic-shape/OV0001-box_2.webp', subType: 'OV0001-box_2' },
      { label: '燃烧1', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/dynamic-shape/OV0001-blur_1.webp', subType: 'OV0001-blur_1' },
      { label: '燃烧2', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/dynamic-shape/OV0001-bottom_burn.webp', subType: 'OV0001-bottom_burn' },
      { label: '燃烧3', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/dynamic-shape/OV0001-bottom_burn2.webp', subType: 'OV0001-bottom_burn2' },
      { label: '烟雾', imageUrl: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/vfx/advanced/dynamic-shape/OV0001-smog_1.webp', subType: 'OV0001-smog_1' }
    ]
  }
}

// 处理路径并导出
processImagePaths(rawTransitionOptions);
processImagePaths(rawFilterOptions);
processImagePaths(rawEffectOptions);

export const transitionOptions = rawTransitionOptions;
export const filterOptions = rawFilterOptions;
export const effectOptions = rawEffectOptions;

// 根据环境决定字体URL前缀
const getFontUrlPrefix = () => {
  // 开发环境使用代理，生产环境直接使用OSS
  return import.meta.env.DEV
    ? '/fonts'
    : 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/font';
};

// 将字体URL转换为完整的OSS URL（用于传参给后端）
export const convertToFullFontUrl = (fontUrl) => {
  if (!fontUrl) return '';

  // 如果已经是完整URL，直接返回
  if (fontUrl.startsWith('http')) {
    return fontUrl;
  }

  // 如果是相对路径，转换为完整的OSS URL
  if (fontUrl.startsWith('/fonts/')) {
    return fontUrl.replace('/fonts/', 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/font/');
  }

  return fontUrl;
};

// 字体列表
export const fontList = [
  {
    label: '阿里巴巴普惠体',
    value: `${getFontUrlPrefix()}/AlibabaPuHuiTi-3-85-Bold.ttf`,
    fontFamily: 'AlibabaPuHuiTi'
  },
  {
    label: '阿里妈妈数黑体',
    value: `${getFontUrlPrefix()}/AlimamaShuHeiTi-Bold.ttf`,
    fontFamily: 'AlimamaShuHeiTi'
  },
  {
    label: '阿里妈妈东方大楷',
    value: `${getFontUrlPrefix()}/Alimama_DongFangDaKai_Regular.ttf`,
    fontFamily: 'AlimamaDongFangDaKai'
  },
  {
    label: '钉钉进步体',
    value: `${getFontUrlPrefix()}/DingTalkJinBuTi-Regular.ttf`,
    fontFamily: 'DingTalkJinBuTi'
  },
  {
    label: '阿里妈妈方圆体',
    value: `${getFontUrlPrefix()}/AlimamaFangYuanTiVF-Thin.ttf`,
    fontFamily: 'AlimamaFangYuanTi'
  },
  {
    label: '淘宝买菜体',
    value: `${getFontUrlPrefix()}/TaoBaoMaiCaiTi-Regular.ttf`,
    fontFamily: 'TaoBaoMaiCaiTi'
  },
  {
    label: '阿里妈妈刀隶体',
    value: `${getFontUrlPrefix()}/AlimamaDaoLiTi-Regular.ttf`,
    fontFamily: 'AlimamaDaoLiTi'
  },
  {
    label: 'WD-XL滑油字',
    value: `${getFontUrlPrefix()}/WDXLLubrifontSC-Regular.ttf`,
    fontFamily: 'WDXLLubrifont'
  },
  {
    label: '思源宋体',
    value: `${getFontUrlPrefix()}/SourceHanSerifSC-VF.ttf`,
    fontFamily: 'SourceHanSerifSC'
  },
  {
    label: '思源黑体',
    value: `${getFontUrlPrefix()}/NotoSansSC-VariableFont_wght.ttf`,
    fontFamily: 'NotoSansSC'
  },
  {
    label: '马善政体',
    value: `${getFontUrlPrefix()}/MaShanZheng-Regular.ttf`,
    fontFamily: 'MaShanZheng'
  },
  {
    label: '站酷快乐体',
    value: `${getFontUrlPrefix()}/ZCOOLKuaiLe-Regular.ttf`,
    fontFamily: 'ZCOOLKuaiLe'
  },
  {
    label: '芝麻行体',
    value: `${getFontUrlPrefix()}/ZhiMangXing-Regular.ttf`,
    fontFamily: 'ZhiMangXing'
  },
  {
    label: '柳建毛草体',
    value: `${getFontUrlPrefix()}/LiuJianMaoCao-Regular.ttf`,
    fontFamily: 'LiuJianMaoCao'
  },
  {
    label: '龙藏书法体',
    value: `${getFontUrlPrefix()}/LongCang-Regular.ttf`,
    fontFamily: 'LongCang'
  },
  {
    label: '上首江湖书法体',
    value: `${getFontUrlPrefix()}/ShangShouJiangHuShuFaTi-2.ttf`,
    fontFamily: 'ShangShouJiangHu'
  },
  {
    label: '文泉驿等宽微米黑',
    value: `${getFontUrlPrefix()}/WenQuanDengKuanWeiMiHei-1.ttf`,
    fontFamily: 'WenQuanDengKuan'
  },
  {
    label: '文鼎PL简中楷',
    value: `${getFontUrlPrefix()}/WenDingPLJianZhongKai.ttf`,
    fontFamily: 'WenDingPL'
  },
  {
    label: '电影字幕体',
    value: `${getFontUrlPrefix()}/dianyingzimuti-2.ttf`,
    fontFamily: 'DianyingZimu'
  },
  {
    label: '仓耳非白W04',
    value: `${getFontUrlPrefix()}/CangErFeiBaiW04-2.ttf`,
    fontFamily: 'CangErFeiBai'
  },
  {
    label: '仓耳舒圆体W03',
    value: `${getFontUrlPrefix()}/CangErShuYuanTiW03-2.ttf`,
    fontFamily: 'CangErShuYuan'
  },
  {
    label: '仓耳渔阳体W03',
    value: `${getFontUrlPrefix()}/CangErYuYangTiW03-2.ttf`,
    fontFamily: 'CangErYuYang'
  },
  {
    label: '仓耳与墨W02',
    value: `${getFontUrlPrefix()}/CangErYuMoW02-2.ttf`,
    fontFamily: 'CangErYuMo'
  },
  {
    label: '荆南波波黑',
    value: `${getFontUrlPrefix()}/JingNanBoBoHei-Bold-2.ttf`,
    fontFamily: 'JingNanBoBo'
  },
  {
    label: '方正仿宋简体',
    value: `${getFontUrlPrefix()}/FangZhengFangSongJianTi-1.ttf`,
    fontFamily: 'FangZhengFangSong'
  },
  {
    label: '方正黑体简体',
    value: `${getFontUrlPrefix()}/FangZhengHeiTiJianTi-1.ttf`,
    fontFamily: 'FangZhengHeiTi'
  },
  {
    label: '方正楷体简体',
    value: `${getFontUrlPrefix()}/FangZhengKaiTiJianTi-1.ttf`,
    fontFamily: 'FangZhengKaiTi'
  },
  {
    label: '方正书宋简体',
    value: `${getFontUrlPrefix()}/FangZhengShuSongJianTi-1.ttf`,
    fontFamily: 'FangZhengShuSong'
  }
];

// 简化的字体加载函数 - 只检查字体是否可用
export const checkFontAvailability = (fontFamily) => {
  // 使用Canvas API检测字体是否已加载
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');

  // 设置基准字体
  context.font = '72px monospace';
  const baselineWidth = context.measureText('测试字体ABCabc123').width;

  // 设置目标字体
  context.font = `72px '${fontFamily}', monospace`;
  const targetWidth = context.measureText('测试字体ABCabc123').width;

  // 如果宽度不同，说明字体已加载
  return baselineWidth !== targetWidth;
};

// 等待字体加载完成
export const waitForFont = (fontFamily, timeout = 5000) => {
  return new Promise((resolve) => {
    const startTime = Date.now();

    const checkFont = () => {
      if (checkFontAvailability(fontFamily)) {
        resolve(true);
      } else if (Date.now() - startTime > timeout) {
        console.warn(`⚠️ 字体加载超时: ${fontFamily}`);
        resolve(false);
      } else {
        setTimeout(checkFont, 100);
      }
    };

    checkFont();
  });
};

// 预加载所有字体 - 改为等待CSS字体加载
export const preloadAllFonts = () => {
  console.log('开始等待字体加载...');

  // 给CSS一些时间来开始加载字体
  return new Promise((resolve) => {
    setTimeout(() => {
      const fontPromises = fontList.map(font =>
        waitForFont(font.fontFamily, 3000)
      );

      Promise.allSettled(fontPromises).then((results) => {
        const successful = results.filter(result => result.status === 'fulfilled' && result.value).length;
        const failed = results.filter(result => result.status === 'rejected' || !result.value).length;
        console.log(`字体加载检查完成: 成功 ${successful}, 失败 ${failed}`);
        resolve(results);
      });
    }, 1000); // 给CSS 1秒时间开始加载
  });
};

// 生成CSS字体样式
export const generateFontCSS = () => {
  const fontFaces = fontList.map(font => `
    @font-face {
      font-family: '${font.fontFamily}';
      src: url('${font.value}') format('truetype');
      font-display: swap;
    }
    .font-option-${font.fontFamily} {
      font-family: '${font.fontFamily}', sans-serif !important;
    }
  `).join('\n');

  return fontFaces;
};

// 注入字体CSS到页面
export const injectFontCSS = () => {
  const existingStyle = document.getElementById('custom-fonts');
  if (existingStyle) {
    console.log('字体CSS已存在，跳过注入');
    return;
  }

  const cssContent = generateFontCSS();
  const style = document.createElement('style');
  style.id = 'custom-fonts';
  style.textContent = cssContent;
  document.head.appendChild(style);

  console.log('✅ 字体CSS已注入到页面头部');
  console.log('注入的CSS内容长度:', cssContent.length);

  // 调试：输出前几个字体的CSS
  const lines = cssContent.split('\n').slice(0, 10);
  console.log('CSS前几行:', lines);
};

// 花字样式选项
const rawFlowerFontStyles = [
  // CS0001 系列
  { label: 'CS0001-000001', value: 'CS0001-000001', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0001-000001.png' },
  { label: 'CS0001-000002', value: 'CS0001-000002', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0001-000002.png' },
  { label: 'CS0001-000003', value: 'CS0001-000003', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0001-000003.png' },
  { label: 'CS0001-000004', value: 'CS0001-000004', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0001-000004.png' },
  { label: 'CS0001-000005', value: 'CS0001-000005', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0001-000005.png' },
  { label: 'CS0001-000006', value: 'CS0001-000006', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0001-000006.png' },
  { label: 'CS0001-000007', value: 'CS0001-000007', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0001-000007.png' },
  { label: 'CS0001-000008', value: 'CS0001-000008', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0001-000008.png' },
  { label: 'CS0001-000009', value: 'CS0001-000009', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0001-000009.png' },
  { label: 'CS0001-000010', value: 'CS0001-000010', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0001-000010.png' },
  { label: 'CS0001-000011', value: 'CS0001-000011', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0001-000011.png' },
  { label: 'CS0001-000012', value: 'CS0001-000012', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0001-000012.png' },
  { label: 'CS0001-000013', value: 'CS0001-000013', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0001-000013.png' },
  { label: 'CS0001-000014', value: 'CS0001-000014', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0001-000014.png' },
  { label: 'CS0001-000015', value: 'CS0001-000015', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0001-000015.png' },
  { label: 'CS0001-000016', value: 'CS0001-000016', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0001-000016.png' },

  // CS0002 系列
  { label: 'CS0002-000001', value: 'CS0002-000001', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0002-000001.png' },
  { label: 'CS0002-000002', value: 'CS0002-000002', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0002-000002.png' },
  { label: 'CS0002-000003', value: 'CS0002-000003', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0002-000003.png' },
  { label: 'CS0002-000004', value: 'CS0002-000004', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0002-000004.png' },
  { label: 'CS0002-000005', value: 'CS0002-000005', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0002-000005.png' },
  { label: 'CS0002-000006', value: 'CS0002-000006', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0002-000006.png' },
  { label: 'CS0002-000007', value: 'CS0002-000007', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0002-000007.png' },
  { label: 'CS0002-000008', value: 'CS0002-000008', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0002-000008.png' },
  { label: 'CS0002-000009', value: 'CS0002-000009', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0002-000009.png' },
  { label: 'CS0002-000010', value: 'CS0002-000010', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0002-000010.png' },
  { label: 'CS0002-000011', value: 'CS0002-000011', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0002-000011.png' },
  { label: 'CS0002-000012', value: 'CS0002-000012', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0002-000012.png' },
  { label: 'CS0002-000013', value: 'CS0002-000013', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0002-000013.png' },
  { label: 'CS0002-000014', value: 'CS0002-000014', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0002-000014.png' },
  { label: 'CS0002-000015', value: 'CS0002-000015', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0002-000015.png' },
  { label: 'CS0002-000016', value: 'CS0002-000016', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0002-000016.png' },

  // CS0003 系列
  { label: 'CS0003-000001', value: 'CS0003-000001', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000001.png' },
  { label: 'CS0003-000002', value: 'CS0003-000002', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000002.png' },
  { label: 'CS0003-000003', value: 'CS0003-000003', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000003.png' },
  { label: 'CS0003-000004', value: 'CS0003-000004', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000004.png' },
  { label: 'CS0003-000005', value: 'CS0003-000005', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000005.png' },
  { label: 'CS0003-000006', value: 'CS0003-000006', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000006.png' },
  { label: 'CS0003-000007', value: 'CS0003-000007', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000007.png' },
  { label: 'CS0003-000008', value: 'CS0003-000008', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000008.png' },
  { label: 'CS0003-000009', value: 'CS0003-000009', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000009.png' },
  { label: 'CS0003-000010', value: 'CS0003-000010', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000010.png' },
  { label: 'CS0003-000011', value: 'CS0003-000011', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000011.png' },
  { label: 'CS0003-000012', value: 'CS0003-000012', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000012.png' },
  { label: 'CS0003-000013', value: 'CS0003-000013', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000013.png' },
  { label: 'CS0003-000014', value: 'CS0003-000014', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000014.png' },
  { label: 'CS0003-000015', value: 'CS0003-000015', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000015.png' },
  { label: 'CS0003-000016', value: 'CS0003-000016', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000016.png' },
  { label: 'CS0003-000017', value: 'CS0003-000017', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000017.png' },
  { label: 'CS0003-000018', value: 'CS0003-000018', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000018.png' },
  { label: 'CS0003-000019', value: 'CS0003-000019', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000019.png' },
  { label: 'CS0003-000020', value: 'CS0003-000020', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000020.png' },
  { label: 'CS0003-000021', value: 'CS0003-000021', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000021.png' },
  { label: 'CS0003-000022', value: 'CS0003-000022', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000022.png' },
  { label: 'CS0003-000023', value: 'CS0003-000023', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000023.png' },
  { label: 'CS0003-000024', value: 'CS0003-000024', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000024.png' },
  { label: 'CS0003-000025', value: 'CS0003-000025', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0003-000025.png' },

  // CS0004 系列
  { label: 'CS0004-000001', value: 'CS0004-000001', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000001.png' },
  { label: 'CS0004-000002', value: 'CS0004-000002', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000002.png' },
  { label: 'CS0004-000003', value: 'CS0004-000003', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000003.png' },
  { label: 'CS0004-000004', value: 'CS0004-000004', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000004.png' },
  { label: 'CS0004-000005', value: 'CS0004-000005', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000005.png' },
  { label: 'CS0004-000006', value: 'CS0004-000006', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000006.png' },
  { label: 'CS0004-000007', value: 'CS0004-000007', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000007.png' },
  { label: 'CS0004-000008', value: 'CS0004-000008', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000008.png' },
  { label: 'CS0004-000009', value: 'CS0004-000009', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000009.png' },
  { label: 'CS0004-000010', value: 'CS0004-000010', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000010.png' },
  { label: 'CS0004-000011', value: 'CS0004-000011', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000011.png' },
  { label: 'CS0004-000012', value: 'CS0004-000012', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000012.png' },
  { label: 'CS0004-000013', value: 'CS0004-000013', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000013.png' },
  { label: 'CS0004-000014', value: 'CS0004-000014', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000014.png' },
  { label: 'CS0004-000015', value: 'CS0004-000015', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000015.png' },
  { label: 'CS0004-000016', value: 'CS0004-000016', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000016.png' },
  { label: 'CS0004-000017', value: 'CS0004-000017', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000017.png' },
  { label: 'CS0004-000018', value: 'CS0004-000018', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000018.png' },
  { label: 'CS0004-000019', value: 'CS0004-000019', image: 'https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/effect-color-style/CS0004-000019.png' }
]

// 处理花字样式路径并导出
processImagePaths(rawFlowerFontStyles);

export const flowerFontStyles = rawFlowerFontStyles;