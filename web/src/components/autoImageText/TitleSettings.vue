<template>
  <div class="title-settings">
    <div class="title-layout-no-preview">
      <!-- 左侧标题列表 -->
      <div class="titles-container">
        <el-empty v-if="titles.length === 0" description="暂无标题，点击下方按钮添加" />

        <div v-else class="title-input-list">
          <!-- 标题列表 -->
          <div
            v-for="(title, index) in titles"
            :key="title.id"
            class="title-input-item"
            :class="{ active: activeTitleIndex === index }"
          >
            <div class="title-number">标题 {{ index + 1 }}</div>

            <div class="title-item">
              <el-input v-model="title.content" placeholder="请输入标题文案" type="text" @focus="setActiveTitle(index)">
                <template #append>
                  <el-button @click="openCopywritingDialog(index)" link type="primary" :icon="Document" />
                  <el-button @click="removeTitle(index)" link type="danger" :icon="Delete" v-if="titles.length > 1" />
                </template>
              </el-input>
            </div>
          </div>

          <!-- 添加更多标题按钮 -->
          <div class="add-title-btn-wrap">
            <el-button type="primary" size="small" @click="addTitle" plain>
              <el-icon>
                <Plus />
              </el-icon>
              添加更多标题
            </el-button>
          </div>
        </div>

        <!-- 添加标题按钮 -->
        <div class="add-title-btn-wrap" v-if="titles.length === 0">
          <el-button type="primary" @click="addTitle" class="add-title-btn">
            <el-icon>
              <Plus />
            </el-icon>
            添加标题
          </el-button>
        </div>
      </div>

      <!-- 中间设置区域 -->
      <div class="title-settings-container-full-width" v-if="titles.length > 0 && titles[activeTitleIndex]">
        <div class="settings-header">
          <div class="title-number">标题 {{ activeTitleIndex + 1 }} 设置 (当前编辑)</div>
          <!-- 显示当前活动标题的内容 -->
          <el-text v-if="activeTitleContent" type="info" class="active-title-content">
            当前编辑内容: {{ activeTitleContent }}
          </el-text>
        </div>

        <div class="settings-content">
          <el-form label-position="top" size="small">
            <div class="settings-row">
              <div class="settings-col">
                <el-form-item :label="`文字高度 (${(titles[activeTitleIndex].height * 100).toFixed(0)}%)`">
                  <el-slider
                    :model-value="titles[activeTitleIndex].height"
                    @input="(val) => updateTitleProperty(activeTitleIndex, 'height', val)"
                    :min="0.01"
                    :max="1.0"
                    :step="0.01"
                    :format-tooltip="percentFormat"
                  />
                </el-form-item>
              </div>
            </div>

            <div class="settings-row">
              <div class="settings-col">
                <el-form-item label="字体">
                  <div class="font-select-container">
                    <el-select
                      :model-value="titles[activeTitleIndex].fontFamily"
                      @change="(val) => updateTitleProperty(activeTitleIndex, 'fontFamily', val)"
                      placeholder="请选择字体"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="font in fontList"
                        :key="font.value"
                        :label="font.label"
                        :value="font.value"
                        :class="`font-option-${font.fontFamily}`"
                      />
                    </el-select>
                    <div
                      v-if="titles[activeTitleIndex].fontFamily"
                      class="font-preview"
                      :style="{ fontFamily: getCurrentFontFamily() }"
                    >
                      字体预览效果 ABC abc 123
                    </div>
                  </div>
                </el-form-item>
              </div>

              <div class="settings-col">
                <el-form-item label="字号">
                  <el-input-number
                    :model-value="titles[activeTitleIndex].fontSize"
                    @change="(val) => updateTitleProperty(activeTitleIndex, 'fontSize', val)"
                    :min="12"
                    :max="200"
                  />
                </el-form-item>
              </div>
            </div>

            <el-form-item label="对齐方式">
              <el-radio-group
                :model-value="titles[activeTitleIndex].alignment"
                @change="(val) => updateTitleProperty(activeTitleIndex, 'alignment', val)"
              >
                <el-radio value="center">居中对齐</el-radio>
                <el-radio value="left">左对齐</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-tabs
              v-model="fontStyleTab[activeTitleIndex]"
              @tab-change="(tab) => handleFontStyleTabChange(tab, activeTitleIndex)"
            >
              <el-tab-pane label="普通样式" name="normal">
                <el-form-item label="字体颜色">
                  <el-color-picker
                    :model-value="titles[activeTitleIndex].fontStyle.color"
                    @change="(val) => updateTitleStyleProperty(activeTitleIndex, 'color', val)"
                    show-alpha
                    color-format="hex"
                  />
                </el-form-item>

                <el-form-item label="字体样式">
                  <el-radio-group
                    :model-value="titles[activeTitleIndex].fontStyle.styleType"
                    @change="(val) => updateTitleStyleProperty(activeTitleIndex, 'styleType', val)"
                  >
                    <el-radio value="none">暂不设置</el-radio>
                    <el-radio value="background">字幕背景</el-radio>
                    <el-radio value="border">字幕边框</el-radio>
                  </el-radio-group>
                </el-form-item>

                <template v-if="titles[activeTitleIndex].fontStyle.styleType === 'background'">
                  <el-form-item label="背景颜色">
                    <el-color-picker
                      :model-value="titles[activeTitleIndex].fontStyle.backgroundColor"
                      @change="(val) => updateTitleStyleProperty(activeTitleIndex, 'backgroundColor', val)"
                      show-alpha
                      color-format="hex"
                    />
                  </el-form-item>
                </template>

                <template v-if="titles[activeTitleIndex].fontStyle.styleType === 'border'">
                  <el-form-item label="边框颜色">
                    <el-color-picker
                      :model-value="titles[activeTitleIndex].fontStyle.borderColor"
                      @change="(val) => updateTitleStyleProperty(activeTitleIndex, 'borderColor', val)"
                      show-alpha
                      color-format="hex"
                    />
                  </el-form-item>
                  <el-form-item label="边框宽度">
                    <el-slider
                      :model-value="titles[activeTitleIndex].fontStyle.borderWidth"
                      @input="(val) => updateTitleStyleProperty(activeTitleIndex, 'borderWidth', val)"
                      :min="1"
                      :max="4"
                      :step="1"
                      show-input
                    />
                  </el-form-item>
                </template>
              </el-tab-pane>

              <el-tab-pane name="flower" :disabled="true">
                <template #label>
                  <span>花字样式</span>
                  <el-tooltip content="花字样式功能目前正在维护优化中，暂时无法使用" placement="top">
                    <el-icon style="margin-left: 4px; color: #909399; cursor: help">
                      <InfoFilled />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-alert title="花字样式功能暂时禁用" type="info" :closable="false" show-icon style="margin: 10px 0">
                  <template #default>
                    <p>花字样式功能目前正在维护优化中，暂时无法使用。</p>
                    <p>您可以使用常规字体样式来设置标题效果。</p>
                  </template>
                </el-alert>

                <!-- 禁用状态下的内容 -->
                <div style="opacity: 0.5; pointer-events: none">
                  <el-form-item label="使用随机效果">
                    <el-switch disabled />
                  </el-form-item>

                  <div class="flower-font-gallery">
                    <div class="flower-font-gallery-tip">请选择花字样式：</div>
                    <div class="flower-font-items-fixed-height">
                      <div class="flower-font-item disabled">
                        <div
                          style="
                            width: 80px;
                            height: 60px;
                            background: #f5f7fa;
                            border-radius: 4px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: #909399;
                          "
                        >
                          暂不可用
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 文案库选择对话框 -->
    <CopywritingSelector
      v-model="copywritingDialogVisible"
      :saved-params="currentTitle?.copywritingSelectionParams"
      @confirm="handleCopywritingConfirm"
    />
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
  import { Plus, Delete, Document, InfoFilled } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import CopywritingSelector from './CopywritingSelector.vue'
  import {
    flowerFontStyles as importedFlowerFontStyles,
    preloadAllFonts,
    injectFontCSS,
    fontList
  } from '../videoEffects.js'

  const props = defineProps({
    titles: { type: Array, required: true }
  })

  const emit = defineEmits(['update:titles', 'active-title-changed'])

  const activeTitleIndex = ref(0)
  // eslint-disable-next-line no-unused-vars
  const flowerFontStyles = ref(importedFlowerFontStyles) // 暂时禁用花字样式功能
  const copywritingDialogVisible = ref(false)
  const currentTitle = ref(null)
  const fontsLoaded = ref(false) // 字体加载状态

  const fontStyleTab = reactive([])

  const initFontStyleTab = () => {
    for (let i = 0; i < props.titles.length; i++) {
      if (!fontStyleTab[i]) {
        // 默认选择普通样式，禁用花字样式
        fontStyleTab[i] = 'normal'
      }
    }
  }

  const setActiveTitle = (index) => {
    if (activeTitleIndex.value !== index) {
      activeTitleIndex.value = index
      emit('active-title-changed', index)
    }
  }

  watch(activeTitleIndex, (newIndex) => {
    emit('active-title-changed', newIndex)
  })

  watch(
    () => props.titles,
    (newTitles) => {
      if (newTitles && newTitles.length > 0 && activeTitleIndex.value >= newTitles.length) {
        setActiveTitle(newTitles.length - 1)
      } else if (newTitles && newTitles.length > 0 && activeTitleIndex.value === -1) {
        setActiveTitle(0)
      }
      initFontStyleTab()

      // 检查有无需要修复数据一致性的情况
      const needsDataFix = newTitles.some((title) => {
        // 检查标题数据一致性
        if (title.content === undefined) {
          return true
        }
        return false
      })

      if (needsDataFix) {
        console.log('检测到标题数据需要修复一致性，执行修复...')
        const fixedTitles = JSON.parse(JSON.stringify(newTitles))

        fixedTitles.forEach((title) => {
          if (title.content === undefined) {
            title.content = ''
          }
        })

        // 使用nextTick避免立即执行导致的无限循环
        nextTick(() => {
          updateTitlesWithAdapter(fixedTitles)
        })
      }
    },
    { immediate: true, deep: true }
  )

  // 添加一个计算属性用于显示当前活动标题的内容
  const activeTitleContent = computed(() => {
    if (activeTitleIndex.value < 0 || activeTitleIndex.value >= props.titles.length) {
      return ''
    }
    return props.titles[activeTitleIndex.value].content || ''
  })

  // 获取当前字体的CSS字体族名称
  const getCurrentFontFamily = () => {
    if (activeTitleIndex.value < 0 || activeTitleIndex.value >= props.titles.length) {
      return 'inherit'
    }

    const currentFontUrl = props.titles[activeTitleIndex.value].fontFamily
    const fontItem = fontList.find((font) => font.value === currentFontUrl)

    if (fontItem) {
      return `'${fontItem.fontFamily}', sans-serif`
    }

    return 'inherit'
  }

  const handleFontStyleTabChange = (tabName, index) => {
    if (!props.titles[index]) return
    const currentTitleStyle = props.titles[index].fontStyle
    if (tabName === 'normal' && currentTitleStyle?.type === 'flower') {
      const newTitles = JSON.parse(JSON.stringify(props.titles))
      newTitles[index].fontStyle.type = 'normal'
      newTitles[index].flowerStyle = ''
      emit('update:titles', newTitles)
    } else if (tabName === 'flower') {
      const newTitles = JSON.parse(JSON.stringify(props.titles))
      newTitles[index].fontStyle.type = 'flower'
      emit('update:titles', newTitles)
      if (newTitles[index].randomEffect) {
        newTitles[index].fontStyle.type = 'flower'
        emit('update:titles', newTitles)
      }
    }
  }

  onMounted(async () => {
    if (props.titles && props.titles.length > 0) {
      emit('active-title-changed', activeTitleIndex.value)
    }

    // 先注入字体CSS，再预加载字体
    try {
      injectFontCSS()
      await preloadAllFonts()
      fontsLoaded.value = true
      console.log('字体CSS注入和预加载完成')
    } catch (error) {
      console.warn('字体加载失败:', error)
    }
  })

  // eslint-disable-next-line no-unused-vars
  const selectFlowerFont = (index, styleValue) => {
    // 暂时禁用花字样式功能
    if (!props.titles[index]) return
    const newTitles = JSON.parse(JSON.stringify(props.titles))
    newTitles[index].flowerStyle = styleValue
    newTitles[index].fontStyle.type = 'flower'
    emit('update:titles', newTitles)
  }

  const addTitle = () => {
    const newTitleObject = createNewTitle()
    const newTitles = [...props.titles, newTitleObject]
    emit('update:titles', newTitles)
    setActiveTitle(newTitles.length - 1)
  }

  const removeTitle = (index) => {
    if (!props.titles[index] || props.titles.length <= 1) return
    const newTitles = JSON.parse(JSON.stringify(props.titles))
    newTitles.splice(index, 1)
    fontStyleTab.splice(index, 1)
    updateTitlesWithAdapter(newTitles)
    if (activeTitleIndex.value >= newTitles.length) {
      setActiveTitle(Math.max(0, newTitles.length - 1))
    } else if (newTitles.length === 0) {
      setActiveTitle(-1)
    } else {
      emit('active-title-changed', activeTitleIndex.value)
    }
  }

  const percentFormat = (val) => (val ? Math.round(val * 100) + '%' : '0%')

  const updateTitleProperty = (index, key, value) => {
    if (!props.titles[index]) return
    const newTitles = JSON.parse(JSON.stringify(props.titles))
    newTitles[index][key] = value
    updateTitlesWithAdapter(newTitles)
  }

  const updateTitleStyleProperty = (index, key, value) => {
    if (!props.titles[index] || !props.titles[index].fontStyle) return
    const newTitles = JSON.parse(JSON.stringify(props.titles))
    newTitles[index].fontStyle[key] = value
    updateTitlesWithAdapter(newTitles)
  }

  const createNewTitle = () => {
    const newTitle = {
      height: 0.15,
      fontFamily: fontList[0].value, // 使用第一个字体作为默认字体
      fontSize: 20,
      alignment: 'center',
      fontStyle: {
        type: 'normal',
        color: '#ffffff',
        styleType: 'none',
        backgroundColor: '',
        borderColor: '',
        borderWidth: 1
      },
      flowerStyle: '',
      randomEffect: false,
      content: '',
      id: Date.now() + Math.random().toString(36).substring(2, 9),
      copywritingSelectionParams: null
    }

    initFontStyleTab()
    return newTitle
  }

  // 打开文案库选择对话框
  const openCopywritingDialog = (index) => {
    currentTitle.value = props.titles[index]
    copywritingDialogVisible.value = true
  }

  // 处理文案选择确认
  const handleCopywritingConfirm = ({ contents, params }) => {
    if (!currentTitle.value) return

    const newTitles = JSON.parse(JSON.stringify(props.titles))
    const currentIndex = newTitles.findIndex((t) => t.id === currentTitle.value.id)
    if (currentIndex === -1) return

    // 保存选择参数
    newTitles[currentIndex].copywritingSelectionParams = params

    // 如果只有一条文案，直接更新当前标题
    if (contents.length === 1) {
      newTitles[currentIndex].content = contents[0]
    } else {
      // 如果有多条文案，需要添加新的标题
      // 1. 更新当前标题为第一条文案
      newTitles[currentIndex].content = contents[0]

      // 2. 在当前位置之后插入新的标题
      const insertIndex = currentIndex + 1
      const newTitles_ = contents.slice(1).map((content) => ({
        content,
        height: currentTitle.value.height,
        fontFamily: currentTitle.value.fontFamily,
        fontSize: currentTitle.value.fontSize,
        alignment: currentTitle.value.alignment,
        fontStyle: { ...currentTitle.value.fontStyle },
        flowerStyle: currentTitle.value.flowerStyle,
        randomEffect: currentTitle.value.randomEffect,
        id: Date.now() + Math.random().toString(36).substring(2, 9),
        copywritingSelectionParams: null
      }))

      // 3. 将新的标题插入到数组中
      newTitles.splice(insertIndex, 0, ...newTitles_)
    }

    updateTitlesWithAdapter(newTitles)
    ElMessage.success(`已为标题选择 ${contents.length} 条文案`)

    // 关闭对话框
    copywritingDialogVisible.value = false
    currentTitle.value = null
  }

  // 在有需要时更新emits
  const updateTitlesWithAdapter = (newTitles) => {
    try {
      // 确保传入的是有效数组
      if (!Array.isArray(newTitles)) {
        console.error('updateTitlesWithAdapter: newTitles不是有效数组')
        return
      }

      // 创建新的数组，避免直接修改原数组
      const adaptedTitles = newTitles.map((title) => {
        // 深拷贝每个标题对象
        const newTitle = JSON.parse(JSON.stringify(title))

        // 确保主标题有content属性
        if (!newTitle.content) {
          newTitle.content = ''
        }

        // 确保有id属性
        if (!newTitle.id) {
          newTitle.id = Date.now() + Math.random().toString(36).substring(2, 9)
        }

        return newTitle
      })

      // 发送更新事件
      emit('update:titles', adaptedTitles)
    } catch (err) {
      console.error('更新标题时出错:', err)
      // 如果出错，尝试发送一个安全的副本
      try {
        emit('update:titles', JSON.parse(JSON.stringify(newTitles)))
      } catch (e) {
        console.error('备用更新标题也失败:', e)
      }
    }
  }
</script>

<style scoped>
  .title-settings {
    margin-bottom: 30px;
  }

  .title-layout-no-preview {
    display: flex;
    gap: 20px;
    margin-top: 20px;
  }

  .titles-container {
    flex: 1;
    border-right: 1px solid #ebeef5;
    padding-right: 15px;
    min-width: 280px;
  }

  .title-settings-container-full-width {
    flex: 2;
    padding: 0 15px;
    min-width: 350px;
  }

  .title-input-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .title-input-item {
    position: relative;
    padding: 12px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s;
    border: 1px solid #e0e0e0;
  }

  .title-input-item.active {
    border-color: #409eff;
    background-color: #ecf5ff;
  }

  .title-input-item .title-number {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 10px;
    color: #606266;
  }

  .settings-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
  }

  .settings-header .title-number {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  .settings-content {
    padding: 0 10px;
  }

  .settings-row {
    display: flex;
    gap: 20px;
  }

  .settings-col {
    flex: 1;
  }

  .flower-font-gallery {
    margin-top: 15px;
  }

  .flower-font-gallery-tip {
    margin-bottom: 10px;
    color: #606266;
  }

  .flower-font-items-fixed-height {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    max-height: 250px;
    overflow-y: auto;
    padding: 10px;
    border-radius: 6px;
    background-color: #f6f8fa;
  }

  .flower-font-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 110px;
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 6px;
    padding: 5px;
    transition: all 0.3s;
  }

  .flower-font-item:hover {
    background-color: #eaeaea;
  }

  .flower-font-item-active {
    border-color: #4b6cb7;
    background-color: rgba(75, 108, 183, 0.1);
  }

  .flower-font-image {
    width: 100px;
    height: 60px;
    object-fit: contain;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
    background-color: white;
  }

  .flower-font-name {
    margin-top: 5px;
    font-size: 12px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }

  .main-title-block {
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 15px;
  }

  .main-title-block.active {
    border-color: #409eff;
    background-color: #ecf5ff;
  }

  .main-title-item {
    margin-bottom: 10px;
    padding: 5px 0;
    border-radius: 4px;
    transition: all 0.2s;
  }

  .main-title-item.active {
    background-color: rgba(64, 158, 255, 0.1);
  }

  .add-main-title-btn-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px dashed #e0e0e0;
  }

  .add-title-btn-wrap {
    margin-top: 20px;
    text-align: center;
  }

  .add-title-btn {
    width: 100%;
    padding: 10px 0;
  }

  .active-title-content {
    margin-left: 20px;
    font-size: 12px;
    color: #606266;
  }

  .clickable-tag {
    cursor: pointer;
    transition: all 0.2s;
  }

  .clickable-tag:hover {
    background-color: #ecf5ff;
    color: #409eff;
    transform: scale(1.05);
  }

  .subtitle-edit-tag {
    display: inline-flex;
    align-items: center;
    gap: 3px;
  }

  .edit-icon {
    font-size: 12px;
  }

  .subtitle-contents-editor {
    max-height: 400px;
  }

  .subtitle-contents-list {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
    border-radius: 4px;
    background-color: #f5f7fa;
    margin-bottom: 15px;
  }

  .subtitle-content-item {
    margin-bottom: 10px;
  }

  .subtitle-content-actions {
    display: flex;
    justify-content: center;
    margin-top: 15px;
  }

  .font-select-container {
    width: 100%;
  }

  .font-preview {
    margin-top: 8px;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    font-size: 14px;
    color: #606266;
    text-align: center;
  }
</style>
