<template>
  <div class="background-music">
    <!-- 背景音乐列表 -->
    <div class="music-list">
      <el-empty v-if="musics.length === 0" description="暂无背景音乐，点击下方按钮添加" />

      <div v-for="(music, index) in musics" :key="index" class="music-item" :class="{ playing: isMusicPlaying(music) }">
        <div class="music-info">
          <div class="music-icon" :class="{ playing: isMusicPlaying(music) }">
            <el-icon>
              <Headset />
            </el-icon>
          </div>
          <div class="music-details">
            <div class="music-name">
              <span v-if="music.duration && !isNaN(music.duration) && music.duration > 0" class="music-duration">
                （{{ formatDuration(music.duration) }}）
              </span>
              {{ getMusicName(music) }}
            </div>
            <div v-if="isMusicPlaying(music)" class="music-progress">
              <el-progress
                :percentage="playingProgress"
                :stroke-width="3"
                :show-text="false"
                :color="isMusicPlaying(music) ? '#409EFF' : '#e6e6e6'"
              />
              <span v-if="isTimeLoaded" class="music-time"
                >{{ formatDuration(currentTime) }} / {{ formatDuration(duration) }}</span
              >
              <span v-else class="music-time">正在播放</span>
            </div>
          </div>
        </div>
        <div class="music-actions">
          <el-button
            @click="playMusic(music)"
            circle
            :icon="isMusicPlaying(music) ? VideoPause : VideoPlay"
            type="primary"
            plain
            size="small"
          />
          <el-button @click="removeMusic(index)" circle :icon="Delete" type="danger" plain size="small" />
        </div>
      </div>
    </div>

    <!-- 提示信息 -->
    <div class="music-tip" v-if="musics.length > 0">
      <el-alert type="info" :closable="false" show-icon> 配置多个背景音乐时，系统会随机选取一个进行播放 </el-alert>
    </div>

    <!-- 添加背景音乐按钮 -->
    <div class="add-music-btn-wrap">
      <el-button type="primary" @click="openMusicDialog" class="add-music-btn">
        <el-icon>
          <Plus />
        </el-icon>
        添加背景音乐
      </el-button>
    </div>

    <!-- 音乐库选择对话框 -->
    <el-dialog v-model="musicDialogVisible" title="音乐库选择" width="80%" class="music-dialog">
      <div class="dialog-content">
        <div class="dialog-sidebar">
          <div class="category-list">
            <!-- 全部分类项 -->
            <div class="category-item" :class="{ active: selectedCategory === '0' }" @click="selectCategory('0')">
              全部
              <!-- 素材数量指示器 -->
              <span v-if="getTotalResourceCount() > 0" class="category-resource-count">
                {{ getTotalResourceCount() }}
              </span>
            </div>
            <!-- 递归渲染分类树 -->
            <category-tree
              v-for="category in categoriesTree"
              :key="category.categoryId"
              :category="category"
              :selected-category="selectedCategory"
              @select="selectCategory"
            />
          </div>
        </div>

        <div class="dialog-main">
          <div class="dialog-toolbar">
            <div class="selected-count" v-if="selectedMusics.length > 0">
              已选择 {{ selectedMusics.length }} 个音乐
              <el-button @click="clearSelection" type="primary" size="small">清空选择</el-button>
            </div>
            <!-- 添加全选按钮 -->
            <div class="toolbar-actions">
              <el-button @click="selectAllMusic" type="primary" plain size="small">
                {{ isCurrentPageAllSelected ? '取消全选' : '全选当前页' }}
              </el-button>
            </div>
          </div>

          <div class="dialog-filters">
            <div class="music-search">
              <el-input v-model="searchKeyword" placeholder="搜索音乐名称" clearable @input="searchMusic">
                <template #prefix>
                  <el-icon>
                    <Search />
                  </el-icon>
                </template>
              </el-input>
            </div>
            <div class="filter-options">
              <div class="filter-row">
                <el-checkbox v-model="onlyShowWithDouyinId" @change="handleFilterChange">
                  只显示有抖音音乐ID的音乐
                </el-checkbox>
                <label class="filter-label" style="margin-left: 20px">选择抖音用户：</label>
                <el-select
                  v-model="selectedDyUserId"
                  placeholder="请选择抖音用户，用于生成音乐ID"
                  style="width: 300px; margin-left: 10px"
                  filterable
                  clearable
                  :filter-method="filterDyUsers"
                  @change="onDyUserSelected"
                >
                  <el-option-group
                    v-for="group in filteredDyUserGroups"
                    :key="group.sysNickname"
                    :label="group.sysNickname"
                  >
                    <el-option
                      v-for="user in group.dyUsers"
                      :key="user.id"
                      :label="user.nickname"
                      :value="user.id"
                      :data-unique-id="user.uniqueId"
                    >
                      <div style="display: flex; justify-content: space-between; align-items: center">
                        <span>{{ user.nickname }}</span>
                        <span style="color: #8492a6; font-size: 13px">{{ user.uniqueId }}</span>
                      </div>
                    </el-option>
                  </el-option-group>
                </el-select>
              </div>
            </div>
          </div>

          <div class="music-pagination">
            <el-pagination
              :current-page="currentPage"
              :page-size="pageSize"
              :total="totalMusic"
              :page-sizes="[20, 50, 100, 200]"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              @update:current-page="(val) => (currentPage = val)"
              @update:page-size="(val) => (pageSize = val)"
              style="margin: 0 0 15px 0"
            />
          </div>

          <div class="music-grid">
            <el-empty v-if="filteredMusic.length === 0" description="暂无音乐，可以尝试选择其他分类或搜索关键词" />

            <div
              v-else
              v-for="music in filteredMusic"
              :key="music.media_id || music.mediaId"
              class="music-grid-item"
              :class="{
                selected: isMusicSelected(music.media_id || music.mediaId),
                playing: isMusicPlaying(music),
                disabled: !music.dyMusicId
              }"
              @click="toggleMusicSelection(music)"
            >
              <div class="music-thumbnail">
                <div class="music-note-icon" :class="{ playing: isMusicPlaying(music) }">
                  <el-icon>
                    <Headset />
                  </el-icon>
                </div>
              </div>
              <div class="music-details">
                <div class="music-title">{{ music.name }}</div>
                <div v-if="music.duration && !isNaN(music.duration) && music.duration > 0" class="music-duration">
                  {{ formatDuration(music.duration) }}
                </div>
                <div class="music-douyin-status">
                  <el-tag v-if="music.dyMusicId" type="success" size="small"> 音乐ID: {{ music.dyMusicId }} </el-tag>
                  <el-button
                    v-else
                    @click.stop="generateDouyinMusicId(music)"
                    type="warning"
                    size="small"
                    plain
                    :loading="music._generating"
                  >
                    发布作品生成音乐ID
                  </el-button>
                </div>
                <div v-if="isMusicPlaying(music)" class="music-progress">
                  <el-progress
                    :percentage="playingProgress"
                    :stroke-width="3"
                    :show-text="false"
                    :color="isMusicPlaying(music) ? '#409EFF' : '#e6e6e6'"
                  />
                  <span v-if="isTimeLoaded" class="music-time"
                    >{{ formatDuration(currentTime) }} / {{ formatDuration(duration) }}</span
                  >
                  <span v-else class="music-time">正在播放</span>
                </div>
              </div>
              <div class="music-controls">
                <el-button
                  @click.stop="playPreview(music)"
                  circle
                  :icon="isMusicPlaying(music) ? VideoPause : VideoPlay"
                  type="primary"
                  text
                  size="small"
                />
                <div class="music-selection-icon" v-if="isMusicSelected(music.media_id || music.mediaId)">
                  <el-icon>
                    <Check />
                  </el-icon>
                </div>
              </div>
            </div>
          </div>

          <div class="music-pagination">
            <el-pagination
              :current-page="currentPage"
              :page-size="pageSize"
              :total="totalMusic"
              :page-sizes="[20, 50, 100, 200]"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              @update:current-page="(val) => (currentPage = val)"
              @update:page-size="(val) => (pageSize = val)"
              style="margin: 0 0 15px 0"
            />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="musicDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSelection">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 进度对话框 -->
    <el-dialog
      v-model="showProgressDialog"
      title="生成音乐ID进度"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="progress-container">
        <div class="progress-header">
          <el-icon v-if="!isCompleted" class="loading-icon">
            <Loading />
          </el-icon>
          <el-icon v-else-if="hasError" class="error-icon">
            <CircleClose />
          </el-icon>
          <el-icon v-else class="success-icon">
            <CircleCheck />
          </el-icon>
          <span class="progress-title">
            {{ isCompleted ? (hasError ? '生成失败' : '生成成功') : '正在生成音乐ID...' }}
          </span>
        </div>

        <div class="progress-steps">
          <div v-for="(step, index) in progressSteps" :key="index" class="progress-step" :class="step.status">
            <div class="step-info">
              <div class="step-number">{{ step.step }}</div>
              <div class="step-content">
                <div class="step-message">{{ step.message }}</div>
                <div class="step-time">{{ step.timestamp }}</div>
              </div>
              <div class="step-status">
                <el-icon v-if="step.status === 'running'" class="running-icon">
                  <Loading />
                </el-icon>
                <el-icon v-else-if="step.status === 'error'" class="error-icon">
                  <CircleClose />
                </el-icon>
                <el-icon v-else-if="step.status === 'success'" class="success-icon">
                  <CircleCheck />
                </el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button v-if="isCompleted" @click="showProgressDialog = false" type="primary"> 确定 </el-button>
        <el-button v-else @click="showProgressDialog = false"> 后台运行 </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue'
  import {
    Headset,
    Plus,
    Delete,
    VideoPlay,
    VideoPause,
    Search,
    Check,
    Loading,
    CircleClose,
    CircleCheck
  } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import { getResourceCategories, getResourceLibrary } from '@/api/ai/auto-video'
  import { getProductUserList } from '@/api/douyin/product'
  import CategoryTree from '../autoVideo/CategoryTree.vue'

  const props = defineProps({
    musics: {
      type: Array,
      required: true
    }
  })

  const emit = defineEmits(['update:musics'])

  // 创建一个单例音频播放器
  let globalAudioPlayer = null
  if (typeof window !== 'undefined') {
    if (!window.globalAudioPlayer) {
      window.globalAudioPlayer = new Audio()
      globalAudioPlayer = window.globalAudioPlayer

      // 添加全局事件处理
      globalAudioPlayer.addEventListener('ended', () => {
        resetPlayingStatus()
      })

      globalAudioPlayer.addEventListener('error', (_e) => {
        resetPlayingStatus()
      })
    } else {
      globalAudioPlayer = window.globalAudioPlayer
    }
  }

  // 音乐相关变量
  const musicDialogVisible = ref(false)
  const categories = ref([])
  const categoriesTree = ref([])
  const selectedCategory = ref('')
  const musicList = ref([])
  const currentPage = ref(1)
  const pageSize = ref(50)
  const totalMusic = ref(0)
  const totalAllMusic = ref(0)
  const searchKeyword = ref('')
  const selectedMusics = ref([])
  const onlyShowWithDouyinId = ref(false)

  // 抖音用户相关变量
  const dyUserGroups = ref([])
  const filteredDyUserGroups = ref([])
  const selectedDyUserId = ref(null)
  const selectedDyUserCookie = ref('')

  // 进度对话框相关变量
  const showProgressDialog = ref(false)
  const progressSteps = ref([])
  const currentStep = ref(0)
  const isCompleted = ref(false)
  const hasError = ref(false)

  // 播放状态变量
  const playingMusicId = ref(null)
  const isPlaying = ref(false)
  const currentTime = ref(0)
  const duration = ref(0)
  const playingProgress = ref(0)
  const isTimeLoaded = ref(false)
  const audioTimer = ref(null)

  // 过滤后的音乐列表
  const filteredMusic = computed(() => {
    if (!musicList.value || musicList.value.length === 0) {
      return []
    }

    let filtered = musicList.value

    // 按抖音音乐ID筛选
    if (onlyShowWithDouyinId.value) {
      filtered = filtered.filter((music) => music.dyMusicId && music.dyMusicId.trim() !== '')
    }

    // 按关键词搜索
    if (!searchKeyword.value) {
      return filtered
    }

    const keyword = searchKeyword.value.toLowerCase()
    return filtered.filter((music) => {
      const name = music.name || ''
      return name.toLowerCase().includes(keyword)
    })
  })

  // 计算当前页是否已全选
  const isCurrentPageAllSelected = computed(() => {
    if (filteredMusic.value.length === 0) return false

    return filteredMusic.value.every((music) => {
      const musicId = music.media_id || music.mediaId
      return selectedMusics.value.some((selected) => selected.media_id === musicId || selected.mediaId === musicId)
    })
  })

  // 监听全局音频播放器状态
  const setupAudioListeners = () => {
    if (!globalAudioPlayer) return

    const updateProgress = () => {
      if (globalAudioPlayer.paused) return

      currentTime.value = globalAudioPlayer.currentTime || 0

      if (
        !isTimeLoaded.value &&
        globalAudioPlayer.duration &&
        !isNaN(globalAudioPlayer.duration) &&
        globalAudioPlayer.duration > 0
      ) {
        duration.value = globalAudioPlayer.duration
        isTimeLoaded.value = true
      }

      if (duration.value > 0) {
        playingProgress.value = (currentTime.value / duration.value) * 100
      }
    }

    if (audioTimer.value) {
      clearInterval(audioTimer.value)
    }

    audioTimer.value = setInterval(updateProgress, 1000)
  }

  // 重置播放状态
  const resetPlayingStatus = () => {
    playingMusicId.value = null
    isPlaying.value = false
    currentTime.value = 0
    duration.value = 0
    playingProgress.value = 0
    isTimeLoaded.value = false

    if (audioTimer.value) {
      clearInterval(audioTimer.value)
    }
  }

  // 根据ID判断音乐是否正在播放
  const isMusicPlaying = (music) => {
    if (!isPlaying.value || !playingMusicId.value) return false

    const musicId = getUniqueMusicId(music)
    return playingMusicId.value === musicId
  }

  // 获取音乐的唯一标识
  const getUniqueMusicId = (music) => {
    if (typeof music === 'string') {
      return music
    } else if (music && typeof music === 'object') {
      return music.media_id || music.mediaId
    }
    return null
  }

  // 获取音乐的URL
  const getMusicUrl = (music) => {
    if (typeof music === 'string') {
      return music.startsWith('http') ? music : null
    } else if (music && typeof music === 'object') {
      return music.url || music.fileUrl || null
    }
    return null
  }

  // 获取音乐名称
  const getMusicName = (music) => {
    if (typeof music === 'string') {
      return '音乐'
    } else if (music && typeof music === 'object') {
      return music.name || '未命名音乐'
    }
    return '未命名音乐'
  }

  // 播放或暂停音乐
  const playMusic = (music) => {
    const musicId = getUniqueMusicId(music)
    const musicUrl = getMusicUrl(music)

    if (!musicId || !musicUrl) {
      ElMessage.warning('无法播放，音乐URL不可用')
      return
    }

    if (playingMusicId.value === musicId) {
      if (isPlaying.value) {
        if (globalAudioPlayer) {
          globalAudioPlayer.pause()
          isPlaying.value = false
        }
      } else {
        if (globalAudioPlayer) {
          globalAudioPlayer
            .play()
            .then(() => {
              isPlaying.value = true
              setupAudioListeners()
            })
            .catch((err) => {
              console.error('恢复播放失败:', err)
              resetPlayingStatus()
            })
        }
      }
      return
    }

    if (globalAudioPlayer) {
      resetPlayingStatus()

      globalAudioPlayer.src = musicUrl
      isTimeLoaded.value = false

      if (typeof music === 'object' && music.duration) {
        duration.value = music.duration
        isTimeLoaded.value = true
      }

      globalAudioPlayer
        .play()
        .then(() => {
          playingMusicId.value = musicId
          isPlaying.value = true
          setupAudioListeners()
        })
        .catch((err) => {
          console.error('播放失败:', err)
          ElMessage.error('播放失败: ' + err.message)
          resetPlayingStatus()
        })
    }
  }

  // 预览音乐
  const playPreview = (music) => {
    playMusic(music)
  }

  // 删除音乐
  const removeMusic = (index) => {
    const newMusics = props.musics.filter((_, i) => i !== index)
    emit('update:musics', newMusics)
  }

  // 格式化时长
  const formatDuration = (seconds) => {
    if (!seconds || isNaN(seconds)) return '00:00'

    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)

    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // 打开音乐选择对话框
  const openMusicDialog = async () => {
    if (props.musics.length >= 50) {
      ElMessage.warning('最多添加50个背景音乐')
      return
    }

    musicDialogVisible.value = true

    // 将已选音乐转换为selectedMusics期望的格式
    selectedMusics.value = props.musics.map((m) => {
      if (typeof m === 'string') {
        return {
          media_id: m,
          mediaId: m,
          name: '未命名音乐'
        }
      }
      return {
        media_id: m.media_id || m.mediaId,
        mediaId: m.media_id || m.mediaId,
        name: m.name || '未命名音乐',
        url: m.url || '',
        fileUrl: m.fileUrl || '',
        duration: m.duration || 0
      }
    })

    currentPage.value = 1

    await loadCategories()
    await loadMusic()
    await getDyUserList()
  }

  // 加载音乐分类
  const loadCategories = async () => {
    try {
      const res = await getResourceCategories('audio')

      if (!res) {
        ElMessage.error('获取音乐分类失败: 网络请求错误')
        categories.value = []
        categoriesTree.value = []
        return
      }

      if (res.code === 0 && res.data) {
        const allCategories = []
        const topCategories = []

        const processCategory = (category, parentId = 0) => {
          if (!category) return null

          const processedCategory = {
            categoryId: String(category.ID || 0),
            name: category.name || '未命名',
            type: 'audio',
            pid: parentId,
            resourceCount: category.resourceCount || 0,
            visibility: category.visibility,
            children: []
          }

          allCategories.push(processedCategory)

          if (category.children && Array.isArray(category.children)) {
            const sortedChildren = [...category.children].sort((a, b) => {
              if (a.pid === 0 && b.pid === 0) {
                if (a.visibility !== b.visibility) {
                  return a.visibility - b.visibility
                }
                return a.name.localeCompare(b.name)
              }
              return 0
            })

            sortedChildren.forEach((child) => {
              const childCategory = processCategory(child, processedCategory.categoryId)
              if (childCategory) {
                processedCategory.children.push(childCategory)
              }
            })
          }

          return processedCategory
        }

        if (Array.isArray(res.data)) {
          const sortedTopCategories = [...res.data].sort((a, b) => {
            if (a.visibility !== b.visibility) {
              return a.visibility - b.visibility
            }
            return a.name.localeCompare(b.name)
          })

          sortedTopCategories.forEach((cat) => {
            const topCategory = processCategory(cat)
            if (topCategory) {
              topCategories.push(topCategory)
            }
          })
        } else if (res.data && typeof res.data === 'object') {
          const singleCategory = processCategory(res.data)
          if (singleCategory) {
            topCategories.push(singleCategory)
          }
        }

        const allCategory = {
          categoryId: '0',
          name: '全部',
          type: 'audio',
          resourceCount: 0
        }
        allCategories.unshift(allCategory)

        categoriesTree.value = topCategories
        categories.value = allCategories

        if (!selectedCategory.value) {
          selectedCategory.value = '0'
        }
      } else {
        ElMessage.error('获取音乐分类失败: 响应格式异常')
        categories.value = []
        categoriesTree.value = []
      }
    } catch (error) {
      ElMessage.error('获取音乐分类失败: ' + error.message)
      categories.value = []
      categoriesTree.value = []
    }
  }

  // 加载音乐资源
  const loadMusic = async () => {
    try {
      const params = {
        type: 'audio',
        page: currentPage.value,
        pageSize: pageSize.value,
        keyword: searchKeyword.value
      }

      if (selectedCategory.value && selectedCategory.value !== '0') {
        params.category = selectedCategory.value
      }

      const res = await getResourceLibrary(params)

      if (!res) {
        ElMessage.error('获取音乐资源失败: 网络请求错误')
        musicList.value = []
        totalMusic.value = 0
        return
      }

      if (res.code === 0 && res.data) {
        if (res.data.list === null) {
          musicList.value = []
          totalMusic.value = 0
          return
        }

        if (Array.isArray(res.data.list)) {
          musicList.value = res.data.list.map((item) => {
            return {
              ID: item.ID || item.id || '',
              id: item.ID || item.id || '',
              media_id: item.media_id || item.mediaId || item.ID || item.id || '',
              mediaId: item.media_id || item.mediaId || item.ID || item.id || '',
              name: item.name || '未命名音乐',
              fileUrl: item.fileUrl || '',
              url: item.url || item.fileUrl || '',
              duration: item.duration || 0,
              dyMusicId: item.dyMusicId || ''
            }
          })

          totalMusic.value = res.data.total || 0

          if (selectedCategory.value === '0') {
            totalAllMusic.value = totalMusic.value
          }

          updateCategoriesResourceCount()
        } else {
          musicList.value = []
          totalMusic.value = 0
        }
      } else {
        console.error('无法识别的音乐响应格式:', res)
        ElMessage.error('获取音乐资源失败: 响应格式异常')
        musicList.value = []
        totalMusic.value = 0
      }
    } catch (error) {
      console.error('获取音乐资源失败', error)
      ElMessage.error('获取音乐资源失败: ' + error.message)
      musicList.value = []
      totalMusic.value = 0
    }
  }

  // 更新分类的资源数量统计
  const updateCategoriesResourceCount = () => {
    const needsUpdate = categories.value.some((category) => !category.resourceCount)
    if (!needsUpdate) return

    const allCategory = categories.value.find((c) => c.categoryId === '0')
    if (allCategory) {
      allCategory.resourceCount = totalAllMusic.value
    }

    if (selectedCategory.value !== '0') {
      const currentCategory = categories.value.find((c) => c.categoryId === selectedCategory.value)
      if (currentCategory) {
        currentCategory.resourceCount = totalMusic.value
      }
    }
  }

  // 获取全部资源数量
  const getTotalResourceCount = () => {
    return totalAllMusic.value
  }

  // 选择分类
  const selectCategory = (categoryId) => {
    selectedCategory.value = categoryId
    loadMusic()
  }

  // 搜索音乐
  const searchMusic = () => {
    currentPage.value = 1
    loadMusic()
  }

  // 处理分页大小变化
  const handleSizeChange = (size) => {
    pageSize.value = size
    loadMusic()
  }

  // 处理页码变化
  const handleCurrentChange = (page) => {
    currentPage.value = page
    loadMusic()
  }

  // 判断音乐是否被选中
  const isMusicSelected = (musicId) => {
    return selectedMusics.value.some((m) => m.media_id === musicId || m.mediaId === musicId)
  }

  // 切换音乐选择状态
  const toggleMusicSelection = (music) => {
    // 没有音乐ID的音乐不能被选中
    if (!music.dyMusicId) {
      ElMessage.warning('该音乐没有抖音音乐ID，无法选择。请先生成音乐ID。')
      return
    }

    const musicId = music.media_id || music.mediaId
    const index = selectedMusics.value.findIndex((m) => m.media_id === musicId || m.mediaId === musicId)

    if (index === -1) {
      selectedMusics.value.push(music)
    } else {
      selectedMusics.value.splice(index, 1)
    }
  }

  // 全选当前页音乐
  const selectAllMusic = () => {
    if (isCurrentPageAllSelected.value) {
      selectedMusics.value = selectedMusics.value.filter(
        (m) => !filteredMusic.value.some((fm) => fm.media_id === m.media_id || fm.mediaId === m.mediaId)
      )
      return
    }

    const newSelections = filteredMusic.value.filter(
      (m) => m.dyMusicId && !selectedMusics.value.some((sm) => sm.media_id === m.media_id || sm.mediaId === m.mediaId)
    )

    selectedMusics.value = [...selectedMusics.value, ...newSelections]
  }

  // 清空选择的音乐
  const clearSelection = () => {
    selectedMusics.value = []
  }

  // 确认选择
  const confirmSelection = () => {
    const newMusics = selectedMusics.value.map((music) => ({
      mediaId: music.media_id || music.mediaId,
      name: music.name,
      url: music.url || music.fileUrl,
      duration: music.duration || 0
    }))

    emit('update:musics', newMusics)
    musicDialogVisible.value = false
    ElMessage.success(`已选择 ${newMusics.length} 个音乐`)
  }

  // 生成抖音音乐ID（流式版本）
  const generateDouyinMusicId = async (music) => {
    // 检查是否选择了抖音用户
    if (!selectedDyUserId.value) {
      ElMessage.error('请先选择抖音用户')
      return
    }

    // 获取音乐ID，优先使用ID，然后是id，最后是media_id
    const musicId = music.ID || music.id || music.media_id || music.mediaId

    if (!musicId) {
      ElMessage.error('音乐ID无效，无法生成抖音音乐ID')
      return
    }

    // 设置生成状态
    music._generating = true

    // 显示进度对话框
    showProgressDialog.value = true
    progressSteps.value = []
    currentStep.value = 0
    isCompleted.value = false
    hasError.value = false

    try {
      // 准备请求体，传递抖音用户ID
      const requestBody = {
        dyUserId: selectedDyUserId.value
      }

      const response = await fetch(`/api/media/music/${musicId}/generate-douyin-music-id-stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: localStorage.getItem('token') ? `Bearer ${localStorage.getItem('token')}` : ''
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.substring(6))

              // 忽略连接确认消息
              if (data.type === 'connected') {
                console.log('流式连接已建立:', data.message)
                continue
              }

              if (data.error && !data.completed) {
                // 处理步骤错误
                progressSteps.value.push({
                  step: data.step || currentStep.value + 1,
                  message: data.message,
                  status: 'error',
                  timestamp: new Date().toLocaleTimeString()
                })
                hasError.value = true
              } else if (data.completed) {
                // 处理完成
                if (data.error) {
                  // 最终错误
                  progressSteps.value.push({
                    step: 0,
                    message: data.error,
                    status: 'error',
                    timestamp: new Date().toLocaleTimeString()
                  })
                  hasError.value = true
                  ElMessage.error('生成失败：' + data.error)
                } else {
                  // 成功完成
                  progressSteps.value.push({
                    step: data.step || 7,
                    message: data.message || '生成完成',
                    status: 'success',
                    timestamp: new Date().toLocaleTimeString()
                  })

                  if (data.musicId) {
                    // 更新音乐的抖音音乐ID
                    music.dyMusicId = data.musicId
                    ElMessage.success('抖音音乐ID生成成功')

                    // 重新加载音乐列表以获取最新数据
                    await loadMusic()
                  }
                }
                isCompleted.value = true
              } else {
                // 处理进度步骤
                const isStarting = data.message.includes('正在')
                const isRetry = data.message.includes('重试')

                if (isStarting || isRetry) {
                  // 开始执行某个步骤或重试，显示运行状态
                  // 检查是否已存在该步骤，如果存在则更新，否则新增
                  const existingIndex = progressSteps.value.findIndex(
                    (step) => step.step === data.step && step.status !== 'success'
                  )
                  if (existingIndex >= 0) {
                    // 更新现有的运行状态步骤
                    progressSteps.value[existingIndex] = {
                      step: data.step,
                      message: data.message,
                      status: 'running',
                      timestamp: new Date().toLocaleTimeString()
                    }
                  } else {
                    // 新增运行状态步骤
                    progressSteps.value.push({
                      step: data.step,
                      message: data.message,
                      status: 'running',
                      timestamp: new Date().toLocaleTimeString()
                    })
                  }
                  currentStep.value = data.step
                } else {
                  // 步骤完成，更新为成功状态
                  const existingIndex = progressSteps.value.findIndex((step) => step.step === data.step)
                  if (existingIndex >= 0) {
                    // 更新现有步骤为成功状态
                    progressSteps.value[existingIndex] = {
                      step: data.step,
                      message: data.message,
                      status: 'success',
                      timestamp: new Date().toLocaleTimeString()
                    }
                  } else {
                    // 如果没有找到现有步骤，直接添加成功状态
                    progressSteps.value.push({
                      step: data.step,
                      message: data.message,
                      status: 'success',
                      timestamp: new Date().toLocaleTimeString()
                    })
                  }
                }
              }
            } catch (parseError) {
              console.warn('解析流数据失败:', parseError, 'Raw line:', line)
            }
          }
        }
      }
    } catch (error) {
      console.error('生成抖音音乐ID失败:', error)
      progressSteps.value.push({
        step: 0,
        message: '连接失败：' + error.message,
        status: 'error',
        timestamp: new Date().toLocaleTimeString()
      })
      hasError.value = true
      isCompleted.value = true
      ElMessage.error('生成失败：' + error.message)
    } finally {
      // 清除生成状态
      music._generating = false
    }
  }

  // 处理筛选变化
  const handleFilterChange = () => {
    // 重新加载音乐列表
    currentPage.value = 1
    loadMusic()
  }

  // 获取抖音用户列表
  const getDyUserList = async () => {
    try {
      const res = await getProductUserList()
      if (res.code === 0) {
        dyUserGroups.value = res.data.list
        // 初始化筛选结果为全部数据
        filteredDyUserGroups.value = res.data.list
      }
    } catch (err) {
      console.error('获取抖音用户列表失败:', err)
      ElMessage.error('获取抖音用户列表失败')
    }
  }

  // 抖音用户筛选方法
  const filterDyUsers = (keyword) => {
    if (!keyword) {
      filteredDyUserGroups.value = dyUserGroups.value
      return
    }

    const filtered = []
    dyUserGroups.value.forEach((group) => {
      const filteredUsers = group.dyUsers.filter(
        (user) =>
          user.nickname.toLowerCase().includes(keyword.toLowerCase()) ||
          user.uniqueId.toLowerCase().includes(keyword.toLowerCase())
      )

      if (filteredUsers.length > 0) {
        filtered.push({
          ...group,
          dyUsers: filteredUsers
        })
      }
    })

    filteredDyUserGroups.value = filtered
  }

  // 处理抖音用户选择
  const onDyUserSelected = (userId) => {
    if (!userId) {
      selectedDyUserCookie.value = ''
      return
    }

    // 直接设置用户ID，不再检查Cookie
    // Cookie将在后端调用接口时从Redis中获取
    selectedDyUserCookie.value = 'available' // 标记为可用，具体Cookie由后端处理
  }

  // 监听选中分类变化
  watch(selectedCategory, () => {
    loadMusic()
  })

  // 组件卸载前清理
  onBeforeUnmount(() => {
    if (audioTimer.value) {
      clearInterval(audioTimer.value)
    }

    if (globalAudioPlayer && playingMusicId.value) {
      globalAudioPlayer.pause()
      globalAudioPlayer.currentTime = 0
      resetPlayingStatus()
    }
  })

  // 初始挂载
  onMounted(() => {
    setupAudioListeners()
  })
</script>

<style scoped>
  .background-music {
    padding: 20px;
  }

  .section-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    border-left: 4px solid #4b6cb7;
    padding-left: 10px;
  }

  .music-list {
    margin-bottom: 20px;
  }

  .music-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 10px;
    background-color: #fff;
    transition: all 0.3s;
  }

  .music-item.playing {
    border-color: #409eff;
    box-shadow: 0 0 10px rgba(64, 158, 255, 0.1);
  }

  .music-info {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .music-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    transition: all 0.3s;
  }

  .music-icon.playing {
    background-color: #409eff;
    color: white;
  }

  .music-details {
    flex: 1;
  }

  .music-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
  }

  .music-duration {
    color: #666;
    font-size: 12px;
  }

  .music-progress {
    margin-top: 8px;
  }

  .music-time {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
  }

  .music-actions {
    display: flex;
    gap: 10px;
  }

  .music-tip {
    margin-bottom: 20px;
  }

  .add-music-btn-wrap {
    display: flex;
    justify-content: center;
    padding: 20px 0;
  }

  .add-music-btn {
    width: 200px;
    height: 50px;
  }

  /* 音乐库对话框样式 */
  .dialog-content {
    display: flex;
    height: 600px;
  }

  .dialog-sidebar {
    width: 200px;
    border-right: 1px solid #ebeef5;
    overflow-y: auto;
  }

  .category-list {
    display: flex;
    flex-direction: column;
  }

  .category-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.3s;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .category-item:hover {
    background-color: #f5f7fa;
  }

  .category-item.active {
    background-color: #ecf5ff;
    color: #409eff;
    font-weight: 500;
  }

  .category-resource-count {
    float: right;
    background-color: #f0f2f5;
    color: #909399;
    font-size: 12px;
    line-height: 1;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 5px;
  }

  .category-item.active .category-resource-count {
    background-color: #d8e6fd;
    color: #409eff;
  }

  .dialog-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .selected-count {
    color: #409eff;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .toolbar-actions {
    display: flex;
    gap: 10px;
  }

  .dialog-main {
    flex: 1;
    padding: 0 20px;
    overflow-y: auto;
  }

  .dialog-filters {
    margin-bottom: 15px;
  }

  .music-search {
    margin: 15px 0 5px;
  }

  .filter-options {
    margin-top: 10px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
  }

  .filter-row {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .filter-row:last-child {
    margin-bottom: 0;
  }

  .filter-label {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
    min-width: 120px;
  }

  .user-selection-container {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .manual-cookie-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .cookie-tip {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    color: #909399;
  }

  .cookie-tip .el-icon {
    font-size: 14px;
  }

  .music-douyin-status {
    margin: 8px 0;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .music-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
  }

  .music-grid-item {
    border: 1px solid #ebeef5;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s;
    cursor: pointer;
    position: relative;
  }

  .music-grid-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .music-grid-item.selected {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }

  .music-grid-item.playing {
    border-color: #409eff;
    background-color: #f0f9ff;
  }

  .music-grid-item.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: #f5f7fa;
  }

  .music-grid-item.disabled:hover {
    transform: none;
    box-shadow: none;
  }

  .music-thumbnail {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
  }

  .music-note-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
  }

  .music-note-icon.playing {
    background-color: #409eff;
    color: white;
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }

  .music-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .music-duration {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
  }

  .music-progress {
    margin-bottom: 8px;
  }

  .music-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .music-selection-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #409eff;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .music-pagination {
    margin-top: 0;
    margin-bottom: 15px;
    display: flex;
    justify-content: center;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }

  /* 进度对话框样式 */
  .progress-container {
    padding: 20px 0;
  }

  .progress-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
  }

  .progress-title {
    margin-left: 10px;
    font-size: 16px;
    font-weight: 500;
  }

  .loading-icon {
    color: #409eff;
    animation: rotate 2s linear infinite;
  }

  .error-icon {
    color: #f56c6c;
  }

  .success-icon {
    color: #67c23a;
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .progress-steps {
    max-height: 400px;
    overflow-y: auto;
  }

  .progress-step {
    margin-bottom: 15px;
    padding: 15px;
    border-radius: 8px;
    transition: all 0.3s;
  }

  .progress-step.running {
    background-color: #f0f9ff;
    border-left: 4px solid #409eff;
  }

  .progress-step.error {
    background-color: #fef0f0;
    border-left: 4px solid #f56c6c;
  }

  .progress-step.success {
    background-color: #f0f9f5;
    border-left: 4px solid #67c23a;
  }

  .step-info {
    display: flex;
    align-items: center;
  }

  .step-number {
    min-width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #409eff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
  }

  .step-content {
    flex: 1;
  }

  .step-message {
    font-size: 14px;
    color: #303133;
    margin-bottom: 5px;
  }

  .step-time {
    font-size: 12px;
    color: #909399;
  }

  .step-status {
    margin-left: 15px;
  }

  .running-icon {
    color: #409eff;
    animation: rotate 2s linear infinite;
  }
</style>
