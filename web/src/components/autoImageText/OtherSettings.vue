<template>
  <div class="other-settings">
    <div class="settings-container">
      <el-form label-width="120px" label-position="right">
        <!-- 基础设置 - 合并原来的生成设置、应用模式设置、内容设置 -->
        <div class="settings-group">
          <h4 class="group-title">基础设置</h4>

          <!-- 生成相关设置 -->
          <div class="sub-section">
            <div class="sub-title">生成配置</div>
            <!-- 单分组图片数量提示 -->
            <el-alert
              v-if="!props.isMultipleGroups && singleGroupImageCount !== null && singleGroupImageCount > 0"
              :title="`当前分组共有${singleGroupImageCount}张图片，最大图片数量不能超过此数量`"
              type="info"
              show-icon
              :closable="false"
              style="margin-bottom: 15px"
            />
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="生成数量" required>
                  <el-input-number
                    v-model="localSettings.generateCount"
                    :min="1"
                    :max="100"
                    :step="1"
                    placeholder="请输入生成数量"
                    @change="updateSettings"
                  />
                  <span class="field-tip">设置要生成的图文数量</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="最小图片数量" required>
                  <el-input-number
                    v-model="localSettings.minImageCount"
                    :min="imageCountLimits.min"
                    :max="imageCountLimits.max"
                    :step="1"
                    :disabled="imageCountLimits.disabled"
                    @change="updateSettings"
                  />
                  <span v-if="!props.isMultipleGroups" class="field-tip">每个图文最少包含的图片数量</span>
                  <span v-else class="field-tip multi-group-tip">{{ imageCountLimits.tip }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="最大图片数量" required>
                  <el-input-number
                    v-model="localSettings.maxImageCount"
                    :min="imageCountLimits.min"
                    :max="imageCountLimits.max"
                    :step="1"
                    :disabled="imageCountLimits.disabled"
                    @change="updateSettings"
                  />
                  <span v-if="!props.isMultipleGroups" class="field-tip">{{ imageCountLimits.tip }}</span>
                  <span v-else class="field-tip multi-group-tip">{{ imageCountLimits.tip }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 应用模式设置 -->
          <div class="sub-section">
            <div class="sub-title">应用模式</div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="标题应用模式">
                  <el-select
                    v-model="localSettings.titleApplyMode"
                    placeholder="选择标题应用模式"
                    @change="updateSettings"
                  >
                    <el-option label="仅第一张图片" :value="1" />
                    <el-option label="每张图片都加" :value="2" />
                  </el-select>
                  <span class="field-tip">标题添加到图片的方式</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="贴纸应用模式">
                  <el-select
                    v-model="localSettings.stickerApplyMode"
                    placeholder="选择贴纸应用模式"
                    @change="updateSettings"
                  >
                    <el-option label="仅第一张图片" :value="1" />
                    <el-option label="每张图片都加" :value="2" />
                  </el-select>
                  <span class="field-tip">贴纸添加到图片的方式</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 内容设置 -->
          <div class="sub-section">
            <div class="sub-title">内容描述</div>
            <el-form-item label="图文描述" required>
              <el-input
                v-model="localSettings.imageTextDescription"
                type="textarea"
                :rows="3"
                placeholder="请输入图文描述（必填）"
                @input="updateSettings"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 话题选择 -->
        <div class="settings-group">
          <h4 class="group-title">
            话题选择 (可选)
            <el-button
              v-if="localTopicSettings.coreTopics.length > 0"
              type="text"
              @click="toggleTopicExpanded"
              style="margin-left: 10px; font-size: 14px"
            >
              {{ isTopicExpanded ? '收起' : '展开' }}
              <el-icon style="margin-left: 4px">
                <ArrowDown v-if="!isTopicExpanded" />
                <ArrowUp v-if="isTopicExpanded" />
              </el-icon>
            </el-button>
          </h4>
          <el-alert
            title="选择话题分类后，可手动选择或随机选择话题，我们会从该分类下随机选择话题添加到图文中"
            type="warning"
            show-icon
            :closable="false"
            style="margin-bottom: 15px"
          />
          <div class="topic-section-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="话题分类">
                  <el-cascader
                    v-model="localTopicSettings.categoryId"
                    :options="topicCategoryOptions"
                    :props="{
                      checkStrictly: true,
                      emitPath: false,
                      value: 'ID',
                      label: 'name',
                      children: 'children',
                      expandTrigger: 'click'
                    }"
                    placeholder="选择话题分类"
                    clearable
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="选择模式">
                  <el-radio-group v-model="localTopicSettings.selectionMode" :disabled="!localTopicSettings.categoryId">
                    <el-radio value="random">随机选择</el-radio>
                    <el-radio value="manual">手动选择</el-radio>
                    <el-radio value="none">不使用话题</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 话题内容区域 - 可收缩 -->
            <div v-show="isTopicExpanded">
              <template v-if="localTopicSettings.selectionMode === 'manual'">
                <el-row :gutter="20" style="margin-bottom: 15px; align-items: center">
                  <el-col :span="16">
                    <el-form-item label="添加核心话题">
                      <el-select
                        v-model="localManualTopicToAdd"
                        placeholder="从分类中选择话题"
                        clearable
                        filterable
                        style="width: 100%"
                        :disabled="
                          !localTopicSettings.categoryId ||
                          topicOptions.length === 0 ||
                          localTopicSettings.coreTopics.length >= (localSettings.generateCount || 1)
                        "
                      >
                        <el-option v-for="item in topicOptions" :key="item.id" :label="item.name" :value="item.id" />
                        <template #empty>
                          <div style="text-align: center; color: #999; padding: 10px 0">
                            {{ localTopicSettings.categoryId ? '此分类下无话题' : '请先选择话题分类' }}
                          </div>
                        </template>
                      </el-select>
                      <span class="field-tip">
                        选择一个话题作为图文的核心主题（最多
                        {{ localSettings.generateCount || 1 }} 个）
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" style="padding-top: 5px">
                    <el-button type="primary" @click="addManualCoreTopic" :disabled="!localManualTopicToAdd">
                      <el-icon class="el-icon--left">
                        <Plus />
                      </el-icon>
                      添加
                    </el-button>
                  </el-col>
                </el-row>

                <el-row v-if="manualCoreTopics.length > 0" style="margin-bottom: 15px">
                  <el-col :span="24">
                    <el-form-item label="已选核心">
                      <div>
                        <el-tag
                          v-for="topic in manualCoreTopics"
                          :key="`manual-core-${topic.originalIndex}`"
                          type="success"
                          closable
                          @close="removeCoreTopic(topic.originalIndex)"
                          style="margin-right: 8px; margin-bottom: 4px"
                        >
                          {{ topic.name }}
                        </el-tag>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </template>

              <el-row v-if="localTopicSettings.selectionMode === 'random' && localTopicSettings.coreTopics.length > 0">
                <el-col :span="24">
                  <el-form-item label="随机选中">
                    <div>
                      <el-tag
                        v-for="(topic, index) in localTopicSettings.coreTopics"
                        :key="index"
                        type="info"
                        style="margin-right: 8px; margin-bottom: 4px"
                      >
                        {{ topic.name }}
                      </el-tag>
                      <span class="field-tip" style="line-height: normal; margin-left: 5px"
                        >(将基于这些话题从分类中随机选择)</span
                      >
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

              <template v-if="localTopicSettings.coreTopics.length > 0">
                <el-row
                  :gutter="20"
                  v-for="(coreTopic, index) in localTopicSettings.coreTopics"
                  :key="`final-topic-${index}`"
                  style="margin-bottom: 15px; padding: 15px; border: 1px dashed #dcdfe6; border-radius: 4px"
                >
                  <el-col :span="24">
                    <div style="margin-bottom: 10px; font-weight: 500">
                      <span>最终话题 #{{ index + 1 }}</span>
                      <el-tag
                        size="small"
                        :type="coreTopic.source === 'random' ? 'info' : 'success'"
                        style="margin-left: 8px"
                      >
                        源: {{ coreTopic.name }}
                      </el-tag>
                      <el-tag
                        size="small"
                        :type="coreTopic.source === 'random' ? 'warning' : 'primary'"
                        style="margin-left: 5px"
                      >
                        {{ coreTopic.source === 'random' ? '随机' : '手动' }}
                      </el-tag>
                      <el-tag size="small" type="success" style="margin-left: 5px"> 分类话题 </el-tag>
                    </div>

                    <el-form-item label-width="0">
                      <div style="display: flex; align-items: center; width: 100%">
                        <el-select
                          v-model="localTopicSettings.finalTopicGroups[index]"
                          multiple
                          :placeholder="`选择或添加话题 (最多5个)`"
                          :multiple-limit="5"
                          filterable
                          allow-create
                          default-first-option
                          :reserve-keyword="false"
                          style="flex-grow: 1; margin-right: 10px"
                        >
                          <el-option
                            v-for="topicOption in localTopicSettings.finalTopicOptions[index]"
                            :key="topicOption.challenge_info ? topicOption.challenge_info.cid : String(topicOption)"
                            :label="
                              topicOption.challenge_info
                                ? topicOption.challenge_info.challenge_name
                                : String(topicOption)
                            "
                            :value="
                              topicOption.challenge_info ? topicOption.challenge_info.cha_name : String(topicOption)
                            "
                          >
                            <div class="topic-option" v-if="topicOption.challenge_info">
                              <span>{{ topicOption.challenge_info.cha_name }}</span>
                            </div>
                            <span v-else>{{ String(topicOption) }}</span>
                          </el-option>
                          <el-option
                            v-for="customTopic in (localTopicSettings.customTopicGroups[index] || []).filter(
                              (ct) =>
                                !(localTopicSettings.finalTopicGroups[index] || []).includes(ct) &&
                                !(localTopicSettings.finalTopicOptions[index] || []).some(
                                  (st) => st.challenge_info && st.challenge_info.cha_name === ct
                                )
                            )"
                            :key="'custom-' + index + '-' + customTopic"
                            :label="customTopic"
                            :value="customTopic"
                          >
                            <div class="topic-option">
                              <span>{{ customTopic }}</span>
                              <el-tag size="small" type="success">自定义</el-tag>
                            </div>
                          </el-option>
                        </el-select>
                        <el-button type="primary" link @click="showTopicInputDialog(index)" icon="Edit"
                          >手动编辑</el-button
                        >
                        <el-button
                          v-if="coreTopic.source === 'manual'"
                          type="danger"
                          link
                          @click="removeCoreTopic(index)"
                          icon="Delete"
                          style="margin-left: 5px"
                          >移除源</el-button
                        >
                      </div>
                      <span class="field-tip" style="line-height: 1.4; margin-top: 5px">
                        将为第
                        {{ index + 1 }}
                        个图文使用这些话题（最多5个）。这些话题来自所选分类的随机选择，您可以直接输入新话题按回车创建，或通过"手动编辑"按钮管理。
                      </span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </template>
            </div>
          </div>
        </div>

        <!-- 分类设置 -->
        <div class="settings-group">
          <h4 class="group-title">分类设置</h4>

          <el-form-item label="图文分类" required>
            <el-cascader
              v-model="selectedCategories"
              :options="categoryOptions"
              :props="{
                multiple: true,
                emitPath: false,
                checkStrictly: true
              }"
              placeholder="请选择图文分类"
              clearable
              filterable
              @change="handleCategoryChange"
            />
            <span class="field-tip">选择图文所属分类</span>
          </el-form-item>
        </div>

        <!-- 伪原创设置 -->
        <div class="settings-group">
          <h4 class="group-title">
            伪原创设置
            <el-button type="text" @click="togglePseudoOriginalExpanded" style="margin-left: 10px; font-size: 14px">
              {{ isPseudoOriginalExpanded ? '收起' : '展开' }}
              <el-icon style="margin-left: 4px">
                <ArrowDown v-if="!isPseudoOriginalExpanded" />
                <ArrowUp v-if="isPseudoOriginalExpanded" />
              </el-icon>
            </el-button>
          </h4>
          <div v-show="isPseudoOriginalExpanded" class="pseudo-original-content">
            <el-alert
              title="配置图片伪原创处理参数，对每张图片进行随机的细微调整，增加原创度"
              type="warning"
              show-icon
              :closable="false"
              style="margin-bottom: 15px"
            />
            <div
              class="pseudo-original-tip"
              style="
                margin-bottom: 15px;
                padding: 10px;
                background: #f8f9fa;
                border-radius: 4px;
                font-size: 13px;
                color: #666;
              "
            >
              <strong>参数说明：</strong><br />
              • 亮度/对比度/饱和度：使用倍数形式，1.0表示不变，0.5表示减半，2.0表示加倍<br />
              • 旋转角度：单位为度，正值顺时针，负值逆时针<br />
              • 尺寸调整：使用倍数形式，1.0表示不变，0.8表示缩小到80%，1.2表示放大到120%<br />
              • 建议使用较小的调整范围以保持图片质量，同时达到伪原创效果
            </div>
            <!-- 亮度调整 -->
            <div class="sub-section">
              <div class="sub-title">亮度调整</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="最小值">
                    <div class="slider-container">
                      <el-slider
                        v-model="localPseudoOriginalSettings.brightnessMin"
                        :min="0.1"
                        :max="2.0"
                        :step="0.01"
                        show-input
                        :show-input-controls="false"
                        @change="updatePseudoOriginalSettings"
                      />
                    </div>
                    <span class="field-tip">建议范围: 0.95 - 1.0，极值: 0.1 - 2.0（对应 -90% 到 +100% 调整）</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="最大值">
                    <div class="slider-container">
                      <el-slider
                        v-model="localPseudoOriginalSettings.brightnessMax"
                        :min="0.1"
                        :max="2.0"
                        :step="0.01"
                        show-input
                        :show-input-controls="false"
                        @change="updatePseudoOriginalSettings"
                      />
                    </div>
                    <span class="field-tip">建议范围: 1.0 - 1.05，极值: 0.1 - 2.0（对应 -90% 到 +100% 调整）</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 对比度调整 -->
            <div class="sub-section">
              <div class="sub-title">对比度调整</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="最小值">
                    <div class="slider-container">
                      <el-slider
                        v-model="localPseudoOriginalSettings.contrastMin"
                        :min="0.1"
                        :max="2.0"
                        :step="0.01"
                        show-input
                        :show-input-controls="false"
                        @change="updatePseudoOriginalSettings"
                      />
                    </div>
                    <span class="field-tip">建议范围: 0.98 - 1.0，极值: 0.1 - 2.0（对应 -90% 到 +100% 调整）</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="最大值">
                    <div class="slider-container">
                      <el-slider
                        v-model="localPseudoOriginalSettings.contrastMax"
                        :min="0.1"
                        :max="2.0"
                        :step="0.01"
                        show-input
                        :show-input-controls="false"
                        @change="updatePseudoOriginalSettings"
                      />
                    </div>
                    <span class="field-tip">建议范围: 1.0 - 1.02，极值: 0.1 - 2.0（对应 -90% 到 +100% 调整）</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 饱和度调整 -->
            <div class="sub-section">
              <div class="sub-title">饱和度调整</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="最小值">
                    <div class="slider-container">
                      <el-slider
                        v-model="localPseudoOriginalSettings.saturationMin"
                        :min="0.1"
                        :max="2.0"
                        :step="0.01"
                        show-input
                        :show-input-controls="false"
                        @change="updatePseudoOriginalSettings"
                      />
                    </div>
                    <span class="field-tip">建议范围: 0.98 - 1.0，极值: 0.1 - 2.0（对应 -90% 到 +100% 调整）</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="最大值">
                    <div class="slider-container">
                      <el-slider
                        v-model="localPseudoOriginalSettings.saturationMax"
                        :min="0.1"
                        :max="2.0"
                        :step="0.01"
                        show-input
                        :show-input-controls="false"
                        @change="updatePseudoOriginalSettings"
                      />
                    </div>
                    <span class="field-tip">建议范围: 1.0 - 1.02，极值: 0.1 - 2.0（对应 -90% 到 +100% 调整）</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 旋转角度调整 -->
            <div class="sub-section">
              <div class="sub-title">旋转角度调整</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="最小值 (度)">
                    <div class="slider-container">
                      <el-slider
                        v-model="localPseudoOriginalSettings.rotationMin"
                        :min="-30"
                        :max="30"
                        :step="0.1"
                        show-input
                        :show-input-controls="false"
                        @change="updatePseudoOriginalSettings"
                      />
                    </div>
                    <span class="field-tip">建议范围: -0.5 - 0，极值: -30 - 30（角度）</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="最大值 (度)">
                    <div class="slider-container">
                      <el-slider
                        v-model="localPseudoOriginalSettings.rotationMax"
                        :min="-30"
                        :max="30"
                        :step="0.1"
                        show-input
                        :show-input-controls="false"
                        @change="updatePseudoOriginalSettings"
                      />
                    </div>
                    <span class="field-tip">建议范围: 0 - 0.5，极值: -30 - 30（角度）</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 尺寸调整 -->
            <div class="sub-section">
              <div class="sub-title">尺寸调整</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="最小值">
                    <div class="slider-container">
                      <el-slider
                        v-model="localPseudoOriginalSettings.sizeMin"
                        :min="0.5"
                        :max="2.0"
                        :step="0.01"
                        show-input
                        :show-input-controls="false"
                        @change="updatePseudoOriginalSettings"
                      />
                    </div>
                    <span class="field-tip">建议范围: 0.99 - 1.0，极值: 0.5 - 2.0（倍数）</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="最大值">
                    <div class="slider-container">
                      <el-slider
                        v-model="localPseudoOriginalSettings.sizeMax"
                        :min="0.5"
                        :max="2.0"
                        :step="0.01"
                        show-input
                        :show-input-controls="false"
                        @change="updatePseudoOriginalSettings"
                      />
                    </div>
                    <span class="field-tip">建议范围: 1.0 - 1.01，极值: 0.5 - 2.0（倍数）</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </el-form>

      <!-- 话题编辑对话框 -->
      <el-dialog v-model="topicInputDialogVisible" title="手动编辑话题" width="500px" append-to-body destroy-on-close>
        <div class="custom-topic-input">
          <el-form :model="topicInputForm" ref="topicInputFormRef">
            <el-form-item>
              <div class="topic-input-tip">请输入话题 (源: {{ editingTopicSourceName }}), 每行一个，最多5个</div>
              <el-input
                type="textarea"
                v-model="topicInputForm.topics"
                :rows="5"
                placeholder="每行输入一个话题，不需要添加#号"
              ></el-input>
            </el-form-item>
            <div class="topic-preview" v-if="topicInputPreview.length > 0">
              <div class="preview-label">预览：</div>
              <div class="topic-tags-preview">
                <el-tag
                  v-for="(topic, index) in topicInputPreview"
                  :key="index"
                  type="warning"
                  size="small"
                  class="topic-tag"
                >
                  #{{ topic }}
                </el-tag>
              </div>
            </div>
          </el-form>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="topicInputDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="addCustomTopics">确定</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, watch, computed } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Plus, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
  import { getTopicList } from '@/api/media/topic'

  const props = defineProps({
    settings: {
      type: Object,
      default: () => ({})
    },
    pseudoOriginalSettings: {
      type: Object,
      default: () => ({})
    },
    selectedCategories: {
      type: Array,
      default: () => []
    },
    categoryOptions: {
      type: Array,
      default: () => []
    },
    isMultipleGroups: {
      type: Boolean,
      default: false
    },
    imageMaterials: {
      type: Array,
      default: () => []
    },
    topicCategoryOptions: {
      type: Array,
      default: () => []
    }
  })

  const emit = defineEmits([
    'update:settings',
    'update:pseudoOriginalSettings',
    'update:selectedCategories',
    'update:topicSettings'
  ])

  // 本地设置数据
  const localSettings = reactive({
    generateCount: props.settings.generateCount || undefined,
    minImageCount: props.settings.minImageCount || 3,
    maxImageCount: props.settings.maxImageCount || 9,
    titleApplyMode: props.settings.titleApplyMode || 1,
    stickerApplyMode: props.settings.stickerApplyMode || 1,
    imageTextDescription: props.settings.imageTextDescription || ''
  })

  // 话题设置
  const localTopicSettings = reactive({
    categoryId: null,
    selectionMode: 'random',
    coreTopics: [],
    finalTopicGroups: [],
    finalTopicOptions: [],
    customTopicGroups: [],
    searchStatus: []
  })

  // 本地分类选择
  const selectedCategories = ref([...props.selectedCategories])

  // 话题相关状态
  const topicOptions = ref([])
  const localManualTopicToAdd = ref(null)
  const isTopicExpanded = ref(true)

  // 伪原创设置相关状态
  const isPseudoOriginalExpanded = ref(false)
  const localPseudoOriginalSettings = reactive({
    brightnessMin: props.pseudoOriginalSettings.brightnessMin || 0.9,
    brightnessMax: props.pseudoOriginalSettings.brightnessMax || 1.1,
    contrastMin: props.pseudoOriginalSettings.contrastMin || 0.95,
    contrastMax: props.pseudoOriginalSettings.contrastMax || 1.05,
    saturationMin: props.pseudoOriginalSettings.saturationMin || 0.95,
    saturationMax: props.pseudoOriginalSettings.saturationMax || 1.05,
    rotationMin: props.pseudoOriginalSettings.rotationMin || -0.1,
    rotationMax: props.pseudoOriginalSettings.rotationMax || 0.1,
    sizeMin: props.pseudoOriginalSettings.sizeMin || 0.98,
    sizeMax: props.pseudoOriginalSettings.sizeMax || 1.02
  })

  // 对话框状态
  const topicInputDialogVisible = ref(false)
  const topicInputForm = ref({ topics: '' })
  const editingTopicIndex = ref(null)
  const editingTopicSourceName = ref('')

  // 计算单分组中的图片数量
  const singleGroupImageCount = computed(() => {
    if (props.isMultipleGroups || !props.imageMaterials || props.imageMaterials.length === 0) {
      return null // 多分组模式或无分组时返回null
    }

    const firstGroup = props.imageMaterials[0]
    if (!firstGroup) return 0

    let count = 0

    // 计算主图片
    if (firstGroup.mediaUrl || firstGroup.mediaId) {
      count += 1
    }

    // 计算额外图片
    if (firstGroup.extraMedia && Array.isArray(firstGroup.extraMedia)) {
      count += firstGroup.extraMedia.length
    }

    return count
  })

  // 计算最小/最大图片数量的限制
  const imageCountLimits = computed(() => {
    if (props.isMultipleGroups) {
      const groupCount = props.imageMaterials.length
      return {
        min: groupCount,
        max: groupCount,
        disabled: true,
        tip: '多分组模式下自动设为分组数量'
      }
    } else {
      const maxAvailable = singleGroupImageCount.value || 20
      return {
        min: 1,
        max: maxAvailable,
        disabled: false,
        tip: `每个图文最多包含的图片数量（当前分组最多${maxAvailable}张）`
      }
    }
  })

  // 计算属性
  const manualCoreTopics = computed(() => {
    return localTopicSettings.coreTopics
      .map((topic, index) => ({ ...topic, originalIndex: index }))
      .filter((topic) => topic.source === 'manual')
  })

  const topicInputPreview = computed(() => {
    if (!topicInputForm.value.topics) return []
    return topicInputForm.value.topics
      .split('\n')
      .map((topic) => topic.trim())
      .filter((topic) => topic.length > 0)
      .slice(0, 5)
  })

  // 更新设置
  const updateSettings = () => {
    emit('update:settings', { ...localSettings })
  }

  // 处理分类变化
  const handleCategoryChange = (categories) => {
    selectedCategories.value = categories || []
    emit('update:selectedCategories', selectedCategories.value)
  }

  // 话题相关方法
  const loadTopicsByCategory = async (categoryId) => {
    if (!categoryId) {
      topicOptions.value = []
      localTopicSettings.coreTopics = []
      return
    }
    try {
      const response = await getTopicList({ categoryId: categoryId, page: 1, pageSize: 999 })
      if (response.code === 0 && response.data && response.data.list) {
        topicOptions.value = response.data.list.map((item) => ({
          id: item.ID,
          name: item.content
        }))
      } else {
        topicOptions.value = []
        ElMessage.warning('获取话题列表失败: ' + (response.message || '未找到话题'))
      }
    } catch (error) {
      console.error('获取话题列表失败:', error)
      topicOptions.value = []
      ElMessage.warning('获取话题列表失败')
    }
  }

  const clearCoreTopicState = () => {
    localTopicSettings.coreTopics = []
    localTopicSettings.finalTopicGroups = []
    localTopicSettings.finalTopicOptions = []
    localTopicSettings.customTopicGroups = []
    localTopicSettings.searchStatus = []
    localManualTopicToAdd.value = null
    topicOptions.value = []
  }

  const selectRandomTopic = async () => {
    const categoryId = localTopicSettings.categoryId
    const generateCount = localSettings.generateCount || 1

    clearCoreTopicState()

    if (!categoryId) {
      ElMessage.warning('请先选择一个话题分类')
      localTopicSettings.selectionMode = 'none'
      return
    }

    if (topicOptions.value.length === 0) {
      await loadTopicsByCategory(categoryId)
      if (topicOptions.value.length === 0) {
        ElMessage.warning('该分类下没有可用的话题')
        localTopicSettings.selectionMode = 'none'
        return
      }
    }

    const availableTopics = topicOptions.value
    const selectedCoreTopics = []
    const initialFinalTopicGroups = []
    const initialFinalTopicOptions = []
    const initialCustomTopicGroups = []
    const initialSearchStatus = []

    for (let i = 0; i < generateCount; i++) {
      const randomIndex = Math.floor(Math.random() * availableTopics.length)
      const randomTopic = availableTopics[randomIndex]

      if (randomTopic && randomTopic.name) {
        const coreTopic = { name: randomTopic.name, id: randomTopic.id, source: 'random' }
        selectedCoreTopics.push(coreTopic)
        initialFinalTopicGroups.push([])
        initialFinalTopicOptions.push([])
        initialCustomTopicGroups.push([])
        initialSearchStatus.push('idle')
      } else {
        selectedCoreTopics.push({ name: '选择失败', id: null, source: 'random' })
        initialFinalTopicGroups.push([])
        initialFinalTopicOptions.push([])
        initialCustomTopicGroups.push([])
        initialSearchStatus.push('error')
      }
    }

    localTopicSettings.coreTopics = selectedCoreTopics
    localTopicSettings.finalTopicGroups = initialFinalTopicGroups
    localTopicSettings.finalTopicOptions = initialFinalTopicOptions
    localTopicSettings.customTopicGroups = initialCustomTopicGroups
    localTopicSettings.searchStatus = initialSearchStatus

    // 为每个核心话题生成随机话题
    selectedCoreTopics.forEach((_, index) => {
      generateRandomTopicsForIndex(index)
    })
  }

  const generateRandomTopicsForIndex = (index) => {
    if (topicOptions.value.length > 0) {
      const shuffledTopics = [...topicOptions.value].sort(() => 0.5 - Math.random())
      const countToPick = Math.min(5, shuffledTopics.length)
      const randomTopics = shuffledTopics.slice(0, countToPick).map((t) => t.name)

      if (!localTopicSettings.finalTopicGroups[index]) localTopicSettings.finalTopicGroups[index] = []
      if (!localTopicSettings.finalTopicOptions[index]) localTopicSettings.finalTopicOptions[index] = []
      if (!localTopicSettings.customTopicGroups[index]) localTopicSettings.customTopicGroups[index] = []

      localTopicSettings.finalTopicGroups[index] = randomTopics
      localTopicSettings.finalTopicOptions[index] = []
      localTopicSettings.customTopicGroups[index] = []
      localTopicSettings.searchStatus[index] = 'success'
    } else {
      // 生成通用备用话题
      const coreTopic = localTopicSettings.coreTopics[index]
      const coreTopicName = coreTopic ? coreTopic.name : ''
      const fallbackTopics = generateGenericFallbackTopics(coreTopicName)

      localTopicSettings.finalTopicGroups[index] = fallbackTopics
      localTopicSettings.finalTopicOptions[index] = []
      localTopicSettings.customTopicGroups[index] = []
      localTopicSettings.searchStatus[index] = 'success'
    }
  }

  const generateGenericFallbackTopics = (coreTopicName) => {
    const genericTopics = ['热门推荐', '每日分享', '生活小技巧', '实用干货', '精彩内容']

    // 如果有核心话题名称，将其作为第一个话题
    if (coreTopicName && coreTopicName.trim()) {
      return [coreTopicName.trim(), ...genericTopics.slice(0, 4)]
    }

    return genericTopics
  }

  const addManualCoreTopic = () => {
    const topicId = localManualTopicToAdd.value
    if (!topicId) {
      ElMessage.warning('请先从下拉列表中选择一个话题')
      return
    }

    const generateCount = localSettings.generateCount || 1
    if (localTopicSettings.coreTopics.length >= generateCount) {
      ElMessage.warning(`最多只能添加 ${generateCount} 个核心话题`)
      return
    }

    const selectedTopic = topicOptions.value.find((t) => t.id === topicId)
    if (!selectedTopic || !selectedTopic.name) {
      ElMessage.error('选择的话题无效或未找到')
      return
    }

    const coreTopic = { name: selectedTopic.name, id: selectedTopic.id, source: 'manual' }

    localTopicSettings.coreTopics.push(coreTopic)
    localTopicSettings.finalTopicGroups.push([])
    localTopicSettings.finalTopicOptions.push([])
    localTopicSettings.customTopicGroups.push([])
    localTopicSettings.searchStatus.push('idle')

    localManualTopicToAdd.value = null

    generateRandomTopicsForIndex(localTopicSettings.coreTopics.length - 1)
  }

  const removeCoreTopic = (index) => {
    if (index < 0 || index >= localTopicSettings.coreTopics.length) return

    localTopicSettings.coreTopics.splice(index, 1)
    localTopicSettings.finalTopicGroups.splice(index, 1)
    localTopicSettings.finalTopicOptions.splice(index, 1)
    localTopicSettings.customTopicGroups.splice(index, 1)
    localTopicSettings.searchStatus.splice(index, 1)
  }

  const showTopicInputDialog = (index) => {
    editingTopicIndex.value = index

    let currentTopics = []
    const finalGroup = localTopicSettings.finalTopicGroups[index] || []
    const customGroup = localTopicSettings.customTopicGroups[index] || []
    currentTopics = [...finalGroup.filter((t) => !customGroup.includes(t)), ...customGroup]
    editingTopicSourceName.value = localTopicSettings.coreTopics[index]?.name || `话题 #${index + 1}`

    topicInputForm.value.topics = currentTopics.join('\n')
    topicInputDialogVisible.value = true
  }

  const addCustomTopics = () => {
    const topics = topicInputForm.value.topics
      .split('\n')
      .map((topic) => topic.trim())
      .filter((topic) => topic.length > 0 && topic.length <= 50)

    if (topics.length > 5) {
      ElMessage.warning('最多只能添加5个话题')
      return
    }

    const finalTopics = [...new Set(topics)]
    const index = editingTopicIndex.value

    if (!localTopicSettings.customTopicGroups[index]) localTopicSettings.customTopicGroups[index] = []
    if (!localTopicSettings.finalTopicGroups[index]) localTopicSettings.finalTopicGroups[index] = []

    localTopicSettings.customTopicGroups[index] = finalTopics
    localTopicSettings.finalTopicGroups[index] = finalTopics

    editingTopicIndex.value = null
    topicInputDialogVisible.value = false
    ElMessage.success(`已${finalTopics.length > 0 ? '更新' : '清空'}话题 (源: ${editingTopicSourceName.value})`)
  }

  const toggleTopicExpanded = () => {
    isTopicExpanded.value = !isTopicExpanded.value
  }

  // 切换伪原创设置展开状态
  const togglePseudoOriginalExpanded = () => {
    isPseudoOriginalExpanded.value = !isPseudoOriginalExpanded.value
  }

  // 更新伪原创设置
  const updatePseudoOriginalSettings = () => {
    emit('update:pseudoOriginalSettings', { ...localPseudoOriginalSettings })
  }

  // 监听话题分类变化
  watch(
    () => localTopicSettings.categoryId,
    (newCategoryId) => {
      clearCoreTopicState()

      if (!newCategoryId) {
        localTopicSettings.selectionMode = 'none'
        return
      }

      loadTopicsByCategory(newCategoryId).then(() => {
        if (localTopicSettings.selectionMode === 'random') {
          selectRandomTopic()
        }
      })
    }
  )

  // 监听选择模式变化
  watch(
    () => localTopicSettings.selectionMode,
    (newMode) => {
      clearCoreTopicState()

      if (!localTopicSettings.categoryId) {
        return
      }

      loadTopicsByCategory(localTopicSettings.categoryId).then(() => {
        if (newMode === 'random') {
          selectRandomTopic()
        }
      })
    }
  )

  // 监听图片数量限制变化，自动调整设置值
  watch(
    () => imageCountLimits.value,
    (newLimits) => {
      if (newLimits.disabled) {
        // 多分组模式，自动设置为分组数量
        localSettings.minImageCount = newLimits.min
        localSettings.maxImageCount = newLimits.max
        updateSettings()
      } else {
        // 单分组模式，检查当前设置是否超出限制
        let changed = false

        if (localSettings.minImageCount > newLimits.max) {
          localSettings.minImageCount = newLimits.max
          changed = true
        }

        if (localSettings.maxImageCount > newLimits.max) {
          localSettings.maxImageCount = newLimits.max
          changed = true
        }

        if (localSettings.minImageCount < newLimits.min) {
          localSettings.minImageCount = newLimits.min
          changed = true
        }

        if (changed) {
          updateSettings()
        }
      }
    },
    { deep: true, immediate: true }
  )

  // 监听核心话题数量变化，自动收缩
  watch(
    () => localTopicSettings.coreTopics.length,
    (newLength, oldLength) => {
      // 当有话题生成时（从0变为>0），自动收缩
      if (oldLength === 0 && newLength > 0) {
        isTopicExpanded.value = false
      }
    }
  )

  // 监听props变化，更新本地数据
  watch(
    () => props.settings,
    (newSettings) => {
      Object.assign(localSettings, {
        generateCount: newSettings.generateCount || undefined,
        minImageCount: newSettings.minImageCount || 3,
        maxImageCount: newSettings.maxImageCount || 9,
        titleApplyMode: newSettings.titleApplyMode || 1,
        stickerApplyMode: newSettings.stickerApplyMode || 1,
        imageTextDescription: newSettings.imageTextDescription || ''
      })
    },
    { deep: true }
  )

  watch(
    () => props.selectedCategories,
    (newCategories) => {
      selectedCategories.value = [...newCategories]
    },
    { deep: true }
  )

  watch(
    () => props.pseudoOriginalSettings,
    (newSettings) => {
      Object.assign(localPseudoOriginalSettings, {
        brightnessMin: newSettings.brightnessMin || 0.9,
        brightnessMax: newSettings.brightnessMax || 1.1,
        contrastMin: newSettings.contrastMin || 0.95,
        contrastMax: newSettings.contrastMax || 1.05,
        saturationMin: newSettings.saturationMin || 0.95,
        saturationMax: newSettings.saturationMax || 1.05,
        rotationMin: newSettings.rotationMin || -0.1,
        rotationMax: newSettings.rotationMax || 0.1,
        sizeMin: newSettings.sizeMin || 0.98,
        sizeMax: newSettings.sizeMax || 1.02
      })
    },
    { deep: true }
  )

  // 发送话题设置更新
  watch(
    localTopicSettings,
    (newVal) => {
      emit('update:topicSettings', { ...newVal })
    },
    { deep: true }
  )
</script>

<style scoped>
  .other-settings {
    padding: 20px;
  }

  .section-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    border-left: 4px solid #4b6cb7;
    padding-left: 10px;
  }

  .settings-container {
    background-color: #fff;
  }

  .settings-group {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #fafafa;
  }

  .group-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 20px 0;
    color: #333;
    border-left: 3px solid #409eff;
    padding-left: 10px;
  }

  .sub-section {
    margin-bottom: 25px;
    padding: 15px;
    border: 1px solid #e8eaec;
    border-radius: 6px;
    background-color: #ffffff;
  }

  .sub-section:last-child {
    margin-bottom: 0;
  }

  .sub-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 15px;
    color: #606266;
    border-left: 2px solid #67c23a;
    padding-left: 8px;
  }

  .field-tip {
    display: block;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    line-height: 1.4;
  }

  .multi-group-tip {
    color: #e6a23c !important;
    font-weight: 500;
  }

  .topic-section-content {
    background-color: #fff;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e4e7ed;
  }

  .pseudo-original-content {
    background-color: #fff;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e4e7ed;
  }

  .custom-topic-input {
    padding: 0px;
  }

  .topic-input-tip {
    margin-bottom: 10px;
    font-size: 14px;
    color: #606266;
  }

  .topic-preview {
    margin-top: 15px;
    padding: 10px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #f8f8f8;
  }

  .preview-label {
    font-weight: 500;
    margin-bottom: 10px;
    color: #606266;
  }

  .topic-tags-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }

  .topic-tags-preview .topic-tag {
    margin-bottom: 4px;
  }

  .topic-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  :deep(.el-form-item) {
    margin-bottom: 20px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #333;
  }

  :deep(.el-input-number) {
    width: 200px;
  }

  :deep(.el-select) {
    width: 250px;
  }

  :deep(.el-cascader) {
    width: 300px;
  }

  :deep(.el-textarea) {
    width: 100%;
  }

  .slider-container {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  :deep(.slider-container .el-slider) {
    flex: 1;
    margin-right: 0;
  }

  :deep(.slider-container .el-slider__input) {
    width: 90px;
    flex-shrink: 0;
  }

  :deep(.slider-container .el-slider__input .el-input__inner) {
    text-align: center;
    font-size: 12px;
    padding: 0 8px;
    height: 32px;
    line-height: 32px;
  }

  :deep(.slider-container .el-slider__runway) {
    height: 6px;
  }

  :deep(.slider-container .el-slider__button) {
    width: 16px;
    height: 16px;
    border: 2px solid #409eff;
  }

  :deep(.slider-container .el-slider__bar) {
    height: 6px;
    background-color: #409eff;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .other-settings {
      padding: 15px;
    }

    .settings-group {
      padding: 15px;
      margin-bottom: 20px;
    }

    :deep(.el-form) {
      padding: 0;
    }

    :deep(.el-input-number),
    :deep(.el-select),
    :deep(.el-cascader) {
      width: 100%;
      max-width: 300px;
    }

    .slider-container {
      flex-direction: column;
      align-items: stretch;
      gap: 8px;
    }

    :deep(.slider-container .el-slider) {
      margin-bottom: 5px;
    }

    :deep(.slider-container .el-slider__input) {
      width: 100%;
      max-width: 120px;
      align-self: center;
    }
  }
</style>
