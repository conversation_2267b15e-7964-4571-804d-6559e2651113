<template>
  <el-dialog v-model="dialogVisible" title="文案库选择" width="70%" class="copywriting-dialog">
    <div class="copywriting-dialog-content">
      <el-form label-width="100px">
        <el-form-item label="选择分类">
          <el-cascader
            v-model="selectedCategory"
            :options="categoryTreeWithCounts"
            :props="cascaderProps"
            placeholder="请选择文案分类"
            clearable
            style="width: 100%"
            :show-all-levels="false"
            separator=" > "
          />
        </el-form-item>
        <el-form-item label="选择模式">
          <el-radio-group v-model="selectMode" @change="handleModeChange">
            <el-radio label="random">随机文案</el-radio>
            <el-radio label="latest">最新文案</el-radio>
            <el-radio label="manual">手动选择</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="选择数量" v-if="selectMode !== 'manual'">
          <div class="quantity-selection">
            <el-input-number v-model="selectQuantity" :min="1" :max="total > 0 ? total : 1" :disabled="selectAll" />
            <el-checkbox
              v-model="selectAll"
              label="选择全部"
              style="margin-left: 15px"
              @change="handleSelectAllChange"
              :disabled="total <= 0"
            />
          </div>
          <div v-if="total === 0 && selectedCategory !== ''" class="no-copywriting-tip">该分类下暂无文案</div>
        </el-form-item>
      </el-form>

      <el-divider />

      <!-- 手动选择列表区域 -->
      <div v-if="selectMode === 'manual'" class="manual-selection-area">
        <div class="manual-selection-header">
          <h4>选择文案 ({{ total }} 条)</h4>
          <el-checkbox v-model="isAllManuallySelected" @change="handleManualSelectAllChange">全选</el-checkbox>
        </div>
        <div v-if="isLoading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>
        <div v-else-if="fetchedCopywriting.length > 0" class="copywriting-list-manual">
          <div
            v-for="item in fetchedCopywriting"
            :key="item.ID"
            class="copywriting-item"
            :class="{ selected: manuallySelectedIds.includes(item.ID) }"
            @click="toggleManualSelection(item.ID)"
          >
            <div class="copywriting-content">{{ item.content }}</div>
            <div v-if="manuallySelectedIds.includes(item.ID)" class="copywriting-selected-icon">
              <el-icon>
                <Check />
              </el-icon>
            </div>
          </div>
        </div>
        <div v-else-if="selectedCategory !== ''">
          <p>此分类没有可用文案。</p>
        </div>
        <div v-else>
          <p>请先选择分类。</p>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="isConfirmDisabled"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, computed, watch } from 'vue'
  import { Check } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import {
    getCopywritingCategoryList,
    getCopywritingList as fetchCopywritingApi,
    getCopywritingCategoryCounts
  } from '@/api/media/copywriting'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      required: true
    },
    savedParams: {
      type: Object,
      default: () => ({})
    }
  })

  const emit = defineEmits(['update:modelValue', 'confirm'])

  // 对话框可见性
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
  })

  // 文案库选择相关状态
  const categoryTree = ref([])
  const categoryCounts = ref({})
  const categoryTreeWithCounts = computed(() => {
    // 为分类树添加计数信息
    const addCounts = (categories) => {
      return categories.map((category) => ({
        ...category,
        label: `${category.name} (${categoryCounts.value[category.ID] || 0})`,
        children: category.children ? addCounts(category.children) : []
      }))
    }
    return addCounts(categoryTree.value)
  })

  const cascaderProps = {
    label: 'label',
    value: 'ID',
    children: 'children',
    emitPath: false // 只返回选中节点的值，不返回完整路径
  }
  const selectedCategory = ref('')
  const selectMode = ref('random')
  const selectQuantity = ref(1)
  const selectAll = ref(false)
  const fetchedCopywriting = ref([])
  const total = ref(0)
  const manuallySelectedIds = ref([])
  const isLoading = ref(false)
  const isAllManuallySelected = ref(false)

  // 计算确定按钮是否禁用
  const isConfirmDisabled = computed(() => {
    if (selectMode.value === 'manual') {
      return manuallySelectedIds.value.length === 0
    } else {
      return fetchedCopywriting.value.length === 0
    }
  })

  // 加载文案分类计数
  const loadCopywritingCategoryCounts = async () => {
    try {
      const res = await getCopywritingCategoryCounts()
      if (res.code === 0 && res.data) {
        categoryCounts.value = res.data
      } else {
        ElMessage.error('获取文案分类计数失败: ' + (res.msg || '未知错误'))
      }
    } catch (error) {
      console.error('获取文案分类计数异常:', error)
      ElMessage.error('获取文案分类计数失败: ' + (error?.message || '网络错误'))
    }
  }

  // 加载文案分类
  const loadCopywritingCategories = async () => {
    try {
      const res = await getCopywritingCategoryList()
      if (res.code === 0 && res.data) {
        categoryTree.value = res.data // 直接用树形结构
        // 加载完分类后加载计数
        await loadCopywritingCategoryCounts()
      } else {
        ElMessage.error('获取文案分类失败: ' + (res.msg || '未知错误'))
      }
    } catch (error) {
      console.error('获取文案分类异常:', error)
      ElMessage.error('获取文案分类失败: ' + (error?.message || '网络错误'))
    }
  }

  // 处理模式变化
  const handleModeChange = () => {
    if (selectMode.value === 'manual') {
      selectAll.value = false
      selectQuantity.value = 1
    }
    fetchCopywritingFromLibrary()
  }

  // 处理全选变化
  const handleSelectAllChange = (value) => {
    if (value) {
      if (total.value > 0) {
        selectQuantity.value = total.value
        fetchCopywritingFromLibrary()
      } else {
        selectAll.value = false
        selectQuantity.value = 1
      }
    } else {
      selectQuantity.value = 1
    }
  }

  // 从库中获取文案
  const fetchCopywritingFromLibrary = async () => {
    if (!selectedCategory.value) {
      fetchedCopywriting.value = []
      total.value = 0
      manuallySelectedIds.value = []
      return
    }

    isLoading.value = true

    try {
      const countRes = await fetchCopywritingApi({
        categoryId: parseInt(selectedCategory.value, 10) || 0,
        page: 1,
        pageSize: 1
      })

      if (countRes.code === 0 && countRes.data) {
        total.value = countRes.data.total || 0
        if (total.value === 0) {
          fetchedCopywriting.value = []
          manuallySelectedIds.value = []
          ElMessage.info('该分类下没有文案')
          isLoading.value = false
          return
        }
      } else {
        total.value = 0
        fetchedCopywriting.value = []
        manuallySelectedIds.value = []
        ElMessage.error('获取文案总数失败: ' + (countRes.msg || '未知错误'))
        isLoading.value = false
        return
      }
    } catch (error) {
      console.error('获取文案总数异常:', error)
      ElMessage.error('获取文案总数失败: ' + (error?.message || '网络错误'))
      total.value = 0
      fetchedCopywriting.value = []
      manuallySelectedIds.value = []
      isLoading.value = false
      return
    }

    let quantityToFetch

    if (selectMode.value === 'manual') {
      quantityToFetch = total.value
    } else {
      quantityToFetch = selectAll.value ? total.value : selectQuantity.value
    }

    try {
      const res = await fetchCopywritingApi({
        categoryId: parseInt(selectedCategory.value, 10) || 0,
        page: 1,
        pageSize: quantityToFetch,
        mode: selectMode.value
      })

      if (res.code === 0 && res.data) {
        fetchedCopywriting.value = res.data.list || []
        if (selectMode.value === 'manual') {
          manuallySelectedIds.value = []
        }
      } else {
        fetchedCopywriting.value = []
        manuallySelectedIds.value = []
        ElMessage.error('获取文案列表失败: ' + (res.msg || '未知错误'))
      }
    } catch (error) {
      console.error('获取文案列表异常:', error)
      ElMessage.error('获取文案列表失败: ' + (error?.message || '网络错误'))
      fetchedCopywriting.value = []
      manuallySelectedIds.value = []
    } finally {
      isLoading.value = false
    }
  }

  // 处理手动全选/取消全选
  const handleManualSelectAllChange = (value) => {
    if (value) {
      manuallySelectedIds.value = fetchedCopywriting.value.map((item) => item.ID)
    } else {
      manuallySelectedIds.value = []
    }
  }

  // 切换手动选择
  const toggleManualSelection = (id) => {
    const index = manuallySelectedIds.value.indexOf(id)
    if (index === -1) {
      manuallySelectedIds.value.push(id)
    } else {
      manuallySelectedIds.value.splice(index, 1)
    }
  }

  // 处理取消
  const handleCancel = () => {
    dialogVisible.value = false
  }

  // 处理确认
  const handleConfirm = () => {
    let selectedContents = []
    if (selectMode.value === 'manual') {
      selectedContents = fetchedCopywriting.value
        .filter((item) => manuallySelectedIds.value.includes(item.ID))
        .map((item) => item.content)
    } else {
      selectedContents = fetchedCopywriting.value.map((item) => item.content)
    }

    if (selectedContents.length === 0) {
      ElMessage.warning('请至少选择一条文案')
      return
    }

    emit('confirm', {
      contents: selectedContents,
      params: {
        categoryId: selectedCategory.value,
        mode: selectMode.value,
        quantity: selectQuantity.value,
        ids: manuallySelectedIds.value
      }
    })
  }

  // 监听对话框打开
  watch(
    () => props.modelValue,
    async (newVal) => {
      if (newVal) {
        // 重置状态
        selectedCategory.value = ''
        selectMode.value = 'random'
        selectQuantity.value = 1
        selectAll.value = false
        fetchedCopywriting.value = []
        total.value = 0
        manuallySelectedIds.value = []
        isLoading.value = false

        await loadCopywritingCategories()

        // 恢复保存的参数
        if (props.savedParams) {
          selectedCategory.value = props.savedParams.categoryId || ''
          selectMode.value = props.savedParams.mode || 'random'

          if (props.savedParams.mode === 'manual') {
            manuallySelectedIds.value = props.savedParams.ids || []
          } else {
            selectQuantity.value = props.savedParams.quantity || 1
          }
          fetchCopywritingFromLibrary()
        }
      }
    }
  )

  // 监听选择变化
  watch([selectedCategory, selectMode, selectQuantity], (newValues, oldValues) => {
    const [newCategory, newMode] = newValues
    const [oldCategory, oldMode] = oldValues

    if (newCategory !== oldCategory) {
      fetchCopywritingFromLibrary()
      return
    }

    if (newMode !== oldMode) {
      return
    }

    if (selectMode.value !== 'manual' && !selectAll.value) {
      fetchCopywritingFromLibrary()
    }
  })

  // 监听手动选择列表的变化
  watch(
    manuallySelectedIds,
    (newVal) => {
      if (selectMode.value === 'manual' && fetchedCopywriting.value.length > 0) {
        isAllManuallySelected.value = newVal.length === fetchedCopywriting.value.length
      } else if (selectMode.value === 'manual') {
        isAllManuallySelected.value = false
      }
    },
    { deep: true }
  )
</script>

<style scoped>
  .copywriting-dialog {
    min-height: 600px;
    max-height: 80vh;
  }

  .copywriting-dialog-content {
    display: flex;
    flex-direction: column;
    max-height: calc(80vh - 170px);
    overflow-y: auto;
  }

  .quantity-selection {
    display: flex;
    align-items: center;
  }

  .no-copywriting-tip {
    color: #f56c6c;
    font-size: 12px;
    margin-left: 10px;
  }

  .manual-selection-area {
    margin-top: 15px;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 4px;
  }

  .manual-selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .loading-container {
    padding: 20px;
  }

  .copywriting-list-manual {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .copywriting-item {
    padding: 12px;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s;
  }

  .copywriting-item:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .copywriting-item.selected {
    border-color: #409eff;
    background-color: #ecf5ff;
  }

  .copywriting-content {
    line-height: 1.5;
  }

  .copywriting-selected-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #409eff;
  }
</style>
