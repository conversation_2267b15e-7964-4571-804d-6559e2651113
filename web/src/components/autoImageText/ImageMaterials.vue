<template>
  <div class="image-materials">
    <!-- 优化后的使用说明 -->
    <div class="usage-description">
      <div class="usage-card">
        <div class="usage-header">
          <el-icon class="usage-icon"><InfoFilled /></el-icon>
          <span class="usage-title">图片分组使用说明</span>
        </div>
        <div class="usage-content">
          <div class="usage-item">
            <div class="usage-badge single">单组</div>
            <span class="usage-text">
              <strong>单个分组模式：</strong>系统会从该分组中随机选取N张图片来生成图文内容
            </span>
          </div>
          <div class="usage-item">
            <div class="usage-badge multi">多组</div>
            <span class="usage-text">
              <strong>多个分组模式：</strong>分组1随机选取1张作为第1张图片，分组2随机选取1张作为第2张图片，以此类推
            </span>
          </div>
          <div class="usage-item warning">
            <div class="usage-badge warning">注意</div>
            <span class="usage-text">
              <strong>重要提示：</strong>多个分组时，生成设置中的最小/最大图片数量将自动设为分组数量
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="materials-container">
      <div v-for="(material, index) in materials" :key="material.id || index" class="material-item">
        <div class="material-header">
          <!-- 分组标题 -->
          <div class="group-title">
            <span class="group-label">分组{{ index + 1 }}</span>
            <span v-if="getAllImages(material).length > 0" class="group-count">
              ({{ getAllImages(material).length }}张图片)
            </span>
          </div>
          <el-button v-if="materials.length > 1" type="danger" text size="small" @click="removeMaterial(index)">
            删除分组
          </el-button>
        </div>

        <div class="material-content">
          <!-- 素材选择区域 -->
          <div class="material-select">
            <div class="preview-area" @click="openMaterialDialog(index)">
              <div v-if="!material.mediaId && !material.mediaUrl" class="empty-preview">
                <el-icon>
                  <Plus />
                </el-icon>
                <div>点击选择图片</div>
              </div>
              <div v-else class="material-preview">
                <!-- 计算所有图片数组 -->
                <template v-if="getAllImages(material).length > 0">
                  <div class="images-grid" :class="getGridClass(getAllImages(material).length)">
                    <!-- 显示前面的图片 -->
                    <div
                      v-for="(image, imageIndex) in getDisplayImages(material)"
                      :key="imageIndex"
                      class="grid-image-item"
                    >
                      <img :src="image.url" :alt="image.name" class="grid-thumb-img" />
                    </div>

                    <!-- 显示剩余数量 -->
                    <div v-if="getRemainingCount(material) > 0" class="remaining-count-item">
                      <span class="remaining-text">+{{ getRemainingCount(material) }}</span>
                    </div>
                  </div>
                </template>
              </div>
            </div>
            <el-button type="primary" @click="openMaterialDialog(index)" plain class="select-material-btn">
              选择分组图片
            </el-button>
          </div>
        </div>
      </div>

      <!-- 添加分组按钮 -->
      <div class="add-group-container">
        <el-button type="dashed" @click="addMaterialGroup" class="add-group-btn">
          <el-icon><Plus /></el-icon>
          添加分组
        </el-button>
      </div>
    </div>

    <!-- 素材库选择对话框 -->
    <el-dialog v-model="materialDialogVisible" title="图片素材库选择" width="80%" class="material-dialog">
      <div class="material-dialog-content">
        <div class="dialog-sidebar">
          <div class="category-list">
            <!-- 递归渲染分类树 -->
            <category-tree
              v-for="category in categoriesTree"
              :key="category.categoryId"
              :category="category"
              :selected-category="selectedCategory"
              @select="selectCategory"
            />
          </div>
        </div>

        <div class="dialog-main">
          <!-- 添加已选计数和清空按钮 -->
          <div class="dialog-toolbar">
            <div class="selected-count" v-if="selectedResources.length > 0">
              已选择 {{ selectedResources.length }} 个图片
              <el-button @click="clearSelection" type="primary" size="small">清空选择</el-button>
              <el-button @click="selectAllResources" type="primary" plain size="small">
                {{ isCurrentPageAllSelected ? '取消全选' : '全选当前页' }}
              </el-button>
            </div>
            <!-- 添加全选按钮 -->
            <div class="toolbar-actions">
              <el-button
                @click="selectAllResources"
                type="primary"
                plain
                size="small"
                v-if="selectedResources.length === 0"
              >
                {{ isCurrentPageAllSelected ? '取消全选' : '全选当前页' }}
              </el-button>
            </div>
          </div>

          <div class="resource-search">
            <el-input v-model="searchKeyword" placeholder="搜索图片名称" clearable @input="searchResources">
              <template #prefix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>

          <div class="resource-pagination">
            <el-pagination
              :current-page="currentPage"
              :page-size="pageSize"
              :total="totalResources"
              :page-sizes="[20, 50, 100, 200]"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              @update:current-page="(val) => (currentPage = val)"
              @update:page-size="(val) => (pageSize = val)"
            />
          </div>

          <div class="resource-grid">
            <div
              v-for="resource in filteredResources"
              :key="resource.resourceId"
              class="resource-item"
              :class="{ selected: isResourceSelected(resource.resourceId) }"
              @click="toggleResourceSelection(resource)"
            >
              <div class="resource-thumbnail">
                <img :src="resource.cover || resource.thumbUrl || resource.url" :alt="resource.name" />
              </div>
              <div class="resource-name" :title="resource.name">{{ resource.name }}</div>
              <div class="resource-selection-icon" v-if="isResourceSelected(resource.resourceId)">
                <el-icon>
                  <Check />
                </el-icon>
              </div>
            </div>
          </div>

          <div class="resource-pagination">
            <el-pagination
              :current-page="currentPage"
              :page-size="pageSize"
              :total="totalResources"
              :page-sizes="[20, 50, 100, 200]"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              @update:current-page="(val) => (currentPage = val)"
              @update:page-size="(val) => (pageSize = val)"
            />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="materialDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSelection">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue'
  import { Plus, Search, Check, InfoFilled } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import { getResourceCategories, getResourceLibrary, getResourceCategoryCounts } from '@/api/ai/auto-video'
  import CategoryTree from '../autoVideo/CategoryTree.vue'

  const props = defineProps({
    materials: {
      type: Array,
      default: () => []
    }
  })

  const emit = defineEmits(['update:materials', 'groupsChanged'])

  // 素材库相关变量
  const materialDialogVisible = ref(false)
  const currentEditingIndex = ref(-1)
  const selectedResources = ref([])
  const categories = ref([])
  const categoriesTree = ref([])
  const selectedCategory = ref('')
  const resources = ref([])
  const currentPage = ref(1)
  const pageSize = ref(50)
  const totalResources = ref(0)
  const totalAllResources = ref(0)
  const searchKeyword = ref('')

  // 计算当前页是否已全选
  const isCurrentPageAllSelected = computed(() => {
    if (filteredResources.value.length === 0) return false
    return filteredResources.value.every((resource) =>
      selectedResources.value.some((selected) => selected.resourceId === resource.resourceId)
    )
  })

  // 过滤后的资源列表
  const filteredResources = computed(() => {
    if (!searchKeyword.value) {
      return resources.value
    }
    return resources.value.filter((resource) => resource.name.toLowerCase().includes(searchKeyword.value.toLowerCase()))
  })

  // 删除素材
  const removeMaterial = (index) => {
    const newMaterials = props.materials.filter((_, i) => i !== index)
    emit('update:materials', newMaterials)
    // 分组变化时，通知父组件更新图片数量设置
    emit('groupsChanged', newMaterials.length)
  }

  // 添加分组
  const addMaterialGroup = () => {
    const newMaterials = [
      ...props.materials,
      {
        id: Date.now(),
        mediaId: '',
        mediaUrl: '',
        name: '',
        extraMedia: []
      }
    ]
    emit('update:materials', newMaterials)
    // 分组变化时，通知父组件更新图片数量设置
    emit('groupsChanged', newMaterials.length)
  }

  // 打开素材选择对话框
  const openMaterialDialog = (index) => {
    currentEditingIndex.value = index
    materialDialogVisible.value = true

    // 将当前图片的素材设置为已选中状态
    const currentMaterial = props.materials[index]
    selectedResources.value = []

    // 添加主素材到选中列表（如果存在）
    if (currentMaterial.mediaId) {
      selectedResources.value.push({
        resourceId: currentMaterial.mediaId,
        mediaId: currentMaterial.mediaId,
        name: currentMaterial.name || '未命名',
        type: 'image',
        url: currentMaterial.mediaUrl || '',
        thumbUrl: '',
        width: currentMaterial.width || 0,
        height: currentMaterial.height || 0
      })
    }

    // 添加额外素材到选中列表（如果存在）
    if (currentMaterial.extraMedia && currentMaterial.extraMedia.length > 0) {
      currentMaterial.extraMedia.forEach((extraItem) => {
        if (extraItem.mediaId || extraItem.resourceId) {
          selectedResources.value.push({
            resourceId: extraItem.resourceId || extraItem.mediaId,
            mediaId: extraItem.mediaId,
            name: extraItem.name || '未命名',
            type: 'image',
            url: extraItem.url || '',
            thumbUrl: extraItem.thumbUrl || '',
            width: extraItem.width || 0,
            height: extraItem.height || 0
          })
        }
      })
    }

    // 重置分页
    currentPage.value = 1

    // 加载分类和资源
    loadCategories()
  }

  // 加载素材分类
  const loadCategories = async () => {
    try {
      const type = 'image' // 固定为图片类型
      const res = await getResourceCategories(type)

      if (!res) {
        ElMessage.error('获取分类失败: 网络请求出错')
        categories.value = []
        categoriesTree.value = []
        return
      }

      if (res.code !== 0) {
        ElMessage.error('获取分类失败: ' + (res.msg || '未知错误'))
        categories.value = []
        categoriesTree.value = []
        return
      }

      const categoriesData = res.data || []

      // 处理分类数据，构建扁平列表和层级树
      const allCategories = []
      const topCategories = []

      // 递归处理分类树
      const processCategoryTree = (categoryList, parentId = 0, level = 0) => {
        const result = []
        if (!Array.isArray(categoryList)) return result

        const sortedCategories = [...categoryList].sort((a, b) => {
          if (a.pid === 0 && b.pid === 0) {
            if (a.visibility !== b.visibility) {
              return a.visibility - b.visibility
            }
            return a.name.localeCompare(b.name)
          }
          return 0
        })

        sortedCategories.forEach((cat) => {
          if (!cat || !cat.ID) return

          const categoryItem = {
            categoryId: String(cat.ID || 0),
            name: cat.name || '未命名',
            type: type,
            pid: parentId,
            level: level,
            resourceCount: 0,
            children: [],
            visibility: cat.visibility || 0
          }

          allCategories.push({ ...categoryItem })

          if (cat.children && Array.isArray(cat.children) && cat.children.length > 0) {
            categoryItem.children = processCategoryTree(cat.children, cat.ID, level + 1)
          }

          result.push(categoryItem)
        })

        return result
      }

      if (Array.isArray(categoriesData)) {
        topCategories.push(...processCategoryTree(categoriesData))
      } else if (categoriesData && typeof categoriesData === 'object') {
        const singleCategory = {
          categoryId: String(categoriesData.ID || 0),
          name: categoriesData.name || '未命名',
          type: type,
          pid: 0,
          level: 0,
          resourceCount: 0,
          children: []
        }

        if (categoriesData.children && Array.isArray(categoriesData.children)) {
          singleCategory.children = processCategoryTree(categoriesData.children, categoriesData.ID, 1)
        }

        allCategories.push(singleCategory)
        topCategories.push(singleCategory)
      }

      categoriesTree.value = topCategories
      categories.value = allCategories

      await loadCategoryCounts()

      if (!selectedCategory.value && categoriesTree.value && categoriesTree.value.length > 0) {
        const firstCategory = categoriesTree.value[0]
        if (firstCategory && firstCategory.categoryId) {
          selectedCategory.value = firstCategory.categoryId
          loadResources()
        } else {
          selectedCategory.value = ''
          loadResources()
        }
      } else if (selectedCategory.value) {
        loadResources()
      } else {
        selectedCategory.value = ''
        loadResources()
      }
    } catch (error) {
      console.error('获取分类异常:', error)
      ElMessage.error('获取图片分类失败: ' + (error?.message || '未知错误'))
      categories.value = []
      categoriesTree.value = []
    }
  }

  // 加载分类计数
  const loadCategoryCounts = async () => {
    try {
      const res = await getResourceCategoryCounts('image')
      if (res && res.code === 0 && res.data) {
        const counts = res.data

        totalAllResources.value = counts['0'] || 0

        categories.value.forEach((category) => {
          const count = counts[category.categoryId]
          if (count !== undefined) {
            category.resourceCount = count
          }
        })

        updateCategoriesResourceCount()
      }
    } catch (error) {
      console.error('获取分类计数失败:', error)
    }
  }

  // 加载素材资源
  const loadResources = async () => {
    try {
      const params = {
        type: 'image',
        page: currentPage.value,
        page_size: pageSize.value,
        keyword: searchKeyword.value
      }

      if (selectedCategory.value) {
        params.category_id = selectedCategory.value
      }

      const res = await getResourceLibrary(params)

      if (!res) {
        ElMessage.error('获取图片失败: 网络请求出错')
        resources.value = []
        totalResources.value = 0
        return
      }

      if (res.code !== 0) {
        ElMessage.error('获取图片失败: ' + (res.msg || '未知错误'))
        resources.value = []
        totalResources.value = 0
        return
      }

      const resData = res.data || {}

      let resourceList = []
      let total = 0

      if (resData.list && Array.isArray(resData.list)) {
        resourceList = resData.list
        total = resData.total || resData.list.length
      } else if (Array.isArray(resData)) {
        resourceList = resData
        total = resData.length
      } else if (resData && typeof resData === 'object') {
        resourceList = [resData]
        total = 1
      }

      totalResources.value = total
      totalAllResources.value = total

      resources.value = resourceList
        .map((item) => {
          if (!item) return null

          let mediaUrl = (item.fileUrl || item.url || '').replace(/^http:\/\//i, 'https://')

          let coverUrl = ''
          if (item.cover) {
            coverUrl = item.cover.replace(/^http:\/\//i, 'https://')
          }

          return {
            resourceId: String(item.ID || 0),
            mediaId: item.mediaId || '',
            name: item.name || '未命名',
            type: 'image',
            url: mediaUrl,
            thumbUrl: coverUrl || mediaUrl,
            cover: coverUrl,
            width: item.width || 0,
            height: item.height || 0,
            categoryId: item.categoryId ? String(item.categoryId) : '',
            createdAt: item.CreatedAt || ''
          }
        })
        .filter(Boolean)

      updateCategoriesResourceCount()

      if (selectedResources.value.length > 0) {
        selectedResources.value = selectedResources.value.map((selectedResource) => {
          const matchedResource = resources.value.find(
            (r) =>
              r.resourceId === selectedResource.resourceId ||
              r.mediaId === selectedResource.resourceId ||
              r.resourceId === selectedResource.mediaId ||
              r.mediaId === selectedResource.mediaId
          )

          if (matchedResource) {
            return {
              ...selectedResource,
              resourceId: matchedResource.resourceId,
              name: matchedResource.name,
              url: matchedResource.url,
              thumbUrl: matchedResource.thumbUrl,
              width: matchedResource.width,
              height: matchedResource.height
            }
          }

          return selectedResource
        })
      }
    } catch (error) {
      console.error('获取图片异常:', error)
      ElMessage.error('获取图片资源失败: ' + (error?.message || '未知错误'))
      resources.value = []
      totalResources.value = 0
    }
  }

  // 更新分类的素材数量统计
  const updateCategoriesResourceCount = () => {
    const needsUpdate = categories.value.some((category) => !category.resourceCount)
    if (!needsUpdate) return

    if (selectedCategory.value) {
      const currentCategory = categories.value.find((c) => c.categoryId === selectedCategory.value)
      if (currentCategory) {
        currentCategory.resourceCount = totalResources.value
      }
    }

    const updateTreeResourceCount = (treeCategories) => {
      if (!Array.isArray(treeCategories)) return

      treeCategories.forEach((category) => {
        const flatCategory = categories.value.find((c) => c.categoryId === category.categoryId)
        if (flatCategory && flatCategory.resourceCount) {
          category.resourceCount = flatCategory.resourceCount
        }

        if (category.children && category.children.length > 0) {
          updateTreeResourceCount(category.children)
        }
      })
    }

    updateTreeResourceCount(categoriesTree.value)
  }

  // 选择分类
  const selectCategory = (categoryId) => {
    selectedCategory.value = categoryId
    loadResources()
  }

  // 搜索资源
  const searchResources = () => {
    currentPage.value = 1
    loadResources()
  }

  // 处理分页大小变化
  const handleSizeChange = (size) => {
    pageSize.value = size
    loadResources()
  }

  // 处理页码变化
  const handleCurrentChange = (page) => {
    currentPage.value = page
    loadResources()
  }

  // 判断资源是否被选中
  const isResourceSelected = (resourceId) => {
    const directMatch = selectedResources.value.some((r) => r.resourceId === resourceId)
    if (directMatch) return true

    return selectedResources.value.some((r) => {
      if (r.mediaId && r.mediaId === resourceId) return true

      if (
        r.resourceId &&
        resources.value.some((resource) => resource.resourceId === r.resourceId && resource.mediaId === resourceId)
      )
        return true

      return false
    })
  }

  // 切换资源选择状态
  const toggleResourceSelection = (resource) => {
    const index = selectedResources.value.findIndex((r) => r.resourceId === resource.resourceId)
    if (index === -1) {
      selectedResources.value.push(resource)
    } else {
      selectedResources.value.splice(index, 1)
    }
  }

  // 全选当前页素材
  const selectAllResources = () => {
    if (isCurrentPageAllSelected.value) {
      selectedResources.value = selectedResources.value.filter(
        (r) => !filteredResources.value.some((fr) => fr.resourceId === r.resourceId)
      )
      return
    }

    const maxSelectCount = 200

    const currentSelectedCount = selectedResources.value.length
    const unselectedCount = filteredResources.value.filter(
      (r) => !selectedResources.value.some((selected) => selected.resourceId === r.resourceId)
    ).length

    if (currentSelectedCount + unselectedCount > maxSelectCount) {
      ElMessage.warning(`最多只能选择${maxSelectCount}个图片`)

      const remainingSlots = maxSelectCount - currentSelectedCount
      if (remainingSlots > 0) {
        const unselectedResources = filteredResources.value.filter(
          (r) => !selectedResources.value.some((selected) => selected.resourceId === r.resourceId)
        )

        selectedResources.value = [...selectedResources.value, ...unselectedResources.slice(0, remainingSlots)]
      }
      return
    }

    const newSelections = filteredResources.value.filter(
      (r) => !selectedResources.value.some((selected) => selected.resourceId === r.resourceId)
    )

    selectedResources.value = [...selectedResources.value, ...newSelections]
  }

  // 清空选择的资源
  const clearSelection = () => {
    selectedResources.value = []
  }

  // 确认选择
  const confirmSelection = () => {
    const index = currentEditingIndex.value
    const newMaterials = [...props.materials]

    if (selectedResources.value.length > 0) {
      // 获取第一个作为主素材
      const mainResource = selectedResources.value[0]

      newMaterials[index] = {
        ...newMaterials[index],
        mediaId: mainResource.mediaId || mainResource.resourceId || '',
        mediaUrl: mainResource.url || '',
        cover: mainResource.cover || '',
        name: mainResource.name || '',
        width: mainResource.width || 0,
        height: mainResource.height || 0
      }

      // 如果有多个素材，除第一个外的都作为额外素材
      if (selectedResources.value.length > 1) {
        newMaterials[index].extraMedia = []
        for (let i = 1; i < selectedResources.value.length; i++) {
          const res = selectedResources.value[i]
          newMaterials[index].extraMedia.push({
            resourceId: res.resourceId || '',
            mediaId: res.mediaId || res.resourceId || '',
            name: res.name || '',
            type: res.type || '',
            url: res.url || '',
            cover: res.cover || ''
          })
        }
      }
    } else {
      // 如果没有选中任何资源，清空当前素材
      newMaterials[index] = {
        ...newMaterials[index],
        mediaId: '',
        mediaUrl: '',
        name: '',
        extraMedia: []
      }
    }

    emit('update:materials', newMaterials)
    materialDialogVisible.value = false
  }

  // 初始挂载
  onMounted(() => {
    // 确保至少有一个素材
    if (props.materials.length === 0) {
      const newMaterial = {
        id: Date.now() + Math.random().toString(36).substring(2, 9),
        mediaId: '',
        mediaUrl: '',
        name: '',
        extraMedia: []
      }
      emit('update:materials', [newMaterial])
    }
  })

  // 获取所有图片（主图片+额外图片）
  const getAllImages = (material) => {
    const images = []

    // 添加主图片
    if (material.mediaUrl) {
      images.push({
        url: material.mediaUrl,
        name: material.name || '图片',
        mediaId: material.mediaId
      })
    }

    // 添加额外图片
    if (material.extraMedia && Array.isArray(material.extraMedia)) {
      material.extraMedia.forEach((extra) => {
        if (extra.url) {
          images.push({
            url: extra.url,
            name: extra.name || '图片',
            mediaId: extra.mediaId
          })
        }
      })
    }

    return images
  }

  // 获取要显示的图片（如果超过8张，显示前7张，剩余的用+x表示）
  const getDisplayImages = (material) => {
    const allImages = getAllImages(material)
    const maxDisplay = 8 // 最多8个网格位置

    if (allImages.length <= maxDisplay) {
      return allImages // 如果总数不超过8张，全部显示
    } else {
      return allImages.slice(0, maxDisplay - 1) // 如果超过8张，显示前7张，留一个位置给"+x"
    }
  }

  // 获取剩余图片数量
  const getRemainingCount = (material) => {
    const allImages = getAllImages(material)
    const maxDisplay = 8

    if (allImages.length <= maxDisplay) {
      return 0 // 如果总数不超过8张，不显示剩余数量
    } else {
      return allImages.length - (maxDisplay - 1) // 剩余数量 = 总数 - 已显示的7张
    }
  }

  // 根据图片数量返回网格样式类
  const getGridClass = (totalCount) => {
    // 如果总数超过8张，实际显示元素是7张图片+1个剩余计数 = 8个元素
    // 如果总数不超过8张，实际显示元素就是图片数量
    const displayElements = totalCount > 8 ? 8 : totalCount

    if (displayElements === 1) return 'grid-single'
    if (displayElements === 2) return 'grid-two'
    if (displayElements === 3) return 'grid-three'
    if (displayElements === 4) return 'grid-four'
    if (displayElements <= 6) return 'grid-six'
    return 'grid-many'
  }

  // 验证素材
  const validateMaterials = () => {
    return props.materials.filter((m) => m.mediaId || m.mediaUrl)
  }

  // 暴露验证方法
  defineExpose({
    validateMaterials
  })
</script>

<style scoped>
  .image-materials {
    padding: 20px;
  }

  .section-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    border-left: 4px solid #4b6cb7;
    padding-left: 10px;
  }

  .materials-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .material-item {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    background-color: #fff;
  }

  .material-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-weight: 600;
    color: #333;
  }

  .material-content {
    display: flex;
    gap: 20px;
    align-items: flex-start;
  }

  .material-select {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
  }

  .preview-area {
    cursor: pointer;
    height: 200px;
    border: 1px dashed #ccc;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background-color: #f8f9fa;
    transition: all 0.3s;
    position: relative;
  }

  .preview-area:hover {
    border-color: #409eff;
  }

  .empty-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #909399;
  }

  .empty-preview .el-icon {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .material-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 8px;
  }

  /* 图片网格布局 */
  .images-grid {
    display: grid;
    gap: 4px;
    width: 100%;
    height: 100%;
    max-height: 184px; /* 200px - 16px padding */
  }

  /* 单张图片 */
  .images-grid.grid-single {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
  }

  /* 两张图片 */
  .images-grid.grid-two {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr;
  }

  /* 三张图片 */
  .images-grid.grid-three {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr;
  }

  /* 四张图片 */
  .images-grid.grid-four {
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr;
  }

  /* 5-6张图片 */
  .images-grid.grid-six {
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: 1fr;
  }

  /* 7张及以上图片 */
  .images-grid.grid-many {
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: 1fr;
  }

  .grid-image-item {
    border-radius: 4px;
    overflow: hidden;
    background-color: #f5f7fa;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
  }

  .grid-thumb-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .remaining-count-item {
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 500;
    font-size: 14px;
  }

  .remaining-text {
    color: white;
  }

  .select-material-btn {
    width: 100%;
  }

  /* 素材库对话框样式 */
  .material-dialog-content {
    display: flex;
    height: 600px;
  }

  .dialog-sidebar {
    width: 200px;
    border-right: 1px solid #ebeef5;
    overflow-y: auto;
  }

  .category-list {
    display: flex;
    flex-direction: column;
  }

  .dialog-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .selected-count {
    color: #409eff;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .toolbar-actions {
    display: flex;
    gap: 10px;
  }

  .dialog-main {
    flex: 1;
    padding: 0 20px;
    overflow-y: auto;
  }

  .resource-search {
    margin: 15px 0 5px;
  }

  .resource-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
  }

  .resource-item {
    border: 1px solid #ebeef5;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
    cursor: pointer;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  .resource-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .resource-item.selected {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }

  .resource-thumbnail {
    height: 120px;
    overflow: hidden;
    position: relative;
    background-color: #f5f7fa;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .resource-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .resource-name {
    padding: 10px;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: 40px;
    line-height: 24px;
    max-width: 100%;
    box-sizing: border-box;
  }

  .resource-selection-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #409eff;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .resource-pagination {
    margin-top: 0;
    margin-bottom: 15px;
    display: flex;
    justify-content: center;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }
  .group-title {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .group-label {
    font-weight: 600;
    color: #333;
    font-size: 16px;
  }

  .group-count {
    font-size: 14px;
    color: #666;
    font-weight: normal;
  }

  .usage-description {
    margin-bottom: 16px;
  }

  .usage-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    padding: 12px 16px;
    color: white;
    box-shadow: 0 2px 12px rgba(102, 126, 234, 0.15);
  }

  .usage-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 6px;
  }

  .usage-icon {
    font-size: 16px;
    color: #ffffff;
  }

  .usage-title {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
  }

  .usage-content {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .usage-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 6px 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
  }

  .usage-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(2px);
  }

  .usage-item.warning {
    background: rgba(255, 193, 7, 0.2);
    border-left: 2px solid #ffc107;
  }

  .usage-badge {
    min-width: 32px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
    flex-shrink: 0;
  }

  .usage-badge.single {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
  }

  .usage-badge.multi {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
  }

  .usage-badge.warning {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #8b4513;
  }

  .usage-text {
    flex: 1;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.95);
    line-height: 1.4;
  }

  .usage-text strong {
    color: #ffffff;
    font-weight: 600;
  }

  .usage-item.warning .usage-text {
    color: rgba(255, 243, 205, 0.95);
  }

  .usage-item.warning .usage-text strong {
    color: #fff3cd;
  }

  .add-group-container {
    text-align: center;
    margin-top: 24px;
    padding: 20px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    background-color: #fafafa;
  }

  .add-group-btn {
    min-width: 160px;
    height: 40px;
    border: 1px dashed #d9d9d9;
    background-color: transparent;
    color: #666;
    transition: all 0.3s;
  }

  .add-group-btn:hover {
    border-color: #409eff;
    color: #409eff;
    background-color: #f0f9ff;
  }

  .mb-20 {
    margin-bottom: 20px;
  }
</style>
