<template>
  <div class="sticker-settings">
    <div class="stickers-container">
      <div class="stickers-layout">
        <!-- 左侧：贴纸列表 -->
        <div class="stickers-left">
          <el-empty v-if="stickers.length === 0" description="暂无贴纸，点击右侧按钮添加" />

          <div v-else class="sticker-list">
            <div
              v-for="(sticker, index) in stickers"
              :key="sticker.id || index"
              class="sticker-item"
              :class="{ active: activeStickerIndex === index }"
              @click="setActiveSticker(index)"
            >
              <div class="sticker-header">
                <span>贴纸 {{ index + 1 }}</span>
                <el-button type="danger" text size="small" @click.stop="confirmRemoveSticker(index)" class="delete-btn">
                  删除
                </el-button>
              </div>

              <div class="sticker-content">
                <!-- 贴纸预览 -->
                <div class="sticker-preview" @click.stop="openStickerDialog(index)">
                  <div v-if="sticker.mediaUrl" class="sticker-image">
                    <img :src="sticker.mediaUrl" alt="贴纸预览" />
                  </div>
                  <div v-else class="sticker-placeholder">
                    <el-icon><Picture /></el-icon>
                    <span>点击选择贴纸</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 添加贴纸按钮 -->
          <div class="add-sticker-btn">
            <el-button type="primary" @click="addSticker" icon="Plus" class="add-btn"> 添加贴纸 </el-button>
          </div>
        </div>

        <!-- 右侧：贴纸配置 -->
        <div class="stickers-right">
          <div v-if="activeStickerIndex >= 0 && stickers[activeStickerIndex]" class="sticker-settings-panel">
            <div class="settings-header">
              <h4>贴纸配置</h4>
              <span class="current-sticker">贴纸 {{ activeStickerIndex + 1 }}</span>
            </div>

            <!-- 尺寸设置 -->
            <div class="setting-row">
              <el-form-item label="贴纸大小" class="setting-item">
                <el-slider
                  :model-value="stickers[activeStickerIndex].scale"
                  @update:model-value="updateStickerProperty('scale', $event)"
                  :min="0.1"
                  :max="2.0"
                  :step="0.1"
                  show-input
                  input-size="small"
                />
              </el-form-item>
            </div>

            <!-- 位置设置 -->
            <div class="setting-row">
              <el-form-item label="位置" class="setting-item">
                <el-select
                  :model-value="stickers[activeStickerIndex].position"
                  @update:model-value="updateStickerProperty('position', $event)"
                  placeholder="选择位置"
                >
                  <el-option label="左上角" value="top-left" />
                  <el-option label="上方中央" value="top-center" />
                  <el-option label="右上角" value="top-right" />
                  <el-option label="左侧中央" value="center-left" />
                  <el-option label="中央" value="center" />
                  <el-option label="右侧中央" value="center-right" />
                  <el-option label="左下角" value="bottom-left" />
                  <el-option label="下方中央" value="bottom-center" />
                  <el-option label="右下角" value="bottom-right" />
                  <el-option label="自定义" value="custom" />
                </el-select>
              </el-form-item>
            </div>

            <!-- X坐标设置 -->
            <div class="setting-row">
              <el-form-item label="X坐标" class="setting-item">
                <el-slider
                  :model-value="stickers[activeStickerIndex].x || 0"
                  @update:model-value="updateStickerProperty('x', $event)"
                  :min="0"
                  :max="1"
                  :step="0.01"
                  :format-tooltip="(val) => `${Math.round(val * 100)}%`"
                  show-input
                  input-size="small"
                />
              </el-form-item>
            </div>

            <!-- Y坐标设置 -->
            <div class="setting-row">
              <el-form-item label="Y坐标" class="setting-item">
                <el-slider
                  :model-value="stickers[activeStickerIndex].y || 0"
                  @update:model-value="updateStickerProperty('y', $event)"
                  :min="0"
                  :max="1"
                  :step="0.01"
                  :format-tooltip="(val) => `${Math.round(val * 100)}%`"
                  show-input
                  input-size="small"
                />
              </el-form-item>
            </div>

            <!-- 透明度设置 -->
            <div class="setting-row">
              <el-form-item label="透明度" class="setting-item">
                <el-slider
                  :model-value="stickers[activeStickerIndex].opacity"
                  @update:model-value="updateStickerProperty('opacity', $event)"
                  :min="0"
                  :max="1"
                  :step="0.1"
                  :format-tooltip="(val) => `${Math.round(val * 100)}%`"
                  show-input
                  input-size="small"
                />
              </el-form-item>
            </div>

            <!-- 旋转角度 -->
            <div class="setting-row">
              <el-form-item label="旋转角度" class="setting-item">
                <el-slider
                  :model-value="stickers[activeStickerIndex].rotation"
                  @update:model-value="updateStickerProperty('rotation', $event)"
                  :min="0"
                  :max="360"
                  :step="1"
                  show-input
                  input-size="small"
                />
              </el-form-item>
            </div>
          </div>

          <div v-else class="no-sticker-selected">
            <el-empty description="请点击左侧贴纸进行配置" :image-size="80" />
          </div>
        </div>
      </div>
    </div>

    <!-- 图片库选择对话框 -->
    <el-dialog v-model="stickerDialogVisible" title="图片库选择" width="80%" class="sticker-dialog">
      <div class="dialog-content">
        <div class="dialog-sidebar">
          <div class="category-list">
            <!-- 全部分类项 -->
            <div class="category-item" :class="{ active: selectedCategory === '0' }" @click="selectCategory('0')">
              全部
              <!-- 素材数量指示器 -->
              <span v-if="getTotalResourceCount() > 0" class="category-resource-count">
                {{ getTotalResourceCount() }}
              </span>
            </div>
            <!-- 递归渲染分类树 -->
            <category-tree
              v-for="category in categoriesTree"
              :key="category.categoryId"
              :category="category"
              :selected-category="selectedCategory"
              @select="selectCategory"
            />
          </div>
        </div>

        <div class="dialog-main">
          <div class="image-search">
            <el-input v-model="searchKeyword" placeholder="搜索图片名称" clearable @input="searchImages">
              <template #prefix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>

          <div class="image-pagination">
            <el-pagination
              :current-page="currentPage"
              :page-size="pageSize"
              :total="totalImages"
              :page-sizes="[20, 50, 100, 200]"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              @update:current-page="(val) => (currentPage = val)"
              @update:page-size="(val) => (pageSize = val)"
              style="margin: 0 0 15px 0"
            />
          </div>

          <div class="image-grid">
            <el-empty v-if="filteredImages.length === 0" description="暂无图片，可以尝试选择其他分类或搜索关键词" />

            <div
              v-else
              v-for="image in filteredImages"
              :key="image.resourceId"
              class="image-grid-item"
              @click="selectStickerImage(image)"
            >
              <div class="image-thumbnail">
                <img :src="image.cover || image.thumbUrl || image.url" :alt="image.name" />
              </div>
              <div class="image-name" :title="image.name">{{ image.name }}</div>
            </div>
          </div>

          <div class="image-pagination">
            <el-pagination
              :current-page="currentPage"
              :page-size="pageSize"
              :total="totalImages"
              :page-sizes="[20, 50, 100, 200]"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              @update:current-page="(val) => (currentPage = val)"
              @update:page-size="(val) => (pageSize = val)"
              style="margin: 0 0 15px 0"
            />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="stickerDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, watch } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Picture, Search } from '@element-plus/icons-vue'
  import { getResourceCategories, getResourceLibrary } from '@/api/ai/auto-video'
  import CategoryTree from '../autoVideo/CategoryTree.vue'

  const props = defineProps({
    stickers: {
      type: Array,
      default: () => []
    }
  })

  const emit = defineEmits(['update:stickers'])

  const activeStickerIndex = ref(-1)
  const currentStickerIndex = ref(-1)

  // 初始化默认贴纸
  const initializeDefaultSticker = () => {
    if (props.stickers.length === 0) {
      const defaultSticker = {
        id: Date.now() + Math.random().toString(36).substring(2, 9),
        mediaUrl: '', // 改为 mediaUrl 以匹配后端字段名
        scale: 1.0,
        position: 'center',
        x: 0.5, // X坐标，范围0-1，0.5表示居中
        y: 0.5, // Y坐标，范围0-1，0.5表示居中
        opacity: 1.0,
        rotation: 0
      }

      emit('update:stickers', [defaultSticker])
      activeStickerIndex.value = 0
    } else if (activeStickerIndex.value === -1) {
      // 如果有贴纸但没有选中任何贴纸，选中第一个
      activeStickerIndex.value = 0
    }
  }

  // 监听贴纸数组变化
  watch(
    () => props.stickers.length,
    (newLength) => {
      if (newLength === 0) {
        activeStickerIndex.value = -1
      } else if (activeStickerIndex.value === -1) {
        activeStickerIndex.value = 0
      }
    }
  )

  // 组件挂载时初始化
  onMounted(() => {
    initializeDefaultSticker()
  })

  // 工具函数（已移除formatOffsetTooltip，现在使用百分比显示）

  // 图片库选择相关变量
  const stickerDialogVisible = ref(false)
  const categories = ref([])
  const categoriesTree = ref([])
  const selectedCategory = ref('')
  const imagesList = ref([])
  const currentPage = ref(1)
  const pageSize = ref(50)
  const totalImages = ref(0)
  const totalAllImages = ref(0)
  const searchKeyword = ref('')

  // 过滤后的图片列表
  const filteredImages = computed(() => {
    if (!imagesList.value || imagesList.value.length === 0) {
      return []
    }

    if (!searchKeyword.value) {
      return imagesList.value
    }

    const keyword = searchKeyword.value.toLowerCase()
    return imagesList.value.filter((image) => {
      const name = image.name || ''
      return name.toLowerCase().includes(keyword)
    })
  })

  // 添加贴纸
  const addSticker = () => {
    const newSticker = {
      id: Date.now() + Math.random().toString(36).substring(2, 9),
      mediaUrl: '', // 改为 mediaUrl 以匹配后端字段名
      scale: 1.0,
      position: 'center',
      x: 0.5, // X坐标，范围0-1，0.5表示居中
      y: 0.5, // Y坐标，范围0-1，0.5表示居中
      opacity: 1.0,
      rotation: 0
    }

    const newStickers = [...props.stickers, newSticker]
    emit('update:stickers', newStickers)

    // 设置新添加的贴纸为活动贴纸
    setActiveSticker(newStickers.length - 1)
  }

  // 确认删除贴纸
  const confirmRemoveSticker = async (index) => {
    try {
      await ElMessageBox.confirm('确定要删除这个贴纸吗？', '删除确认', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      })

      // 用户确认删除
      removeSticker(index)
    } catch {
      // 用户取消删除
      return
    }
  }

  // 删除贴纸
  const removeSticker = (index) => {
    const newStickers = props.stickers.filter((_, i) => i !== index)

    // 如果删除后没有贴纸了，创建一个新的默认贴纸
    if (newStickers.length === 0) {
      const defaultSticker = {
        id: Date.now() + Math.random().toString(36).substring(2, 9),
        mediaUrl: '', // 改为 mediaUrl 以匹配后端字段名
        scale: 1.0,
        position: 'center',
        x: 0.5, // X坐标，范围0-1，0.5表示居中
        y: 0.5, // Y坐标，范围0-1，0.5表示居中
        opacity: 1.0,
        rotation: 0
      }
      emit('update:stickers', [defaultSticker])
      activeStickerIndex.value = 0
      ElMessage.success('贴纸删除成功，已自动创建新贴纸')
      return
    }

    emit('update:stickers', newStickers)

    // 调整活动贴纸索引
    if (activeStickerIndex.value === index) {
      // 如果删除的是当前活动贴纸，选择一个新的活动贴纸
      if (index > 0) {
        activeStickerIndex.value = index - 1
      } else {
        activeStickerIndex.value = 0
      }
    } else if (activeStickerIndex.value > index) {
      activeStickerIndex.value -= 1
    }

    ElMessage.success('贴纸删除成功')
  }

  // 设置活动贴纸
  const setActiveSticker = (index) => {
    activeStickerIndex.value = index
  }

  // 更新贴纸属性
  const updateStickerProperty = (property, value) => {
    if (activeStickerIndex.value >= 0 && activeStickerIndex.value < props.stickers.length) {
      const newStickers = [...props.stickers]
      newStickers[activeStickerIndex.value] = {
        ...newStickers[activeStickerIndex.value],
        [property]: value
      }
      emit('update:stickers', newStickers)
    }
  }

  // 打开贴纸选择对话框
  const openStickerDialog = async (index) => {
    currentStickerIndex.value = index
    setActiveSticker(index)
    stickerDialogVisible.value = true

    currentPage.value = 1

    await loadCategories()
    await loadImages()
  }

  // 加载图片分类
  const loadCategories = async () => {
    try {
      const res = await getResourceCategories('image')

      if (!res) {
        ElMessage.error('获取图片分类失败: 网络请求错误')
        categories.value = []
        categoriesTree.value = []
        return
      }

      if (res.code === 0 && res.data) {
        const allCategories = []
        const topCategories = []

        const processCategory = (category, parentId = 0) => {
          if (!category) return null

          const processedCategory = {
            categoryId: String(category.ID || 0),
            name: category.name || '未命名',
            type: 'image',
            pid: parentId,
            resourceCount: category.resourceCount || 0,
            visibility: category.visibility,
            children: []
          }

          allCategories.push(processedCategory)

          if (category.children && Array.isArray(category.children)) {
            const sortedChildren = [...category.children].sort((a, b) => {
              if (a.pid === 0 && b.pid === 0) {
                if (a.visibility !== b.visibility) {
                  return a.visibility - b.visibility
                }
                return a.name.localeCompare(b.name)
              }
              return 0
            })

            sortedChildren.forEach((child) => {
              const childCategory = processCategory(child, processedCategory.categoryId)
              if (childCategory) {
                processedCategory.children.push(childCategory)
              }
            })
          }

          return processedCategory
        }

        if (Array.isArray(res.data)) {
          const sortedTopCategories = [...res.data].sort((a, b) => {
            if (a.visibility !== b.visibility) {
              return a.visibility - b.visibility
            }
            return a.name.localeCompare(b.name)
          })

          sortedTopCategories.forEach((cat) => {
            const topCategory = processCategory(cat)
            if (topCategory) {
              topCategories.push(topCategory)
            }
          })
        } else if (res.data && typeof res.data === 'object') {
          const singleCategory = processCategory(res.data)
          if (singleCategory) {
            topCategories.push(singleCategory)
          }
        }

        const allCategory = {
          categoryId: '0',
          name: '全部',
          type: 'image',
          resourceCount: 0
        }
        allCategories.unshift(allCategory)

        categoriesTree.value = topCategories
        categories.value = allCategories

        if (!selectedCategory.value) {
          selectedCategory.value = '0'
        }
      } else {
        ElMessage.error('获取图片分类失败: 响应格式异常')
        categories.value = []
        categoriesTree.value = []
      }
    } catch (error) {
      ElMessage.error('获取图片分类失败: ' + error.message)
      categories.value = []
      categoriesTree.value = []
    }
  }

  // 加载图片资源
  const loadImages = async () => {
    try {
      const params = {
        type: 'image',
        page: currentPage.value,
        pageSize: pageSize.value,
        keyword: searchKeyword.value
      }

      if (selectedCategory.value && selectedCategory.value !== '0') {
        params.category = selectedCategory.value
      }

      const res = await getResourceLibrary(params)

      if (!res) {
        ElMessage.error('获取图片资源失败: 网络请求错误')
        imagesList.value = []
        totalImages.value = 0
        return
      }

      if (res.code === 0 && res.data) {
        if (res.data.list === null) {
          imagesList.value = []
          totalImages.value = 0
          return
        }

        if (Array.isArray(res.data.list)) {
          imagesList.value = res.data.list.map((item) => {
            let mediaUrl = (item.fileUrl || item.url || '').replace(/^http:\/\//i, 'https://')
            let coverUrl = ''
            if (item.cover) {
              coverUrl = item.cover.replace(/^http:\/\//i, 'https://')
            }

            return {
              resourceId: String(item.ID || 0),
              mediaId: item.mediaId || '',
              name: item.name || '未命名',
              type: 'image',
              url: mediaUrl,
              thumbUrl: coverUrl,
              cover: coverUrl,
              width: item.width || 0,
              height: item.height || 0,
              categoryId: item.categoryId ? String(item.categoryId) : '',
              createdAt: item.CreatedAt || ''
            }
          })

          totalImages.value = res.data.total || 0

          if (selectedCategory.value === '0') {
            totalAllImages.value = totalImages.value
          }

          updateCategoriesResourceCount()
        } else {
          imagesList.value = []
          totalImages.value = 0
        }
      } else {
        console.error('无法识别的图片响应格式:', res)
        ElMessage.error('获取图片资源失败: 响应格式异常')
        imagesList.value = []
        totalImages.value = 0
      }
    } catch (error) {
      console.error('获取图片资源失败', error)
      ElMessage.error('获取图片资源失败: ' + error.message)
      imagesList.value = []
      totalImages.value = 0
    }
  }

  // 更新分类的资源数量统计
  const updateCategoriesResourceCount = () => {
    const needsUpdate = categories.value.some((category) => !category.resourceCount)
    if (!needsUpdate) return

    const allCategory = categories.value.find((c) => c.categoryId === '0')
    if (allCategory) {
      allCategory.resourceCount = totalAllImages.value
    }

    if (selectedCategory.value !== '0') {
      const currentCategory = categories.value.find((c) => c.categoryId === selectedCategory.value)
      if (currentCategory) {
        currentCategory.resourceCount = totalImages.value
      }
    }
  }

  // 获取全部资源数量
  const getTotalResourceCount = () => {
    return totalAllImages.value
  }

  // 选择分类
  const selectCategory = (categoryId) => {
    selectedCategory.value = categoryId
    loadImages()
  }

  // 搜索图片
  const searchImages = () => {
    currentPage.value = 1
    loadImages()
  }

  // 处理分页大小变化
  const handleSizeChange = (size) => {
    pageSize.value = size
    loadImages()
  }

  // 处理页码变化
  const handleCurrentChange = (page) => {
    currentPage.value = page
    loadImages()
  }

  // 选择贴纸图片
  const selectStickerImage = (image) => {
    if (currentStickerIndex.value >= 0 && currentStickerIndex.value < props.stickers.length) {
      const newStickers = [...props.stickers]
      newStickers[currentStickerIndex.value] = {
        ...newStickers[currentStickerIndex.value],
        mediaUrl: image.url, // 改为 mediaUrl 以匹配后端字段名
        mediaId: image.mediaId || image.resourceId,
        name: image.name
      }
      emit('update:stickers', newStickers)
      ElMessage.success('贴纸选择成功!')
    }

    stickerDialogVisible.value = false
  }
</script>

<style scoped>
  .sticker-settings {
    padding: 20px;
  }

  .section-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    border-left: 4px solid #4b6cb7;
    padding-left: 10px;
  }

  .stickers-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .stickers-layout {
    display: flex;
    gap: 20px;
    min-height: 400px;
  }

  .stickers-left {
    flex: 0 0 220px;
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .stickers-right {
    flex: 1;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e0e0e0;
  }

  .sticker-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .sticker-item {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px;
    background-color: #fff;
    transition: all 0.3s;
    cursor: pointer;
  }

  .sticker-item:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  }

  .sticker-item.active {
    border-color: #409eff;
    box-shadow: 0 0 10px rgba(64, 158, 255, 0.2);
    background-color: #ecf5ff;
  }

  .sticker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
  }

  .delete-btn {
    opacity: 0.7;
    transition: opacity 0.3s;
  }

  .delete-btn:hover {
    opacity: 1;
  }

  .sticker-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .sticker-preview {
    width: 120px;
    height: 120px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    align-self: center;
    margin: 0 auto;
  }

  .sticker-preview:hover {
    border-color: #409eff;
    background-color: #f0f9ff;
  }

  .sticker-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .sticker-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 6px;
  }

  .sticker-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    text-align: center;
  }

  .sticker-placeholder .el-icon {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .sticker-placeholder span {
    font-size: 12px;
  }

  .sticker-settings-panel {
    margin-top: 15px;
  }

  .setting-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
  }

  .setting-item {
    flex: 1;
    margin-bottom: 0;
  }

  .add-sticker-btn {
    display: flex;
    justify-content: center;
    margin-top: 15px;
  }

  .add-btn {
    width: 100%;
    padding: 10px 0;
    border-radius: 6px;
  }

  .settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
  }

  .settings-header h4 {
    margin: 0;
    font-size: 16px;
    color: #333;
    font-weight: 600;
  }

  .current-sticker {
    background-color: #409eff;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }

  .no-sticker-selected {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #909399;
  }

  /* 图片库对话框样式 */
  .dialog-content {
    display: flex;
    height: 600px;
  }

  .dialog-sidebar {
    width: 200px;
    border-right: 1px solid #ebeef5;
    overflow-y: auto;
  }

  .category-list {
    display: flex;
    flex-direction: column;
  }

  .category-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.3s;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .category-item:hover {
    background-color: #f5f7fa;
  }

  .category-item.active {
    background-color: #ecf5ff;
    color: #409eff;
    font-weight: 500;
  }

  .category-resource-count {
    background-color: #f0f2f5;
    color: #909399;
    font-size: 12px;
    line-height: 1;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 5px;
  }

  .category-item.active .category-resource-count {
    background-color: #d8e6fd;
    color: #409eff;
  }

  .dialog-main {
    flex: 1;
    padding: 0 20px;
    overflow-y: auto;
  }

  .image-search {
    margin: 15px 0 5px;
  }

  .image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
  }

  .image-grid-item {
    border: 1px solid #ebeef5;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
    cursor: pointer;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  .image-grid-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .image-thumbnail {
    height: 120px;
    overflow: hidden;
    position: relative;
    background-color: #f5f7fa;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .image-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .image-name {
    padding: 10px;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: 40px;
    line-height: 24px;
    max-width: 100%;
    box-sizing: border-box;
  }

  .image-pagination {
    margin-top: 0;
    margin-bottom: 15px;
    display: flex;
    justify-content: center;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }

  /* Element Plus form item label styling */
  :deep(.el-form-item__label) {
    font-size: 12px;
    font-weight: 500;
    color: #666;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .stickers-layout {
      flex-direction: column;
      min-height: auto;
    }

    .stickers-left {
      flex: none;
      width: 100%;
    }

    .stickers-right {
      flex: none;
      width: 100%;
      margin-top: 20px;
    }

    .setting-row {
      flex-direction: column;
      gap: 10px;
    }
  }
</style>
