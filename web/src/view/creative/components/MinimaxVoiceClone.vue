<template>
  <div class="minimax-voice-clone">
    <el-card class="clone-form-card">
      <el-form ref="cloneForm" :model="formData" :rules="rules" label-width="120px" size="large">
        <!-- 文本输入 -->
        <el-form-item label="转换文本" prop="text">
          <el-input
            v-model="formData.text"
            type="textarea"
            :rows="4"
            placeholder="请输入要转换成语音的文本内容..."
            maxlength="5000"
            show-word-limit
            class="text-input"
          />
        </el-form-item>

        <!-- 音色选择 -->
        <el-form-item label="选择音色" :prop="voiceType === 'official' ? 'officialVoiceId' : 'referenceAudioId'">
          <div class="voice-selection">
            <!-- 音色类型选择 -->
            <el-radio-group v-model="voiceType" class="voice-type-selection" @change="handleVoiceTypeChange">
              <el-radio-button label="official">官方音色</el-radio-button>
              <el-radio-button label="custom">自定义音色</el-radio-button>
            </el-radio-group>

            <!-- 官方音色选择 -->
            <div v-if="voiceType === 'official'" class="official-voice-selection">
              <!-- 筛选标签 -->
              <div class="voice-filter-section">
                <el-row :gutter="10" style="margin-bottom: 15px">
                  <el-col :span="8">
                    <el-select
                      v-model="voiceFilters.languages"
                      placeholder="选择语言"
                      multiple
                      collapse-tags
                      @change="handleFilterChange"
                    >
                      <el-option v-for="lang in VOICE_TAGS.languages" :key="lang" :label="lang" :value="lang" />
                    </el-select>
                  </el-col>
                  <el-col :span="8">
                    <el-select
                      v-model="voiceFilters.genders"
                      placeholder="选择性别"
                      multiple
                      collapse-tags
                      @change="handleFilterChange"
                    >
                      <el-option v-for="gender in VOICE_TAGS.genders" :key="gender" :label="gender" :value="gender" />
                    </el-select>
                  </el-col>
                  <el-col :span="8">
                    <el-select
                      v-model="voiceFilters.ages"
                      placeholder="选择年龄"
                      multiple
                      collapse-tags
                      @change="handleFilterChange"
                    >
                      <el-option v-for="age in VOICE_TAGS.ages" :key="age" :label="age" :value="age" />
                    </el-select>
                  </el-col>
                </el-row>
                <el-row :gutter="10" style="margin-bottom: 15px">
                  <el-col :span="8">
                    <el-select
                      v-model="voiceFilters.styles"
                      placeholder="选择风格（可多选）"
                      multiple
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      @change="handleFilterChange"
                    >
                      <el-option v-for="style in VOICE_TAGS.styles" :key="style" :label="style" :value="style" />
                    </el-select>
                  </el-col>
                  <el-col :span="8">
                    <el-input
                      v-model="voiceFilters.searchText"
                      placeholder="搜索音色名称"
                      clearable
                      @input="handleFilterChange"
                    />
                  </el-col>
                  <el-col :span="6">
                    <el-checkbox v-model="voiceFilters.onlyFavorites" @change="handleFilterChange">
                      只显示收藏 ({{ favoriteVoices.length }})
                    </el-checkbox>
                  </el-col>
                </el-row>
              </div>

              <!-- 音色卡片列表 -->
              <div class="voice-cards-container">
                <div class="voice-cards-grid">
                  <div
                    v-for="voice in paginatedVoices"
                    :key="voice.uniq_id"
                    class="voice-card"
                    :class="{ selected: formData.officialVoiceId === voice.uniq_id }"
                    @click="handleOfficialVoiceSelect(voice.uniq_id)"
                  >
                    <div class="voice-card-header">
                      <h4 class="voice-card-title">{{ voice.voice_name }}</h4>
                      <div class="voice-card-actions">
                        <el-button
                          :type="isFavorite(voice.uniq_id) ? 'danger' : 'primary'"
                          size="small"
                          circle
                          @click.stop="
                            isFavorite(voice.uniq_id)
                              ? removeFromFavorites(voice.uniq_id)
                              : addToFavorites(voice.uniq_id)
                          "
                        >
                          <el-icon><StarFilled v-if="isFavorite(voice.uniq_id)" /><Star v-else /></el-icon>
                        </el-button>
                        <el-button
                          v-if="voice.sample_audio"
                          :type="playingStates.get(voice.sample_audio) ? 'warning' : 'success'"
                          size="small"
                          circle
                          @click.stop="toggleAudioPlay(voice.sample_audio, false)"
                        >
                          <el-icon v-if="playingStates.get(voice.sample_audio)"><VideoPause /></el-icon>
                          <el-icon v-else><VideoPlay /></el-icon>
                        </el-button>
                      </div>
                    </div>
                    <div class="voice-card-tags">
                      <el-tag
                        v-for="tag in voice.tag_list"
                        :key="tag"
                        size="small"
                        class="voice-tag"
                        :type="getTagType(tag)"
                      >
                        {{ tag }}
                      </el-tag>
                    </div>
                    <div class="voice-card-id">{{ voice.uniq_id }}</div>

                    <!-- 播放进度条 -->
                    <div v-if="voice.sample_audio && playingStates.get(voice.sample_audio)" class="voice-card-progress">
                      <div class="progress-info">
                        <span class="current-time">{{
                          formatTime(audioCurrentTime.get(voice.sample_audio) || 0)
                        }}</span>
                        <span class="duration">{{ formatTime(audioDuration.get(voice.sample_audio) || 0) }}</span>
                      </div>
                      <el-progress
                        :percentage="audioProgress.get(voice.sample_audio) || 0"
                        :show-text="false"
                        size="small"
                        class="audio-progress-bar"
                      />
                    </div>
                  </div>
                </div>

                <!-- 分页器 -->
                <div class="voice-pagination" v-if="filteredOfficialVoices.length > voicePagination.pageSize">
                  <el-pagination
                    :current-page="voicePagination.page"
                    :page-size="voicePagination.pageSize"
                    :page-sizes="[9, 12, 24, 48, 96]"
                    :total="filteredOfficialVoices.length"
                    layout="total, sizes, prev, pager, next, jumper"
                    :pager-count="5"
                    background
                    size="default"
                    @size-change="handleVoiceSizeChange"
                    @current-change="handleVoiceCurrentChange"
                    style="margin-top: 0"
                  />
                </div>
              </div>

              <!-- 已选择的音色信息 -->
              <div v-if="selectedOfficialVoice" class="selected-voice-info">
                <el-alert
                  :title="`已选择音色: ${selectedOfficialVoice.voice_name}`"
                  type="success"
                  show-icon
                  :closable="false"
                >
                  <template #default>
                    <div class="selected-voice-details">
                      <span class="voice-id">音色 ID: {{ selectedOfficialVoice.uniq_id }}</span>
                      <div class="voice-tags">
                        <el-tag
                          v-for="tag in selectedOfficialVoice.tag_list"
                          :key="tag"
                          size="small"
                          style="margin-right: 5px"
                          :type="getTagType(tag)"
                        >
                          {{ tag }}
                        </el-tag>
                      </div>
                    </div>
                  </template>
                </el-alert>
              </div>
            </div>

            <!-- 自定义音色选择 -->
            <div v-if="voiceType === 'custom'" class="custom-voice-selection">
              <el-cascader
                v-model="formData.referenceAudioPath"
                :options="voiceOptions"
                :props="cascaderProps"
                placeholder="请选择参考音色"
                style="width: 100%"
                filterable
                @change="handleVoiceSelect"
              />
              <div v-if="selectedVoice" class="selected-voice-preview">
                <div class="voice-info">
                  <span class="voice-name">{{ selectedVoice.originalName || selectedVoice.name }}</span>
                  <span class="voice-category">{{ selectedVoice.categoryName }}</span>
                  <span v-if="selectedVoice.minimaxVoiceId" class="voice-id">
                    音色 ID: {{ selectedVoice.minimaxVoiceId }}
                  </span>
                </div>
                <div class="voice-preview-controls">
                  <el-button
                    :type="playingStates.get(selectedVoice.fileUrl) ? 'warning' : 'success'"
                    size="small"
                    @click="toggleAudioPlay(selectedVoice.fileUrl, false)"
                  >
                    <el-icon v-if="playingStates.get(selectedVoice.fileUrl)"><VideoPause /></el-icon>
                    <el-icon v-else><VideoPlay /></el-icon>
                    {{ playingStates.get(selectedVoice.fileUrl) ? '暂停' : '播放' }}
                  </el-button>
                </div>
                <!-- 播放进度条 -->
                <div v-if="playingStates.get(selectedVoice.fileUrl)" class="voice-progress">
                  <div class="progress-info">
                    <span class="current-time">{{ formatTime(audioCurrentTime.get(selectedVoice.fileUrl) || 0) }}</span>
                    <span class="duration">{{ formatTime(audioDuration.get(selectedVoice.fileUrl) || 0) }}</span>
                  </div>
                  <el-progress
                    :percentage="audioProgress.get(selectedVoice.fileUrl) || 0"
                    :show-text="false"
                    size="small"
                    class="audio-progress-bar"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 背景音乐选择 -->
        <el-form-item label="背景音乐">
          <div class="background-music-selection">
            <!-- 选中的背景音乐预览 -->
            <div v-if="selectedBackgroundMusic" class="selected-music-preview">
              <div class="music-info">
                <div class="music-icon">
                  <el-icon><Headset /></el-icon>
                </div>
                <div class="music-details">
                  <div class="music-name">{{ selectedBackgroundMusic.name }}</div>
                  <div class="music-category">{{ selectedBackgroundMusic.categoryName }}</div>
                  <div class="music-duration-progress">
                    <!-- 播放进度条或时长显示 -->
                    <div v-if="playingStates.get(selectedBackgroundMusic.fileUrl)" class="music-progress">
                      <div class="progress-info">
                        <span class="current-time">{{
                          formatTime(audioCurrentTime.get(selectedBackgroundMusic.fileUrl) || 0)
                        }}</span>
                        <span class="duration">{{
                          formatTime(
                            audioDuration.get(selectedBackgroundMusic.fileUrl) || selectedBackgroundMusic.duration || 0
                          )
                        }}</span>
                      </div>
                      <el-slider
                        :model-value="audioProgress.get(selectedBackgroundMusic.fileUrl) || 0"
                        :min="0"
                        :max="100"
                        :step="0.1"
                        :show-tooltip="false"
                        size="small"
                        class="audio-progress-slider"
                        @input="handleProgressChange(selectedBackgroundMusic.fileUrl, $event)"
                        @change="handleProgressSet(selectedBackgroundMusic.fileUrl, $event)"
                      />
                    </div>
                    <div v-else-if="selectedBackgroundMusic.duration" class="music-duration">
                      时长: {{ formatDuration(selectedBackgroundMusic.duration) }}
                    </div>
                    <div v-else class="music-duration">时长: 未知</div>
                  </div>
                </div>
              </div>
              <div class="music-controls">
                <el-button
                  :type="playingStates.get(selectedBackgroundMusic.fileUrl) ? 'warning' : 'success'"
                  size="small"
                  @click="toggleAudioPlay(selectedBackgroundMusic.fileUrl, formData.backgroundMusicLoop)"
                  :title="formData.backgroundMusicLoop ? '循环播放模式' : '单次播放模式'"
                >
                  <el-icon v-if="playingStates.get(selectedBackgroundMusic.fileUrl)"><VideoPause /></el-icon>
                  <el-icon v-else><VideoPlay /></el-icon>
                  {{ playingStates.get(selectedBackgroundMusic.fileUrl) ? '暂停' : '播放' }}
                  <span v-if="formData.backgroundMusicLoop" style="margin-left: 2px">🔄</span>
                </el-button>
                <el-button size="small" @click="openBackgroundMusicDialog">
                  <el-icon><Plus /></el-icon>
                  更换
                </el-button>
                <el-button size="small" @click="clearBackgroundMusic">
                  <el-icon><Close /></el-icon>
                  清除
                </el-button>
              </div>
            </div>

            <!-- 背景音乐音量控制 -->
            <div v-if="selectedBackgroundMusic" class="music-volume-control">
              <div class="volume-label">
                <span>背景音乐音量</span>
                <el-tooltip
                  content="控制背景音乐相对于人声的音量大小。1.0为原声，小于1.0音量更小，大于1.0音量更大"
                  placement="top"
                >
                  <el-icon style="margin-left: 4px; color: var(--el-color-info)"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
              <div class="slider-container">
                <el-slider
                  v-model="formData.backgroundMusicVolume"
                  :min="0.1"
                  :max="2.0"
                  :step="0.1"
                  show-tooltip
                  class="custom-slider"
                />
                <span class="slider-value">{{ formData.backgroundMusicVolume }}</span>
              </div>
            </div>

            <!-- 背景音乐循环播放控制 -->
            <div v-if="selectedBackgroundMusic" class="music-loop-control">
              <el-checkbox v-model="formData.backgroundMusicLoop">
                <span>循环播放背景音乐</span>
                <el-tooltip content="开启后背景音乐将循环播放，直到语音结束。关闭后背景音乐只播放一次" placement="top">
                  <el-icon style="margin-left: 4px; color: var(--el-color-info)"><QuestionFilled /></el-icon>
                </el-tooltip>
              </el-checkbox>
            </div>

            <!-- 添加背景音乐按钮 -->
            <div v-if="!selectedBackgroundMusic" class="add-music-btn">
              <el-button type="primary" plain @click="openBackgroundMusicDialog">
                <el-icon><Plus /></el-icon>
                添加背景音乐
              </el-button>
            </div>
          </div>
        </el-form-item>

        <!-- 存放位置选择 -->
        <el-form-item label="存放位置" prop="targetCategoryId">
          <el-cascader
            v-model="formData.targetCategoryPath"
            :options="targetCategoryOptions"
            :props="targetCascaderProps"
            placeholder="请选择生成音频的存放分类"
            style="width: 100%"
            filterable
            @change="handleTargetCategorySelect"
          />
        </el-form-item>

        <!-- 高级设置 -->
        <el-form-item label="" prop="">
          <div class="advanced-settings-wrapper">
            <el-collapse v-model="advancedOpen">
              <el-collapse-item title="高级设置" name="advanced">
                <div class="advanced-settings-content">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="模型" label-width="120px">
                        <el-select v-model="formData.model" placeholder="选择模型" style="width: 100%">
                          <el-option label="speech-02-hd" value="speech-02-hd" />
                          <el-option label="speech-02-turbo" value="speech-02-turbo" />
                          <el-option label="speech-01-hd" value="speech-01-hd" />
                          <el-option label="speech-01-turbo" value="speech-01-turbo" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="音频格式" label-width="120px">
                        <el-select v-model="formData.format" placeholder="选择格式" style="width: 100%">
                          <el-option label="MP3" value="mp3" />
                          <el-option label="WAV" value="wav" />
                          <el-option label="PCM" value="pcm" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label-width="120px">
                        <template #label>
                          <span>语速</span>
                          <el-tooltip
                            content="控制语音播放速度。1.0为正常速度，小于1.0较慢，大于1.0较快"
                            placement="top"
                          >
                            <el-icon style="margin-left: 4px; color: var(--el-color-info)"><QuestionFilled /></el-icon>
                          </el-tooltip>
                        </template>
                        <div class="slider-container">
                          <el-slider
                            v-model="formData.speed"
                            :min="0.5"
                            :max="2.0"
                            :step="0.1"
                            show-tooltip
                            class="custom-slider"
                          />
                          <span class="slider-value">{{ formData.speed }}</span>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label-width="120px">
                        <template #label>
                          <span>音量</span>
                          <el-tooltip content="控制音频音量大小。1.0为正常音量" placement="top">
                            <el-icon style="margin-left: 4px; color: var(--el-color-info)"><QuestionFilled /></el-icon>
                          </el-tooltip>
                        </template>
                        <div class="slider-container">
                          <el-slider
                            v-model="formData.volume"
                            :min="0.1"
                            :max="10.0"
                            :step="0.1"
                            show-tooltip
                            class="custom-slider"
                          />
                          <span class="slider-value">{{ formData.volume }}</span>
                        </div>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label-width="120px">
                        <template #label>
                          <span>音调</span>
                          <el-tooltip
                            content="控制音频音调高低。0为正常音调，正值提高音调，负值降低音调"
                            placement="top"
                          >
                            <el-icon style="margin-left: 4px; color: var(--el-color-info)"><QuestionFilled /></el-icon>
                          </el-tooltip>
                        </template>
                        <div class="slider-container">
                          <el-slider
                            v-model="formData.pitch"
                            :min="-12"
                            :max="12"
                            :step="1"
                            show-tooltip
                            class="custom-slider"
                          />
                          <span class="slider-value">{{ formData.pitch }}</span>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="采样率" label-width="120px">
                        <el-select v-model="formData.audioSampleRate" placeholder="选择采样率" style="width: 100%">
                          <el-option label="16000 Hz" :value="16000" />
                          <el-option label="24000 Hz" :value="24000" />
                          <el-option label="32000 Hz" :value="32000" />
                          <el-option label="48000 Hz" :value="48000" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="比特率" label-width="120px">
                        <el-select v-model="formData.bitRate" placeholder="选择比特率" style="width: 100%">
                          <el-option label="64 kbps" :value="64000" />
                          <el-option label="128 kbps" :value="128000" />
                          <el-option label="192 kbps" :value="192000" />
                          <el-option label="256 kbps" :value="256000" />
                          <el-option label="320 kbps" :value="320000" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="submitClone" size="large">
            <el-icon><MagicStick /></el-icon>
            开始克隆
          </el-button>
          <el-button @click="resetForm" size="large">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 任务列表 -->
    <el-card class="task-list-card" v-if="tasks.length > 0">
      <template #header>
        <div class="card-header">
          <span>MiniMax 克隆任务</span>
          <el-button text @click="refreshTasks">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <el-table :data="tasks" style="width: 100%">
        <el-table-column prop="text" label="文本内容" min-width="200">
          <template #default="scope">
            <div class="text-preview">{{ scope.row.text.substring(0, 50) }}...</div>
          </template>
        </el-table-column>
        <el-table-column prop="referenceAudioName" label="声源" width="150">
          <template #default="scope">
            <span v-if="scope.row.referenceAudioName">{{ scope.row.referenceAudioName }}</span>
            <span v-else class="text-gray-400">--</span>
          </template>
        </el-table-column>
        <el-table-column prop="minimaxVoiceId" label="音色ID" width="150">
          <template #default="scope">
            <span v-if="scope.row.minimaxVoiceId" class="voice-id">{{ scope.row.minimaxVoiceId }}</span>
            <span v-else class="text-gray-400">--</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="时长" width="100">
          <template #default="scope">
            <span v-if="scope.row.duration">{{ formatDuration(scope.row.duration) }}</span>
            <span v-else class="text-gray-400">--</span>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="260">
          <template #default="scope">
            <div class="task-actions">
              <div class="action-buttons">
                <el-button
                  v-if="scope.row.audioUrl"
                  :type="playingStates.get(scope.row.audioUrl) ? 'warning' : 'success'"
                  size="small"
                  @click="toggleAudioPlay(scope.row.audioUrl, false)"
                >
                  <el-icon v-if="playingStates.get(scope.row.audioUrl)"><VideoPause /></el-icon>
                  <el-icon v-else><VideoPlay /></el-icon>
                  {{ playingStates.get(scope.row.audioUrl) ? '暂停' : '播放' }}
                </el-button>
                <el-button v-if="scope.row.audioUrl" type="primary" size="small" @click="downloadAudio(scope.row)">
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
                <el-button
                  v-if="scope.row.status === 'RUNNING' || scope.row.status === 'PENDING'"
                  type="info"
                  size="small"
                  @click="checkTaskStatus(scope.row.taskId)"
                >
                  <el-icon><Refresh /></el-icon>
                  刷新状态
                </el-button>
              </div>
              <!-- 播放进度条 -->
              <div v-if="scope.row.audioUrl && playingStates.get(scope.row.audioUrl)" class="task-progress">
                <div class="progress-info">
                  <span class="current-time">{{ formatTime(audioCurrentTime.get(scope.row.audioUrl) || 0) }}</span>
                  <span class="duration">{{ formatTime(audioDuration.get(scope.row.audioUrl) || 0) }}</span>
                </div>
                <el-progress
                  :percentage="audioProgress.get(scope.row.audioUrl) || 0"
                  :show-text="false"
                  size="small"
                  class="audio-progress-bar"
                />
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          :current-page="pagination.page"
          :page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          :pager-count="5"
          background
          size="default"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 背景音乐选择对话框 -->
    <el-dialog v-model="backgroundMusicDialogVisible" title="选择背景音乐" width="1000px" :close-on-click-modal="false">
      <div class="background-music-dialog">
        <div class="music-selection-layout">
          <!-- 左侧：分类选择 -->
          <div class="category-selection-panel">
            <div class="panel-header">
              <h4>音乐分类</h4>
            </div>
            <div class="category-tree">
              <el-cascader
                v-model="selectedCategoryPath"
                :options="musicCategoryOptions"
                :props="categoryProps"
                placeholder="请选择分类"
                style="width: 100%"
                filterable
                clearable
                @change="handleCategorySelect"
                :show-all-levels="false"
              />
            </div>
          </div>

          <!-- 右侧：音乐列表 -->
          <div class="music-list-panel">
            <div class="panel-header">
              <h4>音乐列表</h4>
              <span v-if="currentCategoryMusic.length > 0" class="music-count">
                ({{ currentCategoryMusic.length }}首)
              </span>
            </div>
            <div class="music-list">
              <div v-if="loadingMusic" class="loading-placeholder">
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>加载中...</span>
              </div>
              <div v-else-if="currentCategoryMusic.length === 0" class="empty-placeholder">
                <el-icon><Headset /></el-icon>
                <span>{{ selectedCategoryPath ? '该分类下暂无音乐' : '请先选择分类' }}</span>
              </div>
              <div v-else class="music-items">
                <div
                  v-for="music in currentCategoryMusic"
                  :key="music.ID"
                  class="music-item"
                  :class="{ selected: tempSelectedBackgroundMusic?.ID === music.ID }"
                  @click="selectMusic(music)"
                >
                  <div class="music-item-info">
                    <div class="music-item-name" :title="music.name">{{ music.name }}</div>
                    <div class="music-item-meta">
                      <span v-if="music.duration" class="music-duration">
                        {{ formatDuration(music.duration) }}
                      </span>
                    </div>
                  </div>
                  <div class="music-item-actions">
                    <el-button
                      :type="playingStates.get(music.fileUrl) ? 'warning' : 'success'"
                      size="small"
                      circle
                      @click.stop="toggleAudioPlay(music.fileUrl, true)"
                      title="循环播放模式"
                    >
                      <el-icon v-if="playingStates.get(music.fileUrl)"><VideoPause /></el-icon>
                      <el-icon v-else><VideoPlay /></el-icon>
                    </el-button>
                  </div>
                  <!-- 播放进度条 -->
                  <div v-if="playingStates.get(music.fileUrl)" class="music-item-progress">
                    <el-progress
                      :percentage="audioProgress.get(music.fileUrl) || 0"
                      :show-text="false"
                      size="small"
                      class="small-progress-bar"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 预选音乐预览 -->
        <div v-if="tempSelectedBackgroundMusic" class="temp-music-preview">
          <div class="music-preview-card">
            <div class="music-info">
              <div class="music-icon">
                <el-icon><Headset /></el-icon>
              </div>
              <div class="music-details">
                <div class="music-name">{{ tempSelectedBackgroundMusic.name }}</div>
                <div class="music-category">{{ tempSelectedBackgroundMusic.categoryName }}</div>
                <div class="music-duration-progress">
                  <!-- 播放进度条或时长显示 -->
                  <div v-if="playingStates.get(tempSelectedBackgroundMusic.fileUrl)" class="music-progress">
                    <div class="progress-info">
                      <span class="current-time">{{
                        formatTime(audioCurrentTime.get(tempSelectedBackgroundMusic.fileUrl) || 0)
                      }}</span>
                      <span class="duration">{{
                        formatTime(
                          audioDuration.get(tempSelectedBackgroundMusic.fileUrl) ||
                            tempSelectedBackgroundMusic.duration ||
                            0
                        )
                      }}</span>
                    </div>
                    <el-slider
                      :model-value="audioProgress.get(tempSelectedBackgroundMusic.fileUrl) || 0"
                      :min="0"
                      :max="100"
                      :step="0.1"
                      :show-tooltip="false"
                      size="small"
                      class="audio-progress-slider"
                      @input="handleProgressChange(tempSelectedBackgroundMusic.fileUrl, $event)"
                      @change="handleProgressSet(tempSelectedBackgroundMusic.fileUrl, $event)"
                    />
                  </div>
                  <div v-else-if="tempSelectedBackgroundMusic.duration" class="music-duration">
                    时长: {{ formatDuration(tempSelectedBackgroundMusic.duration) }}
                  </div>
                  <div v-else class="music-duration">时长: 未知</div>
                </div>
              </div>
            </div>
            <div class="music-controls">
              <el-button
                :type="playingStates.get(tempSelectedBackgroundMusic.fileUrl) ? 'warning' : 'success'"
                size="small"
                @click="toggleAudioPlay(tempSelectedBackgroundMusic.fileUrl, true)"
                title="循环播放模式"
              >
                <el-icon v-if="playingStates.get(tempSelectedBackgroundMusic.fileUrl)"><VideoPause /></el-icon>
                <el-icon v-else><VideoPlay /></el-icon>
                {{ playingStates.get(tempSelectedBackgroundMusic.fileUrl) ? '暂停' : '播放' }}
                <span style="margin-left: 2px">🔄</span>
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelBackgroundMusicSelection">取消</el-button>
          <el-button type="primary" @click="confirmBackgroundMusicSelection" :disabled="!tempSelectedBackgroundMusic">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, computed } from 'vue'
  import { ElMessage } from 'element-plus'
  import {
    MagicStick,
    Refresh,
    VideoPlay,
    VideoPause,
    Download,
    QuestionFilled,
    Star,
    StarFilled,
    Headset,
    Plus,
    Close,
    Loading
  } from '@element-plus/icons-vue'
  import {
    createMinimaxVoiceCloneTask,
    getMinimaxVoiceCloneTaskList,
    getMinimaxVoiceCloneTaskStatus
  } from '@/api/ai/minimaxVoiceClone'
  import { MINIMAX_VOICES, VOICE_TAGS, getChineseVoices, advancedFilter } from '@/api/minimaxVoices'
  import { getMusicList } from '@/api/media/music'
  import { getCategoryList } from '@/api/media/musicCategory'
  import { formatDate } from '@/utils/format'
  import { downloadFile } from '@/utils/downloadFile'
  import { addFavoriteVoice, removeFavoriteVoice, getFavoriteVoices } from '@/api/minimaxVoiceFavorite'
  import { useUserStore } from '@/pinia/modules/user'

  defineOptions({
    name: 'MinimaxVoiceClone'
  })

  // 获取用户store
  const userStore = useUserStore()

  // 表单数据
  const formData = reactive({
    text: '',
    referenceAudioId: null,
    referenceAudioPath: [],
    officialVoiceId: null,
    targetCategoryId: null,
    targetCategoryPath: [],
    model: 'speech-02-hd',
    speed: 1.0,
    volume: 1.0,
    pitch: 0,
    audioSampleRate: 32000,
    bitRate: 128000,
    format: 'mp3',
    // 背景音乐相关字段
    backgroundMusicId: null,
    backgroundMusicPath: [],
    backgroundMusicVolume: 0.2, // 背景音乐音量，原声为1，默认0.2为20%
    backgroundMusicLoop: true // 背景音乐循环播放，默认开启
  })

  // 表单校验规则
  const rules = computed(() => {
    const baseRules = {
      text: [
        { required: true, message: '请输入要转换的文本', trigger: 'blur' },
        { min: 1, max: 5000, message: '文本长度应在1-5000字符之间', trigger: 'blur' }
      ],
      targetCategoryId: [{ required: true, message: '请选择存放位置', trigger: 'change' }]
    }

    // 根据音色类型动态添加校验规则
    if (voiceType.value === 'official') {
      baseRules.officialVoiceId = [{ required: true, message: '请选择官方音色', trigger: 'change' }]
    } else if (voiceType.value === 'custom') {
      baseRules.referenceAudioId = [{ required: true, message: '请选择参考音色', trigger: 'change' }]
    }

    return baseRules
  })

  // 组件状态
  const loading = ref(false)
  const advancedOpen = ref([])
  const cloneForm = ref()

  // 音频播放状态
  const playingAudios = ref(new Map()) // 存储正在播放的音频实例
  const playingStates = ref(new Map()) // 存储播放状态
  const audioProgress = ref(new Map()) // 存储音频播放进度
  const audioDuration = ref(new Map()) // 存储音频总时长
  const audioCurrentTime = ref(new Map()) // 存储音频当前播放时间

  // 级联选择器配置
  const cascaderProps = {
    value: 'ID',
    label: 'name',
    children: 'children',
    emitPath: false
  }

  // 目标分类选择器配置（支持选择任意级别）
  const targetCascaderProps = {
    value: 'ID',
    label: 'name',
    children: 'children',
    emitPath: false,
    checkStrictly: true, // 允许选择任意级别的分类，不必选择到叶子节点
    expandTrigger: 'hover' // 鼠标悬停展开子分类
  }

  // 音色选项和目标分类选项
  const voiceOptions = ref([])
  const targetCategoryOptions = ref([])
  const selectedVoice = ref(null)

  // 背景音乐相关变量
  const backgroundMusicOptions = ref([])
  const selectedBackgroundMusic = ref(null)
  const backgroundMusicDialogVisible = ref(false)
  const backgroundMusicMap = ref(new Map())

  // 新的分类和音乐选择变量
  const musicCategoryOptions = ref([])
  const selectedCategoryPath = ref(null)
  const currentCategoryMusic = ref([])
  const loadingMusic = ref(false)

  // 对话框临时选择变量
  const tempSelectedBackgroundMusic = ref(null)

  // 分类选择器配置
  const categoryProps = {
    value: 'ID',
    label: 'name',
    children: 'children',
    emitPath: true,
    checkStrictly: true, // 允许选择任意级别的分类，不必选择到叶子节点
    expandTrigger: 'hover' // 鼠标悬停展开子分类
  }

  // 任务列表
  const tasks = ref([])
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0
  })

  // 音乐数据映射
  const musicMap = ref(new Map())

  // 音乐类型选择
  const voiceType = ref('official')

  // 官方音色列表 - 使用本地音色库
  const selectedOfficialVoice = ref(null)
  const filteredOfficialVoices = ref(MINIMAX_VOICES)

  // 音色筛选条件
  const voiceFilters = reactive({
    languages: ['中文-普通话'], // 默认选中中文普通话
    genders: [],
    ages: [],
    styles: [],
    regions: [],
    searchText: '',
    onlyFavorites: false // 是否只显示收藏
  })

  // 收藏音色列表
  const favoriteVoices = ref([])
  const loadingFavorites = ref(false)

  // 音色列表分页
  const voicePagination = reactive({
    page: 1,
    pageSize: 9,
    total: 0
  })

  // 获取音乐分类列表（完全按照music.vue的方式）
  const categories = ref([])
  const fetchCategories = async () => {
    try {
      const res = await getCategoryList()
      let data = {
        name: '全部分类',
        ID: 0,
        pid: 0,
        children: []
      }
      if (res.code === 0) {
        categories.value = res.data || []
        categories.value.unshift(data)
        console.log('fetchCategories - 完整分类数据:', categories.value)
        console.log('当前用户信息:', userStore.userInfo)

        // 记录每个分类的权限信息
        console.log('📊 分类权限统计:')
        let totalCategories = 0
        let publicCategories = 0
        let ownCategories = 0
        let availableCategories = 0

        const currentUserId = userStore.userInfo?.ID
        console.log(`当前用户ID: ${currentUserId}`)

        categories.value.forEach((cat) => {
          if (cat.ID > 0) {
            totalCategories++
            const isPublic = cat.visibility === 0
            const isOwn = cat.creator === currentUserId
            const shouldShow = isPublic || isOwn

            if (isPublic) publicCategories++
            if (isOwn) ownCategories++
            if (shouldShow) availableCategories++

            console.log(`分类 ${cat.name} (ID: ${cat.ID}):`, {
              visibility: cat.visibility,
              creator: cat.creator,
              isPublic,
              isOwn,
              shouldShow
            })
          }
        })

        console.log(`📈 统计结果:`)
        console.log(`  总分类数: ${totalCategories}`)
        console.log(`  公共分类: ${publicCategories}`)
        console.log(`  个人分类: ${ownCategories}`)
        console.log(`  可用分类: ${availableCategories}`)
        console.log(`----`)

        return categories.value
      }
      return [data]
    } catch (error) {
      console.error('获取分类失败:', error)
      return []
    }
  }

  // 获取指定分类下的音乐列表
  const fetchMusicByCategory = async (categoryId) => {
    try {
      const res = await getMusicList({
        page: 1,
        pageSize: 1000, // 获取所有音乐
        categoryId: categoryId
      })
      if (res.code === 0 && res.data) {
        return res.data.list || []
      }
      return []
    } catch (error) {
      console.error('获取音乐失败:', error)
      return []
    }
  }

  // 检查分类是否为ID42或其子分类
  const isVoiceCategoryOrChild = (categoryId, categories) => {
    if (categoryId === 42) return true

    const findInCategories = (cats) => {
      for (const cat of cats) {
        if (cat.ID === 42) {
          // 检查是否为42的子分类
          const checkChildren = (children) => {
            for (const child of children || []) {
              if (child.ID === categoryId) return true
              if (child.children && checkChildren(child.children)) return true
            }
            return false
          }
          return checkChildren(cat.children || [])
        }
        if (cat.children && findInCategories(cat.children)) return true
      }
      return false
    }

    return findInCategories(categories)
  }

  // 构建音色选项（仅ID42及其子分类，且用户可访问）
  const buildVoiceOptions = async (items = categories.value) => {
    const options = []

    const processCategory = async (cat, parentPath = []) => {
      const currentPath = [...parentPath, cat]
      const isVoiceCategory = isVoiceCategoryOrChild(cat.ID, categories.value)
      // 检查用户权限
      const hasPermission = isUserAvailableCategory(cat)

      if (isVoiceCategory && cat.ID !== 0 && hasPermission) {
        // 获取该分类下的音乐
        const musicList = await fetchMusicByCategory(cat.ID)

        if (musicList.length > 0) {
          // 先处理音乐数据，确保结构一致
          const processedMusicList = musicList.map((music) => {
            // 构建显示名称，如果有MiniMax ID则在名称后面显示
            let displayName = music.name
            if (music.minimaxVoiceId && music.minimaxVoiceId.trim() !== '') {
              displayName = `${music.name}`
            }

            const processedMusic = {
              // 保留所有原始字段
              ...music,
              // 然后覆盖需要处理的字段
              name: displayName, // 使用包含MiniMax ID的显示名称
              originalName: music.name, // 保留原始名称
              categoryName: cat.name,
              fileUrl:
                music.fileUrl?.indexOf('http') === 0
                  ? music.fileUrl
                  : import.meta.env.VITE_BASE_API + '/' + music.fileUrl,
              isMusic: true
            }

            // 将音乐添加到映射中
            musicMap.value.set(music.ID, processedMusic)

            return processedMusic
          })

          // 为分类名称添加公共/个人图标
          const categoryIcon = cat.visibility === 0 ? '🌐' : '👤'
          const categoryDisplayName = `${categoryIcon} ${cat.name}`

          const categoryOption = {
            ID: cat.ID,
            name: categoryDisplayName,
            originalName: cat.name,
            visibility: cat.visibility,
            creator: cat.creator,
            children: processedMusicList
          }

          options.push(categoryOption)
        }
      }

      // 递归处理子分类
      if (cat.children) {
        for (const child of cat.children) {
          await processCategory(child, currentPath)
        }
      }
    }

    for (const category of items) {
      if (category.ID !== 0) {
        // 排除"全部分类"
        await processCategory(category)
      }
    }

    return options
  }

  // 检查分类是否为用户可用的分类（公共分类或用户自己的分类）
  const isUserAvailableCategory = (category) => {
    const currentUserId = userStore.userInfo?.ID

    console.log(`🔍 权限检查开始 - 分类: ${category.name} (ID: ${category.ID})`)
    console.log(`  当前用户ID: ${currentUserId}`)
    console.log(`  分类可见性: ${category.visibility} (0=公共, 1=个人)`)
    console.log(`  分类创建者: ${category.creator}`)

    if (!currentUserId || !category) {
      console.log(`  ❌ 权限检查失败: 缺少用户ID或分类信息`)
      return false
    }

    // 公共分类（visibility = 0）或用户自己创建的分类（creator = 当前用户ID）
    const isPublic = category.visibility === 0
    const isOwn = category.creator === currentUserId
    const isAvailable = isPublic || isOwn

    console.log(`  ${isPublic ? '✅' : '❌'} 是公共分类: ${isPublic}`)
    console.log(`  ${isOwn ? '✅' : '❌'} 是自己的分类: ${isOwn}`)
    console.log(`  ${isAvailable ? '✅ 通过' : '❌ 拒绝'} 最终结果: ${isAvailable}`)
    console.log(`----`)

    return isAvailable
  }

  // 构建音乐分类选项（纯分类树，不包含音乐）
  const buildMusicCategoryOptions = (items = categories.value) => {
    console.log('🎼 构建音乐分类选项 - 输入分类:', items.length)

    const options = []
    let processedCount = 0
    let voiceCategoryCount = 0
    let noPermissionCount = 0
    let successCount = 0

    // 递归构建分类树
    const buildCategoryTree = (cat) => {
      // 检查是否为音色分类（ID42及其子分类）
      const isVoiceCategory = isVoiceCategoryOrChild(cat.ID, categories.value)
      if (isVoiceCategory) {
        return null
      }

      // 检查用户权限
      const hasPermission = isUserAvailableCategory(cat)
      if (!hasPermission) {
        return null
      }

      // 为分类名称添加公共/个人图标
      const categoryIcon = cat.visibility === 0 ? '🌐' : '👤'
      const categoryDisplayName = `${categoryIcon} ${cat.name}`

      const option = {
        ID: cat.ID,
        name: categoryDisplayName,
        originalName: cat.name,
        visibility: cat.visibility,
        creator: cat.creator,
        children: []
      }

      // 递归处理子分类
      if (cat.children && cat.children.length > 0) {
        cat.children.forEach((child) => {
          const childOption = buildCategoryTree(child)
          if (childOption) {
            option.children.push(childOption)
          }
        })
      }

      return option
    }

    // 处理所有分类
    for (const category of items) {
      if (category.ID === 0) continue // 跳过"全部分类"

      processedCount++
      console.log(`🔄 处理分类 ${processedCount}: ${category.name} (ID: ${category.ID})`)

      const categoryTree = buildCategoryTree(category)
      if (categoryTree) {
        successCount++
        console.log(`  ✅ 添加分类: ${category.name}`)
        options.push(categoryTree)
      } else {
        const isVoiceCategory = isVoiceCategoryOrChild(category.ID, categories.value)
        if (isVoiceCategory) {
          voiceCategoryCount++
          console.log(`  🎤 跳过音色分类: ${category.name}`)
        } else {
          noPermissionCount++
          console.log(`  🚫 跳过无权限分类: ${category.name}`)
        }
      }
    }

    console.log(`🎼 音乐分类处理总结:`)
    console.log(`  处理总数: ${processedCount}`)
    console.log(`  音色分类: ${voiceCategoryCount}`)
    console.log(`  无权限: ${noPermissionCount}`)
    console.log(`  成功加载: ${successCount}`)
    console.log(`  最终选项: ${options.length}`)
    console.log('构建音乐分类选项 - 结果:', options)
    return options
  }

  // 构建背景音乐分类选项（只构建分类结构，不加载音乐）
  const buildBackgroundMusicCategoryOptions = (items = categories.value) => {
    console.log('🎵 构建背景音乐分类选项 - 输入分类:', items.length)

    const options = []
    let processedCount = 0
    let voiceCategoryCount = 0
    let noPermissionCount = 0
    let successCount = 0

    // 递归构建分类树（只包含分类，不包含音乐）
    const buildCategoryTree = (cat) => {
      // 检查是否为音色分类（ID42及其子分类）
      const isVoiceCategory = isVoiceCategoryOrChild(cat.ID, categories.value)
      // 检查用户权限
      const hasPermission = isUserAvailableCategory(cat)

      if (!isVoiceCategory && hasPermission) {
        // 为分类名称添加公共/个人图标
        const categoryIcon = cat.visibility === 0 ? '🌐' : '👤'
        const categoryDisplayName = `${categoryIcon} ${cat.name}`

        const option = {
          ID: cat.ID,
          name: categoryDisplayName,
          originalName: cat.name,
          visibility: cat.visibility,
          creator: cat.creator,
          children: [] // 不加载音乐，保持为空
        }

        // 递归处理子分类
        if (cat.children && cat.children.length > 0) {
          cat.children.forEach((child) => {
            const childOption = buildCategoryTree(child)
            if (childOption) {
              option.children.push(childOption)
            }
          })
        }

        return option
      }

      return null
    }

    // 只处理顶级分类
    for (const topCategory of items) {
      if (topCategory.ID === 0) continue // 跳过"全部分类"

      processedCount++
      console.log(`🔄 处理顶级分类 ${processedCount}: ${topCategory.name} (ID: ${topCategory.ID})`)

      // 检查是否为音色分类（ID42及其子分类）
      const isVoiceCategory = isVoiceCategoryOrChild(topCategory.ID, categories.value)
      if (isVoiceCategory) {
        voiceCategoryCount++
        console.log(`  🎤 跳过音色分类: ${topCategory.name}`)
        continue
      }

      // 检查用户权限
      const hasPermission = isUserAvailableCategory(topCategory)
      if (!hasPermission) {
        noPermissionCount++
        console.log(`  🚫 跳过无权限分类: ${topCategory.name}`)
        continue
      }

      // 构建分类树（包含子分类）
      const categoryTree = buildCategoryTree(topCategory)
      if (categoryTree) {
        successCount++
        console.log(`  ✅ 添加分类树: ${topCategory.name}`)
        options.push(categoryTree)
      }
    }

    console.log(`🎵 背景音乐分类处理总结:`)
    console.log(`  处理总数: ${processedCount}`)
    console.log(`  音色分类: ${voiceCategoryCount}`)
    console.log(`  无权限: ${noPermissionCount}`)
    console.log(`  成功加载: ${successCount}`)
    console.log(`  最终选项: ${options.length}`)
    console.log('构建背景音乐分类选项 - 结果:', options)
    return options
  }

  // 构建目标分类选项（只显示顶级分类，支持级联选择子分类）
  const buildTargetCategoryOptions = (items = categories.value) => {
    console.log('🎯 构建目标分类选项 - 输入分类:', items.length)

    const options = []
    let processedCount = 0
    let voiceCategoryCount = 0
    let noPermissionCount = 0
    let successCount = 0

    // 递归构建分类树（包含子分类）
    const buildCategoryTree = (cat) => {
      // 检查是否为音色分类（ID42及其子分类）
      const isVoiceCategory = isVoiceCategoryOrChild(cat.ID, categories.value)
      // 检查用户权限
      const hasPermission = isUserAvailableCategory(cat)

      if (!isVoiceCategory && hasPermission) {
        // 为分类名称添加公共/个人图标
        const categoryIcon = cat.visibility === 0 ? '🌐' : '👤'
        const categoryDisplayName = `${categoryIcon} ${cat.name}`

        const option = {
          ID: cat.ID,
          name: categoryDisplayName,
          originalName: cat.name,
          visibility: cat.visibility,
          creator: cat.creator,
          children: []
        }

        // 递归处理子分类
        if (cat.children && cat.children.length > 0) {
          cat.children.forEach((child) => {
            const childOption = buildCategoryTree(child)
            if (childOption) {
              option.children.push(childOption)
            }
          })
        }

        return option
      }

      return null
    }

    // 只处理顶级分类
    for (const topCategory of items) {
      if (topCategory.ID === 0) continue // 跳过"全部分类"

      processedCount++
      console.log(`🔄 处理顶级分类 ${processedCount}: ${topCategory.name} (ID: ${topCategory.ID})`)

      // 检查是否为音色分类（ID42及其子分类）
      const isVoiceCategory = isVoiceCategoryOrChild(topCategory.ID, categories.value)
      if (isVoiceCategory) {
        voiceCategoryCount++
        console.log(`  🎤 跳过音色分类: ${topCategory.name}`)
        continue
      }

      // 检查用户权限
      const hasPermission = isUserAvailableCategory(topCategory)
      if (!hasPermission) {
        noPermissionCount++
        console.log(`  🚫 跳过无权限分类: ${topCategory.name}`)
        continue
      }

      // 构建分类树（包含子分类）
      const categoryTree = buildCategoryTree(topCategory)
      if (categoryTree) {
        successCount++
        console.log(`  ✅ 添加分类树: ${topCategory.name}`)
        options.push(categoryTree)
      }
    }

    console.log(`🎯 目标分类处理总结:`)
    console.log(`  处理总数: ${processedCount}`)
    console.log(`  音色分类: ${voiceCategoryCount}`)
    console.log(`  无权限: ${noPermissionCount}`)
    console.log(`  成功加载: ${successCount}`)
    console.log(`  最终选项: ${options.length}`)
    console.log('构建目标分类选项 - 结果:', options)
    return options
  }

  // 初始化数据
  const initData = async () => {
    console.log('开始初始化数据...')
    console.log('当前用户信息:', userStore.userInfo)

    await fetchCategories()
    console.log('获取到的分类数据:', categories.value)

    voiceOptions.value = await buildVoiceOptions()
    console.log('构建的音色选项:', voiceOptions.value)

    // 构建分类选项用于左右分栏选择
    musicCategoryOptions.value = buildMusicCategoryOptions()
    console.log('构建的音乐分类选项:', musicCategoryOptions.value)

    // 构建背景音乐分类选项（只构建分类结构，不加载音乐）
    backgroundMusicOptions.value = buildBackgroundMusicCategoryOptions()
    console.log('构建的背景音乐选项:', backgroundMusicOptions.value)

    targetCategoryOptions.value = buildTargetCategoryOptions()
    console.log('构建的目标分类选项:', targetCategoryOptions.value)

    await fetchOfficialVoices()
    await fetchTasks()
    await fetchFavoriteVoices() // 获取收藏列表
    // 应用默认筛选条件
    handleFilterChange()
  }

  // 获取收藏音色列表
  const fetchFavoriteVoices = async () => {
    try {
      loadingFavorites.value = true
      const res = await getFavoriteVoices()
      if (res.code === 0) {
        favoriteVoices.value = res.data || []
        // 如果有收藏音色，默认开启收藏筛选
        if (favoriteVoices.value.length > 0) {
          voiceFilters.onlyFavorites = true
        }
      }
    } catch (error) {
      console.error('获取收藏音色失败:', error)
    } finally {
      loadingFavorites.value = false
    }
  }

  // 添加收藏
  const addToFavorites = async (voiceId) => {
    try {
      const res = await addFavoriteVoice({ voiceId })
      if (res.code === 0) {
        favoriteVoices.value.push(voiceId)
        ElMessage.success('收藏成功')
      } else {
        ElMessage.error(res.msg || '收藏失败')
      }
    } catch (error) {
      console.error('收藏失败:', error)
      ElMessage.error('收藏失败')
    }
  }

  // 取消收藏
  const removeFromFavorites = async (voiceId) => {
    try {
      const res = await removeFavoriteVoice({ voiceId })
      if (res.code === 0) {
        const index = favoriteVoices.value.indexOf(voiceId)
        if (index > -1) {
          favoriteVoices.value.splice(index, 1)
        }
        ElMessage.success('取消收藏成功')
      } else {
        ElMessage.error(res.msg || '取消收藏失败')
      }
    } catch (error) {
      console.error('取消收藏失败:', error)
      ElMessage.error('取消收藏失败')
    }
  }

  // 检查是否已收藏
  const isFavorite = (voiceId) => {
    return favoriteVoices.value.includes(voiceId)
  }

  // 音色筛选处理
  const handleFilterChange = () => {
    let filtered = advancedFilter({
      languages: voiceFilters.languages,
      genders: voiceFilters.genders,
      ages: voiceFilters.ages,
      styles: voiceFilters.styles,
      regions: voiceFilters.regions,
      searchText: voiceFilters.searchText
    })

    // 如果开启了只显示收藏，再过滤收藏
    if (voiceFilters.onlyFavorites) {
      filtered = filtered.filter((voice) => favoriteVoices.value.includes(voice.uniq_id))
    }

    filteredOfficialVoices.value = filtered
    // 重置分页
    voicePagination.page = 1
    voicePagination.total = filteredOfficialVoices.value.length
  }

  // 计算当前页的音色数据
  const paginatedVoices = computed(() => {
    const start = (voicePagination.page - 1) * voicePagination.pageSize
    const end = start + voicePagination.pageSize
    return filteredOfficialVoices.value.slice(start, end)
  })

  // 标签类型判断
  const getTagType = (tag) => {
    if (tag.includes('语') || tag.includes('中文') || tag.includes('英语')) return 'primary'
    if (tag === '男' || tag === '女') return 'success'
    if (['少年', '青年', '中年', '老年'].includes(tag)) return 'warning'
    if (tag.includes('中国-')) return 'info'
    return ''
  }

  // 音色分页处理
  const handleVoiceSizeChange = (val) => {
    voicePagination.pageSize = val
    voicePagination.page = 1
  }

  const handleVoiceCurrentChange = (val) => {
    voicePagination.page = val
  }

  // 获取官方音色列表（使用本地数据）
  const fetchOfficialVoices = async () => {
    // 不再需要API调用，使用本地音色库
    // 默认显示中文普通话音色
    filteredOfficialVoices.value = getChineseVoices()
    voicePagination.total = filteredOfficialVoices.value.length
  }

  // 处理音色选择
  const handleVoiceSelect = (value) => {
    if (value) {
      formData.referenceAudioId = value
      selectedVoice.value = musicMap.value.get(value)
      // 更新分类名称显示，使用原始名称
      if (selectedVoice.value) {
        const category = voiceOptions.value.find((cat) => cat.children?.some((music) => music.ID === value))
        if (category) {
          selectedVoice.value.categoryName = category.originalName || category.name
        }
      }
    } else {
      formData.referenceAudioId = null
      selectedVoice.value = null
    }

    // 清除校验错误
    cloneForm.value?.clearValidate(['referenceAudioId', 'officialVoiceId'])
  }

  // 处理目标分类选择
  const handleTargetCategorySelect = (value) => {
    // 当 checkStrictly: true 时，value 可能是数组（路径）或单个值
    if (Array.isArray(value)) {
      // 如果是数组，取最后一个元素作为目标分类ID
      formData.targetCategoryId = value[value.length - 1]
    } else {
      // 如果是单个值，直接使用
      formData.targetCategoryId = value
    }
    console.log('选择的目标分类ID:', formData.targetCategoryId)
  }

  // 处理分类选择
  const handleCategorySelect = async (value) => {
    console.log('🎯 分类选择变化:', value)

    if (value && value.length > 0) {
      // 获取当前选中的分类ID（可以是任意级别）
      const categoryId = value[value.length - 1]
      await loadMusicByCategory(categoryId)
    } else {
      currentCategoryMusic.value = []
    }
  }

  // 加载指定分类下的音乐
  const loadMusicByCategory = async (categoryId) => {
    console.log('📂 加载分类音乐:', categoryId)

    loadingMusic.value = true
    try {
      const musicList = await fetchMusicByCategory(categoryId)

      currentCategoryMusic.value = musicList.map((music) => {
        const processedMusic = {
          ...music,
          name: music.name,
          originalName: music.name,
          fileUrl:
            music.fileUrl?.indexOf('http') === 0 ? music.fileUrl : import.meta.env.VITE_BASE_API + '/' + music.fileUrl
        }

        // 确保音乐存在于映射中
        musicMap.value.set(music.ID, processedMusic)
        backgroundMusicMap.value.set(music.ID, processedMusic)

        return processedMusic
      })

      console.log(`✅ 加载完成，找到 ${currentCategoryMusic.value.length} 首音乐`)
    } catch (error) {
      console.error('❌ 加载音乐失败:', error)
      currentCategoryMusic.value = []
    } finally {
      loadingMusic.value = false
    }
  }

  // 选择音乐
  const selectMusic = (music) => {
    console.log('🎵 选择音乐:', music.name)

    tempSelectedBackgroundMusic.value = {
      ...music,
      categoryName: getCategoryNameFromPath(selectedCategoryPath.value)
    }
  }

  // 根据分类路径获取分类名称
  const getCategoryNameFromPath = (categoryPath) => {
    if (!categoryPath || categoryPath.length === 0) return ''

    // 获取最后一级分类的名称
    const findCategoryName = (categories, path, index = 0) => {
      if (index >= path.length) return ''

      const targetId = path[index]
      const category = categories.find((cat) => cat.ID === targetId)

      if (!category) return ''

      if (index === path.length - 1) {
        // 最后一级，返回原始名称
        return category.originalName || category.name
      }

      // 递归查找子分类
      if (category.children) {
        return findCategoryName(category.children, path, index + 1)
      }

      return category.originalName || category.name
    }

    return findCategoryName(musicCategoryOptions.value, categoryPath)
  }

  // 清除背景音乐
  const clearBackgroundMusic = () => {
    formData.backgroundMusicId = null
    formData.backgroundMusicPath = []
    formData.backgroundMusicLoop = true // 重置为默认值
    selectedBackgroundMusic.value = null
  }

  // 打开背景音乐选择对话框
  const openBackgroundMusicDialog = () => {
    // 重置临时选择状态
    selectedCategoryPath.value = null
    currentCategoryMusic.value = []
    tempSelectedBackgroundMusic.value = null

    // 如果当前已有选择，设置为临时选择
    if (selectedBackgroundMusic.value) {
      tempSelectedBackgroundMusic.value = { ...selectedBackgroundMusic.value }
    }

    backgroundMusicDialogVisible.value = true
  }

  // 确认背景音乐选择
  const confirmBackgroundMusicSelection = () => {
    if (tempSelectedBackgroundMusic.value) {
      formData.backgroundMusicId = tempSelectedBackgroundMusic.value.ID
      formData.backgroundMusicPath = [tempSelectedBackgroundMusic.value.ID]
      selectedBackgroundMusic.value = { ...tempSelectedBackgroundMusic.value }
      console.log('✅ 确认选择背景音乐:', selectedBackgroundMusic.value.name)
    }
    backgroundMusicDialogVisible.value = false
  }

  // 取消背景音乐选择
  const cancelBackgroundMusicSelection = () => {
    // 重置临时选择
    selectedCategoryPath.value = null
    currentCategoryMusic.value = []
    tempSelectedBackgroundMusic.value = null
    backgroundMusicDialogVisible.value = false
  }

  // 提交克隆任务
  const submitClone = async () => {
    try {
      await cloneForm.value.validate()

      // 音色选择校验已在表单规则中处理，这里不需要额外检查

      loading.value = true

      // 创建请求数据
      const requestData = {
        text: formData.text,
        model: formData.model,
        speed: formData.speed,
        volume: formData.volume,
        pitch: formData.pitch,
        audioSampleRate: formData.audioSampleRate,
        bitRate: formData.bitRate,
        format: formData.format,
        targetCategoryId: formData.targetCategoryId,
        // 背景音乐相关字段
        backgroundMusicId: formData.backgroundMusicId,
        backgroundMusicVolume: formData.backgroundMusicVolume,
        backgroundMusicLoop: formData.backgroundMusicLoop
      }

      // 根据音色类型设置不同的参数
      if (voiceType.value === 'official') {
        // 使用官方音色
        requestData.officialVoiceId = formData.officialVoiceId
        requestData.referenceAudioName = selectedOfficialVoice.value?.voice_name || '官方音色'
      } else {
        // 使用自定义音色
        requestData.referenceAudioId = formData.referenceAudioId
        requestData.referenceAudioName = selectedVoice.value.originalName || selectedVoice.value.name
      }

      console.log('准备发送的 MiniMax 请求数据:', requestData)

      const res = await createMinimaxVoiceCloneTask(requestData)
      console.log('MiniMax API响应:', res)
      if (res.code === 0) {
        ElMessage.success('MiniMax 克隆任务已提交，请稍候查看结果')
        await fetchTasks()
      } else {
        ElMessage.error('创建任务失败: ' + (res.msg || '未知错误'))
      }
    } catch (error) {
      console.error('提交失败:', error)
      ElMessage.error('提交失败: ' + (error.message || '未知错误'))
    } finally {
      loading.value = false
    }
  }

  // 重置表单
  const resetForm = () => {
    cloneForm.value?.resetFields()
    formData.referenceAudioPath = []
    formData.targetCategoryPath = []
    formData.backgroundMusicPath = []
    formData.officialVoiceId = null
    formData.backgroundMusicId = null
    formData.backgroundMusicLoop = true // 重置为默认值
    selectedVoice.value = null
    selectedOfficialVoice.value = null
    selectedBackgroundMusic.value = null
    voiceType.value = 'official'
    advancedOpen.value = []
  }

  // 获取任务列表
  const fetchTasks = async () => {
    try {
      const res = await getMinimaxVoiceCloneTaskList({
        page: pagination.page,
        pageSize: pagination.pageSize
      })
      if (res.code === 0) {
        tasks.value = res.data.list || []
        pagination.total = res.data.total || 0
      }
    } catch (error) {
      console.error('获取 MiniMax 任务列表失败:', error)
    }
  }

  // 刷新任务列表
  const refreshTasks = () => {
    fetchTasks()
  }

  // 检查任务状态
  const checkTaskStatus = async (taskId) => {
    try {
      const res = await getMinimaxVoiceCloneTaskStatus(taskId)
      if (res.code === 0) {
        await fetchTasks()
        if (res.data.status === 'SUCCEEDED') {
          ElMessage.success('任务已完成')
        } else if (res.data.status === 'FAILED') {
          ElMessage.error('任务失败: ' + (res.data.errorMsg || '未知错误'))
        }
      }
    } catch (error) {
      console.error('检查状态失败:', error)
    }
  }

  // 切换音频播放/暂停
  const toggleAudioPlay = (audioUrl, shouldLoop = false) => {
    const isPlaying = playingStates.value.get(audioUrl)

    if (isPlaying) {
      // 暂停播放
      const audio = playingAudios.value.get(audioUrl)
      if (audio) {
        audio.pause()
        playingStates.value.set(audioUrl, false)
      }
    } else {
      // 暂停其他正在播放的音频
      playingAudios.value.forEach((audio, url) => {
        if (url !== audioUrl) {
          audio.pause()
          playingStates.value.set(url, false)
          audioProgress.value.set(url, 0)
          audioCurrentTime.value.set(url, 0)
        }
      })

      // 开始播放
      let audio = playingAudios.value.get(audioUrl)
      if (!audio) {
        audio = new Audio(audioUrl)
        audio.loop = shouldLoop // 设置循环播放
        playingAudios.value.set(audioUrl, audio)

        // 监听元数据加载完成事件
        audio.addEventListener('loadedmetadata', () => {
          audioDuration.value.set(audioUrl, audio.duration)
          console.log(`音频元数据加载完成: ${audioUrl}, 时长: ${audio.duration}秒`)
        })

        // 监听播放进度更新事件
        audio.addEventListener('timeupdate', () => {
          const current = audio.currentTime
          const duration = audio.duration
          audioCurrentTime.value.set(audioUrl, current)
          if (duration > 0) {
            const progress = (current / duration) * 100
            audioProgress.value.set(audioUrl, progress)
          }
        })

        // 监听播放结束事件
        audio.addEventListener('ended', () => {
          if (!audio.loop) {
            playingStates.value.set(audioUrl, false)
            audioProgress.value.set(audioUrl, 0)
            audioCurrentTime.value.set(audioUrl, 0)
          }
        })

        // 监听播放错误事件
        audio.addEventListener('error', () => {
          console.error('播放失败:', audioUrl)
          ElMessage.error('播放失败')
          playingStates.value.set(audioUrl, false)
          audioProgress.value.set(audioUrl, 0)
          audioCurrentTime.value.set(audioUrl, 0)
        })
      } else {
        // 更新现有音频的循环设置
        audio.loop = shouldLoop
      }

      audio
        .play()
        .then(() => {
          playingStates.value.set(audioUrl, true)
        })
        .catch((error) => {
          console.error('播放失败:', error)
          ElMessage.error('播放失败')
          playingStates.value.set(audioUrl, false)
          audioProgress.value.set(audioUrl, 0)
          audioCurrentTime.value.set(audioUrl, 0)
        })
    }
  }

  // 下载音频
  const downloadAudio = async (task) => {
    try {
      await downloadFile(task.audioUrl, task.text.substring(0, 20) + '.mp3')
    } catch (error) {
      console.error('下载失败:', error)
      ElMessage.error('下载失败')
    }
  }

  // 获取状态类型
  const getStatusType = (status) => {
    const statusMap = {
      RUNNING: 'warning',
      SUCCEEDED: 'success',
      FAILED: 'danger',
      PENDING: 'info',
      SUSPENDED: 'warning',
      NOT_FOUND: 'danger',
      UNKNOWN: 'info'
    }
    return statusMap[status] || 'info'
  }

  // 获取状态文本
  const getStatusText = (status) => {
    const statusMap = {
      RUNNING: '处理中',
      SUCCEEDED: '已完成',
      FAILED: '失败',
      PENDING: '等待中',
      SUSPENDED: '挂起',
      NOT_FOUND: '未找到',
      UNKNOWN: '未知'
    }
    return statusMap[status] || '未知'
  }

  // 格式化时长（秒转换为分:秒格式）
  const formatDuration = (seconds) => {
    if (!seconds || seconds <= 0) return '--'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // 格式化播放时间（秒转换为分:秒格式，用于进度条）
  const formatTime = (seconds) => {
    if (!seconds || seconds <= 0) return '0:00'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // 处理进度条拖动变化（实时预览）
  const handleProgressChange = (audioUrl, percentage) => {
    const audio = playingAudios.value.get(audioUrl)
    if (!audio || !audio.duration) return

    const newTime = (percentage / 100) * audio.duration
    audioCurrentTime.value.set(audioUrl, newTime)
    audioProgress.value.set(audioUrl, percentage)
  }

  // 处理进度条拖动完成（设置播放位置）
  const handleProgressSet = (audioUrl, percentage) => {
    const audio = playingAudios.value.get(audioUrl)
    if (!audio || !audio.duration) return

    const newTime = (percentage / 100) * audio.duration
    audio.currentTime = newTime
    audioCurrentTime.value.set(audioUrl, newTime)
    audioProgress.value.set(audioUrl, percentage)
  }

  // 分页处理
  const handleSizeChange = (val) => {
    pagination.pageSize = val
    fetchTasks()
  }

  const handleCurrentChange = (val) => {
    pagination.page = val
    fetchTasks()
  }

  // 处理音乐类型选择
  const handleVoiceTypeChange = () => {
    // 重置相关表单项
    formData.referenceAudioPath = []
    formData.referenceAudioId = null
    formData.officialVoiceId = null
    selectedVoice.value = null
    selectedOfficialVoice.value = null

    // 清除表单校验错误
    cloneForm.value?.clearValidate(['referenceAudioId', 'officialVoiceId'])
  }

  // 处理官方音色选择
  const handleOfficialVoiceSelect = (value) => {
    formData.officialVoiceId = value
    formData.referenceAudioId = value // 保持兼容性
    selectedOfficialVoice.value = MINIMAX_VOICES.find((v) => v.uniq_id === value)

    // 清除校验错误
    cloneForm.value?.clearValidate(['officialVoiceId', 'referenceAudioId'])
  }

  onMounted(() => {
    initData()
  })
</script>

<style scoped>
  .minimax-voice-clone {
    width: 100%;
  }

  .clone-form-card {
    margin-bottom: 30px;
  }

  .text-input {
    width: 100%;
  }

  .voice-selection {
    width: 100%;
  }

  .voice-type-selection {
    margin-bottom: 15px;
  }

  .official-voice-selection,
  .custom-voice-selection {
    width: 100%;
  }

  .official-voice-option {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .official-voice-option .voice-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .official-voice-option .voice-details {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .selected-voice-preview {
    margin-top: 15px;
    padding: 15px;
    background: var(--el-fill-color-light);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .voice-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .voice-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .voice-category {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .voice-style {
    font-size: 12px;
    color: var(--el-color-info);
  }

  .voice-description {
    font-size: 11px;
    color: var(--el-text-color-regular);
    margin-top: 3px;
  }

  .voice-id {
    font-size: 11px;
    color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
    padding: 1px 6px;
    border-radius: 3px;
    font-family: monospace;
    margin-top: 2px;
  }

  .voice-preview-controls {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .voice-filter-section {
    padding: 15px;
    background: var(--el-fill-color-extra-light);
    border-radius: 6px;
    margin-bottom: 15px;
    border: 1px solid var(--el-border-color-lighter);
  }

  .voice-tags {
    margin: 8px 0;
  }

  .official-voice-selection .el-select {
    width: 100%;
  }

  /* 音色卡片样式 */
  .voice-cards-container {
    width: 100%;
  }

  .voice-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
  }

  .voice-card {
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    padding: 16px;
    background: var(--el-bg-color);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
  }

  .voice-card:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .voice-card.selected {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  }

  .voice-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
  }

  .voice-card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
    flex: 1;
    margin-right: 8px;
    line-height: 1.4;
  }

  .voice-card-actions {
    display: flex;
    gap: 6px;
    align-items: center;
  }

  .voice-card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 10px;
  }

  .voice-tag {
    font-size: 11px;
    padding: 2px 6px;
  }

  .voice-card-id {
    font-size: 11px;
    color: var(--el-text-color-secondary);
    font-family: monospace;
    background: var(--el-fill-color-extra-light);
    padding: 4px 8px;
    border-radius: 4px;
    word-break: break-all;
  }

  .voice-pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .voice-pagination :deep(.el-pagination) {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .voice-pagination :deep(.el-pagination__sizes) {
    display: inline-flex !important;
    align-items: center;
    margin-right: 8px;
  }

  .voice-pagination :deep(.el-pagination__sizes .el-select) {
    width: auto;
    min-width: 90px;
  }

  .voice-pagination :deep(.el-pagination__sizes .el-select .el-select__wrapper) {
    min-width: 90px;
  }

  .voice-pagination :deep(.el-pagination__sizes .el-select .el-select__selection) {
    overflow: visible;
  }

  .voice-pagination :deep(.el-pagination__sizes .el-select .el-select__selected-item) {
    white-space: nowrap;
    overflow: visible;
  }

  .voice-pagination :deep(.el-pagination__sizes .el-select .el-select__input) {
    width: 100%;
  }

  /* 确保分页器下拉选择器的文本完整显示 */
  .voice-pagination :deep(.el-pagination__sizes .el-select) {
    height: 32px;
    line-height: 32px;
  }

  .voice-pagination :deep(.el-pagination__sizes .el-select .el-select__wrapper) {
    height: 32px;
    line-height: 32px;
  }

  .selected-voice-info {
    margin-top: 20px;
  }

  .selected-voice-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .selected-voice-details .voice-id {
    font-family: monospace;
    font-size: 12px;
  }

  .selected-voice-details .voice-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .voice-cards-grid {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 12px;
    }

    .voice-card {
      padding: 12px;
    }

    .voice-card-title {
      font-size: 14px;
    }
  }

  @media (max-width: 480px) {
    .voice-cards-grid {
      grid-template-columns: 1fr;
      gap: 10px;
    }
  }

  .task-list-card {
    margin-top: 30px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .text-preview {
    line-height: 1.5;
    color: var(--el-text-color-primary);
  }

  .voice-id {
    font-family: monospace;
    font-size: 12px;
    color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
    padding: 2px 6px;
    border-radius: 4px;
  }

  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  .pagination-wrapper :deep(.el-pagination) {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .pagination-wrapper :deep(.el-pagination__sizes) {
    display: inline-flex !important;
    align-items: center;
    margin-right: 8px;
  }

  .pagination-wrapper :deep(.el-pagination__sizes .el-select) {
    width: auto;
    min-width: 90px;
  }

  .pagination-wrapper :deep(.el-pagination__sizes .el-select .el-select__wrapper) {
    min-width: 90px;
  }

  .pagination-wrapper :deep(.el-pagination__sizes .el-select .el-select__selection) {
    overflow: visible;
  }

  .pagination-wrapper :deep(.el-pagination__sizes .el-select .el-select__selected-item) {
    white-space: nowrap;
    overflow: visible;
  }

  .pagination-wrapper :deep(.el-pagination__sizes .el-select .el-select__input) {
    width: 100%;
  }

  /* 确保任务列表分页器下拉选择器的文本完整显示 */
  .pagination-wrapper :deep(.el-pagination__sizes .el-select) {
    height: 32px;
    line-height: 32px;
  }

  .pagination-wrapper :deep(.el-pagination__sizes .el-select .el-select__wrapper) {
    height: 32px;
    line-height: 32px;
  }

  .advanced-settings-wrapper {
    width: 100%;
  }

  .advanced-settings-content {
    margin: 0;
    padding: 0;
  }

  .slider-container {
    display: flex;
    align-items: center;
    gap: 15px;
    width: 100%;
  }

  .custom-slider {
    flex: 1;
    margin: 0;
  }

  .slider-value {
    min-width: 40px;
    text-align: center;
    font-size: 12px;
    color: var(--el-text-color-regular);
    background: var(--el-fill-color-light);
    padding: 2px 8px;
    border-radius: 4px;
  }

  :deep(.el-collapse-item__header) {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    padding: 0 20px;
    height: 48px;
    line-height: 48px;
  }

  :deep(.el-form-item) {
    margin-bottom: 18px;
  }

  :deep(.el-collapse-item__content) {
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 0;
  }

  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-input-number) {
    width: 100%;
  }

  :deep(.el-collapse) {
    border: none;
  }

  :deep(.el-collapse-item) {
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
  }

  :deep(.el-collapse-item__header) {
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-extra-light);
    padding: 0 16px;
    border-radius: 6px 6px 0 0;
  }

  :deep(.el-collapse-item__content) {
    padding: 16px;
    border-radius: 0 0 6px 6px;
  }

  :deep(.el-slider__runway) {
    height: 6px;
  }

  :deep(.el-slider__button) {
    width: 16px;
    height: 16px;
  }

  /* 优化Cascader选项显示 */
  :deep(.el-cascader-menu__item) {
    line-height: 1.6;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 为包含MiniMax ID的选项添加特殊样式 */
  :deep(.el-cascader-menu__item[title*='音色ID:']) {
    background: linear-gradient(90deg, transparent 0%, rgba(103, 194, 58, 0.08) 100%);
    border-left: 3px solid var(--el-color-success-light-5);
    position: relative;
  }

  :deep(.el-cascader-menu__item[title*='音色ID:']:before) {
    content: '✓';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--el-color-success);
    font-weight: bold;
    font-size: 12px;
  }

  /* 背景音乐选择样式 */
  .background-music-selection {
    width: 100%;
  }

  .music-selector {
    width: 100%;
    margin-bottom: 15px;
  }

  .selected-music-preview {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    background: var(--el-fill-color-light);
    border-radius: 8px;
    margin-bottom: 15px;
    border: 1px solid var(--el-border-color-lighter);
  }

  .selected-music-preview .music-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
  }

  .selected-music-preview .music-icon {
    font-size: 24px;
    color: var(--el-color-primary);
  }

  .selected-music-preview .music-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .selected-music-preview .music-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
    font-size: 14px;
  }

  .selected-music-preview .music-category {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .selected-music-preview .music-duration {
    font-size: 12px;
    color: var(--el-text-color-regular);
  }

  .selected-music-preview .music-controls {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .music-volume-control {
    margin-bottom: 15px;
  }

  .volume-label {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
    color: var(--el-text-color-primary);
  }

  .music-loop-control {
    margin-bottom: 15px;
    padding: 10px 0;
  }

  .music-loop-control :deep(.el-checkbox) {
    height: auto;
    line-height: 1.5;
  }

  .music-loop-control :deep(.el-checkbox__label) {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: var(--el-text-color-primary);
  }

  .add-music-btn {
    display: flex;
    justify-content: center;
    padding: 20px;
    border: 2px dashed var(--el-border-color);
    border-radius: 8px;
    background: var(--el-fill-color-extra-light);
    transition: all 0.3s;
  }

  .add-music-btn:hover {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
  }

  /* 背景音乐对话框样式 */
  .background-music-dialog {
    padding: 10px 0;
  }

  .music-selection-layout {
    display: flex;
    gap: 20px;
    height: 400px;
  }

  .category-selection-panel {
    flex: 0 0 300px;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    overflow: hidden;
  }

  .music-list-panel {
    flex: 1;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: var(--el-fill-color-extra-light);
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .panel-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .music-count {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .category-tree {
    padding: 16px;
  }

  .music-list {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .loading-placeholder,
  .empty-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-secondary);
    gap: 8px;
  }

  .loading-placeholder .el-icon {
    font-size: 24px;
    color: var(--el-color-primary);
  }

  .empty-placeholder .el-icon {
    font-size: 48px;
    color: var(--el-text-color-placeholder);
  }

  .music-items {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  }

  .music-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--el-bg-color);
  }

  .music-item:hover {
    border-color: var(--el-color-primary-light-7);
    background: var(--el-color-primary-light-9);
  }

  .music-item.selected {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary-light-8);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  }

  .music-item:last-child {
    margin-bottom: 0;
  }

  .music-item-info {
    flex: 1;
    min-width: 0;
  }

  .music-item-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .music-item-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .music-duration {
    background: var(--el-fill-color-light);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: monospace;
  }

  .music-item-actions {
    flex: 0 0 auto;
    margin-left: 12px;
  }

  .temp-music-preview {
    margin-top: 20px;
    border-top: 1px solid var(--el-border-color-lighter);
    padding-top: 20px;
  }

  .music-preview-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    background: var(--el-fill-color-light);
    border-radius: 8px;
    border: 1px solid var(--el-border-color-lighter);
  }

  .music-preview-card .music-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
  }

  .music-preview-card .music-icon {
    font-size: 24px;
    color: var(--el-color-primary);
  }

  .music-preview-card .music-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .music-preview-card .music-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
    font-size: 14px;
  }

  .music-preview-card .music-category {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .music-preview-card .music-duration {
    font-size: 12px;
    color: var(--el-text-color-regular);
  }

  .music-preview-card .music-controls {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  /* 播放进度条样式 */
  .music-progress,
  .voice-progress,
  .voice-card-progress,
  .task-progress {
    margin-top: 8px;
  }

  .music-duration-progress {
    margin-top: 4px;
  }

  .progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    font-size: 11px;
    color: var(--el-text-color-secondary);
  }

  .current-time,
  .duration {
    font-family: monospace;
    background: var(--el-fill-color-extra-light);
    padding: 1px 4px;
    border-radius: 3px;
  }

  .audio-progress-bar {
    margin: 0;
  }

  .audio-progress-bar :deep(.el-progress-bar__outer) {
    height: 4px;
    background-color: var(--el-fill-color-light);
  }

  .audio-progress-bar :deep(.el-progress-bar__inner) {
    background: linear-gradient(90deg, var(--el-color-primary-light-3), var(--el-color-primary));
  }

  /* 拖动进度条样式 */
  .audio-progress-slider {
    margin: 0;
  }

  .audio-progress-slider :deep(.el-slider__runway) {
    height: 4px;
    background-color: var(--el-fill-color-light);
    border-radius: 2px;
  }

  .audio-progress-slider :deep(.el-slider__bar) {
    background: linear-gradient(90deg, var(--el-color-primary-light-3), var(--el-color-primary));
    border-radius: 2px;
  }

  .audio-progress-slider :deep(.el-slider__button) {
    width: 12px;
    height: 12px;
    border: 2px solid var(--el-color-primary);
    background: white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }

  .audio-progress-slider :deep(.el-slider__button):hover {
    transform: scale(1.2);
  }

  .music-duration {
    font-size: 12px;
    color: var(--el-text-color-regular);
  }

  .small-progress-bar {
    margin: 4px 0;
  }

  .small-progress-bar :deep(.el-progress-bar__outer) {
    height: 2px;
  }

  .music-item-progress {
    margin-top: 8px;
    padding: 0 16px;
  }

  .voice-card-progress {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid var(--el-border-color-extra-light);
  }

  .task-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .task-progress {
    margin-top: 4px;
  }

  /* 循环播放图标效果 */
  .music-controls .el-button[title*='循环'],
  .music-item-actions .el-button[title*='循环'] {
    position: relative;
  }

  .music-item-actions .el-button[title*='循环']:after {
    content: '🔄';
    position: absolute;
    top: -3px;
    right: -3px;
    font-size: 6px;
    background: var(--el-color-success);
    color: white;
    border-radius: 50%;
    width: 12px;
    height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
</style>
