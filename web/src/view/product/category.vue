<template>
  <div class="product-category-container">
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="分类名称">
          <el-input v-model="searchInfo.name" placeholder="请输入分类名称" style="width: 200px" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchInfo.status" placeholder="请选择状态" style="width: 120px" clearable>
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getTableData">查询</el-button>
          <el-button icon="Refresh" @click="resetSearch">重置</el-button>
          <el-button type="primary" @click="handleAdd">新增分类</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="drag-table-container">
      <el-table :data="tableData" border stripe style="width: 100%" v-loading="loading" row-key="id" ref="tableRef">
        <el-table-column label="拖拽" width="80" align="center">
          <template #default>
            <el-icon class="drag-handle" style="cursor: move; color: #409eff">
              <Rank />
            </el-icon>
          </template>
        </el-table-column>

        <el-table-column label="分类名称" prop="name" min-width="100" />

        <el-table-column label="排序值" prop="sortOrder" align="center" width="100">
          <template #default="scope">
            <el-tag>{{ scope.row.sortOrder }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="商品数" prop="productCount" align="center" width="100">
          <template #default="scope">
            <el-button type="primary" link class="product-count-link" @click="handleProductCountClick(scope.row)">
              {{ scope.row.productCount }}
            </el-button>
          </template>
        </el-table-column>

        <el-table-column label="创建人" prop="creatorName" align="center" width="120" />

        <el-table-column label="状态" align="center" width="120">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="2"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>

        <el-table-column label="创建时间" align="center" width="180">
          <template #default="scope">
            <div>{{ formatDateTime(scope.row.createdAt) }}</div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)" :disabled="scope.row.productCount > 0">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="gva-pagination">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100]"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 新增/编辑分类对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增分类' : '编辑分类'"
      width="40%"
      :close-on-click-modal="false"
    >
      <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, onMounted, nextTick } from 'vue'
  import { Rank } from '@element-plus/icons-vue'
  import { useRouter } from 'vue-router'
  import {
    createProductCategory,
    updateProductCategory,
    deleteProductCategory,
    getProductCategoryList,
    updateCategoryStatus,
    updateCategorySort
  } from '@/api/douyin/productCategory'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import Sortable from 'sortablejs'

  const router = useRouter()
  const loading = ref(false)
  const page = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const tableData = ref([])
  const tableRef = ref()
  let sortableInstance = null

  const searchInfo = ref({
    name: '',
    status: null
  })

  // 对话框相关
  const dialogVisible = ref(false)
  const dialogType = ref('add') // 'add' 或 'edit'
  const formRef = ref()
  const formData = ref({
    name: '',
    status: 1
  })

  // 表单验证规则
  const rules = {
    name: [
      { required: true, message: '请输入分类名称', trigger: 'blur' },
      { min: 1, max: 50, message: '分类名称长度在1到50个字符', trigger: 'blur' }
    ],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }]
  }

  // 初始化拖拽排序
  const initSortable = () => {
    if (sortableInstance) {
      sortableInstance.destroy()
    }

    nextTick(() => {
      const tbody = tableRef.value?.$el.querySelector('tbody')
      if (!tbody) return

      sortableInstance = Sortable.create(tbody, {
        handle: '.drag-handle',
        animation: 150,
        onEnd: async (evt) => {
          const { oldIndex, newIndex } = evt
          if (oldIndex === newIndex) return

          // 更新本地数据顺序
          const movedItem = tableData.value.splice(oldIndex, 1)[0]
          tableData.value.splice(newIndex, 0, movedItem)

          // 重新计算排序值并提交到后端
          const categories = tableData.value.map((item, index) => ({
            id: item.ID,
            sortOrder: index
          }))

          try {
            await updateCategorySort({ categories })
            ElMessage.success('排序更新成功')
            // 刷新数据以获取最新排序值
            getTableData()
          } catch (err) {
            console.error('排序更新失败:', err)
            ElMessage.error('排序更新失败')
            // 恢复原序
            getTableData()
          }
        }
      })
    })
  }

  // 获取列表数据
  const getTableData = async () => {
    loading.value = true
    try {
      const params = {
        page: page.value,
        pageSize: pageSize.value,
        name: searchInfo.value.name,
        status: searchInfo.value.status
      }

      const res = await getProductCategoryList(params)
      if (res.code === 0) {
        tableData.value = res.data.list || []
        total.value = res.data.total || 0

        // 重新初始化拖拽功能
        setTimeout(() => {
          initSortable()
        }, 100)
      }
    } catch (err) {
      console.error('获取分类列表失败:', err)
      ElMessage.error('获取分类列表失败')
    } finally {
      loading.value = false
    }
  }

  // 格式化日期时间
  const formatDateTime = (dateTimeStr) => {
    if (!dateTimeStr) return ''
    try {
      const date = new Date(dateTimeStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(
        2,
        '0'
      )} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    } catch (e) {
      console.error('格式化日期时间失败:', e)
      return dateTimeStr
    }
  }

  // 重置搜索条件
  const resetSearch = () => {
    searchInfo.value = {
      name: '',
      status: null
    }
    page.value = 1
    getTableData()
  }

  // 分页相关方法
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }

  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }

  // 新增分类
  const handleAdd = () => {
    dialogType.value = 'add'
    formData.value = {
      name: '',
      status: 1
    }
    dialogVisible.value = true
  }

  // 编辑分类
  const handleEdit = (row) => {
    dialogType.value = 'edit'
    formData.value = {
      id: row.ID,
      name: row.name,
      status: row.status
    }
    dialogVisible.value = true
  }

  // 删除分类
  const handleDelete = async (row) => {
    if (row.productCount > 0) {
      ElMessage.warning('该分类下有商品，无法删除')
      return
    }

    try {
      await ElMessageBox.confirm(`确定要删除分类 "${row.name}" 吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await deleteProductCategory(row.ID)
      ElMessage.success('删除成功')
      getTableData()
    } catch (err) {
      if (err !== 'cancel') {
        console.error('删除失败:', err)
        ElMessage.error('删除失败')
      }
    }
  }

  // 状态切换
  const handleStatusChange = async (row) => {
    try {
      await updateCategoryStatus({
        id: row.ID,
        status: row.status
      })
      ElMessage.success('状态更新成功')
      getTableData()
    } catch (err) {
      console.error('状态更新失败:', err)
      ElMessage.error('状态更新失败')
      // 恢复原状态
      row.status = row.status === 1 ? 2 : 1
    }
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()

      if (dialogType.value === 'add') {
        await createProductCategory(formData.value)
        ElMessage.success('创建成功')
      } else {
        await updateProductCategory(formData.value.id, formData.value)
        ElMessage.success('更新成功')
      }

      dialogVisible.value = false
      getTableData()
    } catch (err) {
      if (err !== false) {
        // 表单验证失败时err为false
        console.error(dialogType.value === 'add' ? '创建失败:' : '更新失败:', err)
        ElMessage.error(dialogType.value === 'add' ? '创建失败' : '更新失败')
      }
    }
  }

  // 点击商品数跳转到商品列表页面
  const handleProductCountClick = (row) => {
    console.log(row)
    // 跳转到商品列表页面，并传递分类ID
    router.push({
      name: 'listNew',
      query: {
        categoryId: row.id
      }
    })
  }

  onMounted(() => {
    getTableData()
  })
</script>

<style lang="scss" scoped>
  .product-category-container {
    padding: 18px;

    .gva-search-box {
      margin-bottom: 16px;
    }

    .drag-table-container {
      .drag-handle {
        cursor: move;

        &:hover {
          color: #1890ff !important;
        }
      }

      :deep(.el-table__row) {
        &.sortable-ghost {
          opacity: 0.8;
          background: #f0f9ff;
        }
      }
    }

    .gva-pagination {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
    }

    .product-count-link {
      font-weight: 600;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.1);
        text-shadow: 0 1px 2px rgba(64, 158, 255, 0.3);
      }
    }
  }
</style>
