<template>
  <div class="digital-human-container">
    <!-- 工作流选择Tab -->
    <el-card class="workflow-tabs-card">
      <el-tabs v-model="activeWorkflow" @tab-change="handleWorkflowChange">
        <el-tab-pane label="图生数字人" name="standard">
          <div class="tab-description">
            <p>支持完整的数字人定制功能，包括动作描述、高级参数调节等</p>
          </div>
        </el-tab-pane>
        <el-tab-pane label="视频数字人" name="heygem">
          <div class="tab-description">
            <p>基于HeyGem+IndexTTS快速语音迁移V3的数字人生成</p>
            <div class="heygem-features">
              <el-tag type="success" size="small">✨ 极速生成</el-tag>
              <el-tag type="primary" size="small">🎯 操作简单</el-tag>
              <el-tag type="warning" size="small">🎵 音频驱动</el-tag>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 标准数字人表单 -->
    <el-card v-if="activeWorkflow === 'standard'" class="main-form-card">
      <el-form ref="digitalHumanForm" :model="formData" :rules="rules" label-width="140px" size="large">
        <!-- 参考音频选择 -->
        <el-form-item label="参考音色" prop="referenceAudioId">
          <div class="audio-selection-wrapper">
            <!-- 音色分类选择 -->
            <el-cascader
              v-model="formData.referenceAudioPath"
              :options="voiceOptions"
              :props="cascaderProps"
              placeholder="请先选择音色分类"
              style="width: 100%"
              filterable
              @change="handleVoiceSelect"
            />

            <!-- 搜索框 -->
            <div class="voice-search-wrapper">
              <el-input
                v-model="voiceSearchKeyword"
                placeholder="搜索该分类下的音色..."
                @input="debounceVoiceSearch"
                clearable
                :disabled="!selectedVoiceCategory"
              >
                <template #append>
                  <el-button @click="fetchVoiceFromLibrary" :disabled="!selectedVoiceCategory">
                    <el-icon><Search /></el-icon>
                  </el-button>
                </template>
              </el-input>
            </div>

            <!-- 音色列表 -->
            <div class="voice-list" v-loading="loadingVoice">
              <div v-if="!voiceList.length" class="empty-voice">
                <el-empty
                  :description="!selectedVoiceCategory && !voiceSearchKeyword ? '请先选择音色分类' : '暂无音色数据'"
                  :image-size="100"
                />
              </div>
              <div v-else class="voice-grid">
                <div
                  v-for="voice in voiceList"
                  :key="voice.ID"
                  :class="['voice-item', { selected: selectedVoice?.ID === voice.ID }]"
                  @click="selectVoice(voice)"
                >
                  <div class="voice-info">
                    <div class="voice-main-row">
                      <div class="voice-name">{{ voice.name }}</div>
                      <div class="voice-meta">
                        <span class="voice-category">{{ voice.categoryName || '未分类' }}</span>
                        <span class="voice-duration">{{ formatDuration(voice.duration) }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="voice-controls">
                    <el-button
                      :type="playingStates.get(voice.fileUrl) ? 'warning' : 'primary'"
                      size="small"
                      @click.stop="toggleAudioPlay(voice.fileUrl)"
                    >
                      <el-icon v-if="playingStates.get(voice.fileUrl)"><VideoPause /></el-icon>
                      <el-icon v-else><VideoPlay /></el-icon>
                    </el-button>
                  </div>
                  <div v-if="selectedVoice?.ID === voice.ID" class="selected-indicator">
                    <el-icon><Check /></el-icon>
                  </div>
                </div>
              </div>

              <!-- 分页 -->
              <div v-if="voiceList.length" class="voice-pagination">
                <el-pagination
                  v-model:current-page="voicePagination.page"
                  v-model:page-size="voicePagination.pageSize"
                  :page-sizes="[10, 20, 50]"
                  :total="voicePagination.total"
                  layout="total, sizes, prev, pager, next"
                  @size-change="handleVoiceSizeChange"
                  @current-change="handleVoiceCurrentChange"
                />
              </div>
            </div>

            <!-- 选中的音色预览和编辑区 -->
            <div v-if="selectedVoice" class="selected-voice-preview">
              <div class="preview-header">
                <span class="preview-title">已选择音色：{{ selectedVoice.name }}</span>
                <el-button size="small" type="primary" @click="clearSelectedVoice">
                  <el-icon><Close /></el-icon>
                  清除选择
                </el-button>
              </div>
              <div class="voice-preview-controls">
                <el-button
                  :type="playingStates.get(selectedVoice.fileUrl) ? 'warning' : 'success'"
                  size="small"
                  @click="toggleAudioPlay(selectedVoice.fileUrl)"
                >
                  <el-icon v-if="playingStates.get(selectedVoice.fileUrl)"><VideoPause /></el-icon>
                  <el-icon v-else><VideoPlay /></el-icon>
                  {{ playingStates.get(selectedVoice.fileUrl) ? '暂停' : '播放' }}
                </el-button>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 台词内容 -->
        <el-form-item label="台词内容" prop="script">
          <div class="script-wrapper">
            <el-radio-group v-model="scriptSource" class="script-source-selector">
              <el-radio-button label="manual">手动输入</el-radio-button>
              <el-radio-button label="library">从文案库选择</el-radio-button>
            </el-radio-group>

            <!-- 手动输入 -->
            <div v-if="scriptSource === 'manual'" class="manual-script">
              <el-input
                v-model="formData.script"
                type="textarea"
                :rows="4"
                placeholder="请输入数字人的台词内容..."
                maxlength="500"
                show-word-limit
                class="script-input"
              />
            </div>

            <!-- 从文案库选择 -->
            <div v-if="scriptSource === 'library'" class="library-script">
              <!-- 文案分类选择 -->
              <el-cascader
                v-model="selectedCopywritingPath"
                :options="copywritingOptions"
                :props="copywritingCascaderProps"
                placeholder="请先选择文案分类"
                style="width: 100%"
                filterable
                @change="handleCopywritingSelect"
              />

              <!-- 搜索框 -->
              <div class="copywriting-search-wrapper">
                <el-input
                  v-model="copywritingSearchKeyword"
                  placeholder="搜索该分类下的文案..."
                  @input="debounceCopywritingSearch"
                  clearable
                  :disabled="!selectedCopywritingCategory"
                >
                  <template #append>
                    <el-button @click="fetchCopywritingFromLibrary" :disabled="!selectedCopywritingCategory">
                      <el-icon><Search /></el-icon>
                    </el-button>
                  </template>
                </el-input>
              </div>

              <!-- 文案列表 -->
              <div class="copywriting-list" v-loading="loadingCopywriting">
                <div v-if="!copywritingList.length" class="empty-copywriting">
                  <el-empty
                    :description="
                      !selectedCopywritingCategory && !copywritingSearchKeyword ? '请先选择文案分类' : '暂无文案数据'
                    "
                    :image-size="100"
                  />
                </div>
                <div v-else class="copywriting-grid">
                  <div
                    v-for="copywriting in copywritingList"
                    :key="copywriting.ID"
                    :class="['copywriting-item', { selected: selectedCopywriting?.ID === copywriting.ID }]"
                    @click="selectCopywriting(copywriting)"
                  >
                    <div class="copywriting-info">
                      <div class="copywriting-content">
                        {{ copywriting.content?.substring(0, 80) }}{{ copywriting.content?.length > 80 ? '...' : '' }}
                      </div>
                    </div>
                    <div v-if="selectedCopywriting?.ID === copywriting.ID" class="selected-indicator">
                      <el-icon><Check /></el-icon>
                    </div>
                  </div>
                </div>

                <!-- 分页 -->
                <div v-if="copywritingList.length" class="copywriting-pagination">
                  <el-pagination
                    v-model:current-page="copywritingPagination.page"
                    v-model:page-size="copywritingPagination.pageSize"
                    :page-sizes="[10, 20, 50]"
                    :total="copywritingPagination.total"
                    layout="total, sizes, prev, pager, next"
                    @size-change="handleCopywritingSizeChange"
                    @current-change="handleCopywritingCurrentChange"
                  />
                </div>
              </div>

              <!-- 选中的文案预览和编辑区 -->
              <div v-if="selectedCopywriting" class="selected-copywriting-preview">
                <div class="preview-header">
                  <span class="preview-title">已选择文案：{{ selectedCopywriting.title || '无标题' }}</span>
                  <el-button size="small" type="primary" @click="clearSelectedCopywriting">
                    <el-icon><Close /></el-icon>
                    清除选择
                  </el-button>
                </div>
                <el-input
                  v-model="formData.script"
                  type="textarea"
                  :rows="3"
                  placeholder="选择的文案内容将显示在这里，您可以进一步编辑..."
                  class="script-input"
                />
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 人物图片选择 -->
        <el-form-item label="人物图片" prop="characterImage">
          <div class="image-selection-wrapper">
            <el-cascader
              v-model="selectedImagePath"
              :options="imageOptions"
              :props="imageCascaderProps"
              placeholder="请选择图片分类"
              style="width: 100%"
              filterable
              @change="handleImageCategorySelect"
            />

            <!-- 搜索框 -->
            <div class="image-search-wrapper">
              <el-input v-model="imageSearchKeyword" placeholder="搜索图片..." @input="debounceImageSearch">
                <template #append>
                  <el-button @click="fetchImagesFromLibrary">
                    <el-icon><Search /></el-icon>
                  </el-button>
                </template>
              </el-input>
            </div>

            <!-- 图片展示区 -->
            <div class="image-gallery" v-loading="loadingImages">
              <div v-if="!fetchedImages.length" class="empty-gallery">
                <el-empty
                  :description="!selectedImageCategory && !imageSearchKeyword ? '请先选择图片分类' : '暂无图片数据'"
                  :image-size="100"
                />
              </div>
              <div v-else class="image-grid">
                <div
                  v-for="image in fetchedImages"
                  :key="image.ID"
                  :class="['image-item', { selected: formData.characterImage?.ID === image.ID }]"
                  @click="selectImage(image)"
                >
                  <div class="image-wrapper">
                    <img :src="getImageUrl(image.fileUrl)" :alt="image.name" @error="handleImageError" />
                    <div class="image-overlay">
                      <div class="image-name">{{ image.name }}</div>
                      <div class="image-dimensions">{{ image.width }}×{{ image.height }}</div>
                    </div>
                    <div v-if="formData.characterImage?.ID === image.ID" class="selected-indicator">
                      <el-icon><Check /></el-icon>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 分页 -->
              <div v-if="fetchedImages.length" class="image-pagination">
                <el-pagination
                  v-model:current-page="imagePagination.page"
                  v-model:page-size="imagePagination.pageSize"
                  :page-sizes="[12, 24, 48]"
                  :total="imagePagination.total"
                  layout="total, sizes, prev, pager, next"
                  @size-change="handleImageSizeChange"
                  @current-change="handleImageCurrentChange"
                />
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 人物动作 -->
        <el-form-item label="人物动作" prop="action">
          <el-input
            v-model="formData.action"
            placeholder="请描述数字人的动作，例如：美女在说话、商务人士介绍产品、老师在讲课等..."
            maxlength="100"
            show-word-limit
          />
          <div class="action-tips">
            <el-alert title="动作提示" type="info" :closable="false" show-icon>
              <template #default>
                <div class="tips-content">
                  <p>动作描述要简洁明确，例如："美女在说话"、"商务人士微笑"；可以描述表情、手势、姿态等细节</p>
                </div>
              </template>
            </el-alert>
          </div>
        </el-form-item>

        <!-- 工作流选择 -->
        <el-form-item label="工作流">
          <el-radio-group v-model="formData.workflowType">
            <el-radio :label="1">HeyGem＋index-tts＋图片＋wan视频模型</el-radio>
            <el-radio :label="2">Heygem+FusionX+IndexTTS数字人</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 视频分类选择 -->
        <el-form-item label="视频分类">
          <el-cascader
            v-model="selectedCategories"
            :options="categoryOptions"
            :props="{
              checkStrictly: true,
              multiple: false,
              emitPath: false,
              value: 'value',
              label: 'label',
              children: 'children',
              expandTrigger: 'click'
            }"
            placeholder="请选择视频分类"
            clearable
            style="width: 100%"
          />
        </el-form-item>

        <!-- 高级设置 -->
        <el-form-item label="">
          <div class="advanced-settings-wrapper">
            <el-collapse v-model="advancedOpen">
              <el-collapse-item title="高级设置" name="advanced">
                <div class="advanced-settings-content">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="视频尺寸" label-width="100px">
                        <el-select v-model="formData.videoSize" placeholder="选择视频尺寸" style="width: 100%">
                          <el-option label="方形 (832×832)" value="832x832" />
                          <el-option label="竖屏 (720×1280)" value="720x1280" />
                          <el-option label="横屏 (1280×720)" value="1280x720" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="帧率" label-width="100px">
                        <el-input-number v-model="formData.fps" :min="8" :max="30" style="width: 100%" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="submitTask" size="large">
            <el-icon><VideoCamera /></el-icon>
            开始生成数字人视频
          </el-button>
          <el-button @click="resetForm" size="large">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- HeyGem极速生成表单 -->
    <el-card v-if="activeWorkflow === 'heygem'" class="main-form-card">
      <!-- 使用说明 -->
      <div class="heygem-instructions">
        <el-alert title="使用指南 - 两种工作模式：" type="info" :closable="false" show-icon>
          <template #default>
            <div class="instruction-content">
              <ul>
                <li><strong>音频+视频模式：</strong>直接使用完整音频文件与人物视频合成，适合已有录音的场景</li>
                <li>
                  <strong>参考音频+台词+视频模式：</strong
                  >通过参考音频克隆声音，根据台词生成语音，适合需要定制内容的场景
                </li>
              </ul>
            </div>
          </template>
        </el-alert>
      </div>

      <el-form ref="heygemForm" :model="heygemFormData" :rules="heygemRules" label-width="140px" size="large">
        <!-- 模式选择 -->
        <el-form-item label="工作模式" prop="mode">
          <el-radio-group v-model="heygemFormData.mode" @change="handleHeygemModeChange">
            <el-radio-button :label="1">
              <el-icon><VideoPlay /></el-icon>
              音频+视频
            </el-radio-button>
            <el-radio-button :label="2">
              <el-icon><Microphone /></el-icon>
              参考音频+台词+视频
            </el-radio-button>
          </el-radio-group>
          <div class="mode-description">
            <p v-if="heygemFormData.mode === 1" class="mode-tip">
              🎵 <strong>音频+视频模式：</strong>直接使用完整的音频文件与人物视频合成数字人视频
            </p>
            <p v-if="heygemFormData.mode === 2" class="mode-tip">
              🎤 <strong>参考音频+台词+视频模式：</strong>使用参考音频进行声音克隆，根据输入的台词生成数字人视频
            </p>
          </div>
        </el-form-item>

        <!-- 模式1：直接音频选择 -->
        <el-form-item v-if="heygemFormData.mode === 1" label="音频文件" prop="directAudioId">
          <div class="audio-selection-wrapper">
            <!-- 音频分类选择 -->
            <el-cascader
              v-model="heygemFormData.directAudioPath"
              :options="heygemAudioCategoryOptions"
              :props="heygemAudioCascaderProps"
              placeholder="请先选择音频分类"
              style="width: 100%"
              filterable
              @change="handleHeygemDirectAudioCategorySelect"
            />

            <!-- 搜索框 -->
            <div class="audio-search-wrapper">
              <el-input
                v-model="heygemAudioSearchKeyword"
                placeholder="搜索该分类下的音频文件..."
                @input="debounceHeygemAudioSearch"
                clearable
                :disabled="!heygemSelectedAudioCategory"
              >
                <template #append>
                  <el-button @click="fetchHeygemAudioList" :disabled="!heygemSelectedAudioCategory">
                    <el-icon><Search /></el-icon>
                  </el-button>
                </template>
              </el-input>
            </div>

            <!-- 音频列表 -->
            <div class="audio-list" v-loading="loadingHeygemAudio">
              <div v-if="!heygemAudioList.length" class="empty-audio">
                <el-empty
                  :description="
                    !heygemSelectedAudioCategory && !heygemAudioSearchKeyword ? '请先选择音频分类' : '暂无音频数据'
                  "
                  :image-size="100"
                />
              </div>
              <div v-else class="audio-grid">
                <div
                  v-for="audio in heygemAudioList"
                  :key="audio.ID"
                  :class="['audio-item', { selected: selectedHeygemDirectAudio?.ID === audio.ID }]"
                  @click="selectHeygemDirectAudio(audio)"
                >
                  <div class="audio-info">
                    <div class="audio-main-row">
                      <div class="audio-name">{{ audio.name }}</div>
                      <div class="audio-meta">
                        <span class="audio-category">{{ audio.categoryName || '未分类' }}</span>
                        <span class="audio-duration">{{ formatDuration(audio.duration) }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="audio-controls">
                    <el-button
                      :type="playingStates.get(audio.fileUrl) ? 'warning' : 'primary'"
                      size="small"
                      @click.stop="toggleAudioPlay(audio.fileUrl)"
                    >
                      <el-icon v-if="playingStates.get(audio.fileUrl)"><VideoPause /></el-icon>
                      <el-icon v-else><VideoPlay /></el-icon>
                    </el-button>
                  </div>
                  <div v-if="selectedHeygemDirectAudio?.ID === audio.ID" class="selected-indicator">
                    <el-icon><Check /></el-icon>
                  </div>
                </div>
              </div>

              <!-- 分页 -->
              <div v-if="heygemAudioList.length" class="audio-pagination">
                <el-pagination
                  v-model:current-page="heygemAudioPagination.page"
                  v-model:page-size="heygemAudioPagination.pageSize"
                  :page-sizes="[10, 20, 50]"
                  :total="heygemAudioPagination.total"
                  layout="total, sizes, prev, pager, next"
                  @size-change="handleHeygemAudioSizeChange"
                  @current-change="handleHeygemAudioCurrentChange"
                />
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 模式2：参考音频选择 -->
        <el-form-item v-if="heygemFormData.mode === 2" label="参考音色" prop="referenceAudioId">
          <div class="audio-selection-wrapper">
            <el-cascader
              v-model="heygemFormData.referenceAudioPath"
              :options="voiceOptions"
              :props="cascaderProps"
              placeholder="请选择参考音色"
              style="width: 100%"
              filterable
              @change="handleHeygemVoiceSelect"
            />
            <div v-if="selectedHeygemVoice" class="selected-voice-preview">
              <div class="voice-main-row">
                <div class="voice-name">{{ selectedHeygemVoice.name }}</div>
                <div class="voice-meta">
                  <span class="voice-category">{{ selectedHeygemVoice.categoryName }}</span>
                  <span class="voice-duration">{{ formatDuration(selectedHeygemVoice.duration) }}</span>
                </div>
              </div>
              <div class="voice-preview-controls">
                <el-button
                  :type="playingStates.get(selectedHeygemVoice.fileUrl) ? 'warning' : 'success'"
                  size="small"
                  @click="toggleAudioPlay(selectedHeygemVoice.fileUrl)"
                >
                  <el-icon v-if="playingStates.get(selectedHeygemVoice.fileUrl)"><VideoPause /></el-icon>
                  <el-icon v-else><VideoPlay /></el-icon>
                  {{ playingStates.get(selectedHeygemVoice.fileUrl) ? '暂停' : '播放' }}
                </el-button>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 模式2：台词内容 -->
        <el-form-item v-if="heygemFormData.mode === 2" label="台词内容" prop="script">
          <el-input
            v-model="heygemFormData.script"
            type="textarea"
            :rows="4"
            placeholder="请输入数字人的台词内容..."
            maxlength="500"
            show-word-limit
            class="script-input"
          />
        </el-form-item>

        <!-- 人物视频选择 -->
        <el-form-item label="人物视频" prop="characterVideo">
          <div class="video-selection-wrapper">
            <el-cascader
              v-model="heygemSelectedVideoPath"
              :options="videoOptions"
              :props="videoCascaderProps"
              placeholder="请选择视频分类"
              style="width: 100%"
              filterable
              @change="handleHeygemVideoCategorySelect"
            />

            <!-- 搜索框 -->
            <div class="video-search-wrapper">
              <el-input v-model="heygemVideoSearchKeyword" placeholder="搜索视频..." @input="debounceHeygemVideoSearch">
                <template #append>
                  <el-button @click="fetchHeygemVideosFromLibrary">
                    <el-icon><Search /></el-icon>
                  </el-button>
                </template>
              </el-input>
            </div>

            <!-- 视频展示区 -->
            <div class="video-gallery" v-loading="loadingHeygemVideos">
              <div v-if="!heygemFetchedVideos.length" class="empty-gallery">
                <el-empty
                  :description="
                    !heygemSelectedVideoCategory && !heygemVideoSearchKeyword ? '请先选择视频分类' : '暂无视频数据'
                  "
                  :image-size="100"
                />
              </div>
              <div v-else class="video-grid">
                <div
                  v-for="video in heygemFetchedVideos"
                  :key="video.ID"
                  :class="['video-item', { selected: heygemFormData.characterVideo?.ID === video.ID }]"
                  @click="selectHeygemVideo(video)"
                >
                  <div
                    class="video-wrapper"
                    @mouseenter="handleVideoHover(video.ID, true)"
                    @mouseleave="handleVideoHover(video.ID, false)"
                  >
                    <!-- 当没有封面或封面加载失败时，显示视频第一帧 -->
                    <video
                      v-if="!isVideoPlaying(video.ID) && (!video.cover || hasImageError(video.ID))"
                      :src="getVideoUrl(video.url || video.fileUrl)"
                      muted
                      preload="metadata"
                      class="video-cover"
                      @error="handleVideoError"
                    >
                      您的浏览器不支持视频播放
                    </video>
                    <!-- 封面图片，当存在且未出错时显示 -->
                    <img
                      v-else-if="!isVideoPlaying(video.ID) && video.cover"
                      :src="getImageUrl(video.cover)"
                      :alt="video.title || video.name"
                      class="video-cover"
                      @error="handleCoverImageError(video.ID)"
                    />
                    <!-- 视频，鼠标悬停时显示 -->
                    <video
                      v-else
                      :ref="`video-${video.ID}`"
                      :src="getVideoUrl(video.url || video.fileUrl)"
                      muted
                      autoplay
                      loop
                      class="video-player"
                      @error="handleVideoError"
                    >
                      您的浏览器不支持视频播放
                    </video>
                    <div class="video-overlay">
                      <div class="video-name">{{ video.title || video.name }}</div>
                      <div class="video-duration">{{ formatDuration(video.duration) }}</div>
                    </div>
                    <div v-if="heygemFormData.characterVideo?.ID === video.ID" class="selected-indicator">
                      <el-icon><Check /></el-icon>
                    </div>
                    <!-- 播放图标，显示在封面上 -->
                    <div v-if="!isVideoPlaying(video.ID)" class="play-icon">
                      <el-icon><VideoPlay /></el-icon>
                    </div>
                    <!-- 如果视频和封面都无法加载，显示占位符 -->
                    <div
                      v-if="!isVideoPlaying(video.ID) && !video.cover && hasImageError(video.ID)"
                      class="video-placeholder"
                    >
                      <el-icon class="placeholder-icon"><VideoCamera /></el-icon>
                      <div class="placeholder-text">视频预览</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 分页 -->
              <div v-if="heygemFetchedVideos.length" class="video-pagination">
                <el-pagination
                  v-model:current-page="heygemVideoPagination.page"
                  v-model:page-size="heygemVideoPagination.pageSize"
                  :page-sizes="[12, 24, 48]"
                  :total="heygemVideoPagination.total"
                  layout="total, sizes, prev, pager, next"
                  @size-change="handleHeygemVideoSizeChange"
                  @current-change="handleHeygemVideoCurrentChange"
                />
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 视频分类选择 -->
        <el-form-item label="视频分类">
          <el-cascader
            v-model="heygemSelectedCategories"
            :options="categoryOptions"
            :props="{
              checkStrictly: true,
              multiple: false,
              emitPath: false,
              value: 'value',
              label: 'label',
              children: 'children',
              expandTrigger: 'click'
            }"
            placeholder="请选择视频分类"
            clearable
            style="width: 100%"
          />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" :loading="heygemLoading" @click="submitHeygemTask" size="large">
            <el-icon><VideoCamera /></el-icon>
            极速生成数字人视频
          </el-button>
          <el-button @click="resetHeygemForm" size="large">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 任务列表 -->
    <el-card class="task-list-card" v-if="tasks.length > 0">
      <template #header>
        <div class="card-header">
          <span>数字人视频任务</span>
          <el-button text @click="refreshTasks">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <el-table :data="tasks" style="width: 100%">
        <el-table-column prop="script" label="台词内容" min-width="200">
          <template #default="scope">
            <div class="script-preview">
              <el-tag v-if="scope.row.script?.includes('HeyGem')" type="warning" size="small" class="workflow-tag">
                HeyGem
              </el-tag>
              <el-tag v-if="scope.row.heygemMode === 1" type="success" size="small" class="mode-tag"> 模式1 </el-tag>
              <el-tag v-if="scope.row.heygemMode === 2" type="primary" size="small" class="mode-tag"> 模式2 </el-tag>
              {{
                scope.row.script?.includes('HeyGem')
                  ? scope.row.heygemMode === 1
                    ? '音频+视频极速生成'
                    : scope.row.script?.substring(0, 40) + '...'
                  : scope.row.script?.substring(0, 50) + '...'
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="action" label="动作" width="120">
          <template #default="scope">
            <span v-if="scope.row.action">{{ scope.row.action.substring(0, 10) }}...</span>
            <span v-else class="text-gray-400">--</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="时长" width="100">
          <template #default="scope">
            <span v-if="scope.row.duration">{{ formatDuration(scope.row.duration) }}</span>
            <span v-else class="text-gray-400">--</span>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button v-if="scope.row.videoUrl" type="success" size="small" @click="previewVideo(scope.row.videoUrl)">
              <el-icon><VideoPlay /></el-icon>
              预览
            </el-button>
            <el-button v-if="scope.row.videoUrl" type="primary" size="small" @click="downloadVideo(scope.row)">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-button
              v-if="scope.row.status === 'RUNNING' || scope.row.status === 'PENDING'"
              type="info"
              size="small"
              @click="checkTaskStatus(scope.row.taskId, scope.$index)"
            >
              <el-icon><Refresh /></el-icon>
              刷新状态
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 视频预览弹窗 -->
    <el-dialog v-model="videoPreviewVisible" title="视频预览" width="800px" center>
      <div class="video-preview-wrapper">
        <video v-if="previewVideoUrl" :src="previewVideoUrl" controls style="width: 100%; max-height: 500px" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import {
    VideoCamera,
    VideoPlay,
    VideoPause,
    Download,
    Refresh,
    Search,
    Check,
    Microphone,
    Close
  } from '@element-plus/icons-vue'
  import {
    createDigitalHumanTask,
    getDigitalHumanTaskList,
    getDigitalHumanTaskStatus,
    createHeygemDigitalHumanTask
  } from '@/api/ai/digitalHuman'
  import { getMusicList } from '@/api/media/music'
  import { getCategoryList } from '@/api/media/musicCategory'
  import { getCopywritingList, getCopywritingCategoryList } from '@/api/media/copywriting'
  import { getResourceLibrary, getResourceCategories } from '@/api/ai/auto-video'
  import { getVideoCategoryList } from '@/api/creative/videoCategory'
  import { formatDate } from '@/utils/format'
  import { downloadFile } from '@/utils/downloadFile'

  defineOptions({
    name: 'DigitalHuman'
  })

  // 当前激活的工作流
  const activeWorkflow = ref('standard')

  // 标准数字人表单数据
  const formData = reactive({
    referenceAudioId: null,
    referenceAudioPath: [],
    script: '',
    characterImage: null,
    action: '美女在说话',
    videoSize: '720x1280', // 默认设置为竖屏
    fps: 16,
    workflowType: 1
  })

  // HeyGem表单数据
  const heygemFormData = reactive({
    mode: 1, // 默认模式1：音频+视频
    // 模式1专用字段
    directAudioId: null,
    directAudioPath: [],
    // 模式2专用字段
    referenceAudioId: null,
    referenceAudioPath: [],
    script: '',
    // 公共字段
    characterVideo: null
  })

  // 视频分类
  const categoryOptions = ref([])
  const selectedCategories = ref(null)
  const heygemSelectedCategories = ref(null)

  // 表单校验规则
  const rules = {
    referenceAudioId: [{ required: true, message: '请选择参考音色', trigger: 'change' }],
    script: [
      { required: true, message: '请输入台词内容', trigger: 'blur' },
      { min: 1, max: 500, message: '台词长度应在1-500字符之间', trigger: 'blur' }
    ],
    characterImage: [{ required: true, message: '请选择人物图片', trigger: 'change' }],
    action: [
      { required: true, message: '请输入人物动作', trigger: 'blur' },
      { max: 100, message: '动作描述不能超过100字符', trigger: 'blur' }
    ]
  }

  // HeyGem表单校验规则
  const heygemRules = reactive({
    mode: [{ required: true, message: '请选择工作模式', trigger: 'change' }],
    directAudioId: [{ required: false, message: '请选择音频文件', trigger: 'change' }],
    referenceAudioId: [{ required: false, message: '请选择参考音色', trigger: 'change' }],
    script: [{ required: false, message: '请输入台词内容', trigger: 'blur' }],
    characterVideo: [{ required: true, message: '请选择人物视频', trigger: 'change' }]
  })

  // 组件状态
  const loading = ref(false)
  const heygemLoading = ref(false)
  const loadingImages = ref(false)
  const advancedOpen = ref([])
  const digitalHumanForm = ref()
  const heygemForm = ref()
  const scriptSource = ref('manual')
  const videoPreviewVisible = ref(false)
  const previewVideoUrl = ref('')

  // 音频播放状态
  const playingAudios = ref(new Map())
  const playingStates = ref(new Map())

  // HeyGem音色选择
  const selectedHeygemVoice = ref(null)
  const selectedHeygemDirectAudio = ref(null)

  // 音色分类级联选择器配置（只选择分类）
  const cascaderProps = {
    value: 'ID',
    label: 'name',
    children: 'children',
    emitPath: true, // 返回完整路径：[分类ID]
    checkStrictly: true, // 只允许选择任意级别的节点（分类）
    multiple: false // 单选模式
  }

  // 文案级联选择器配置（只选择分类）
  const copywritingCascaderProps = {
    value: 'ID',
    label: 'name',
    children: 'children',
    emitPath: true, // 返回完整路径：[分类ID]
    checkStrictly: true, // 只允许选择任意级别的节点（分类）
    multiple: false // 单选模式
  }

  // 图片级联选择器配置（保持路径方便调试）
  const imageCascaderProps = {
    value: 'ID',
    label: 'name',
    children: 'children',
    emitPath: true // 保持路径以便调试
  }

  // 视频级联选择器配置
  const videoCascaderProps = {
    value: 'ID',
    label: 'name',
    children: 'children',
    emitPath: true // 保持路径以便调试
  }

  // HeyGem音频分类选择器配置
  const heygemAudioCascaderProps = {
    value: 'ID',
    label: 'name',
    children: 'children',
    emitPath: true // 需要路径以便获取最终选中的分类ID
  }

  // 音色选项和相关数据
  const voiceOptions = ref([])
  const selectedVoice = ref(null)
  const selectedVoiceCategory = ref(null)
  const voiceList = ref([])
  const voiceSearchKeyword = ref('')
  const loadingVoice = ref(false)
  const voicePagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0
  })
  const musicMap = ref(new Map())

  // HeyGem音频分类选项（排除音色分类）
  const heygemAudioCategoryOptions = ref([])

  // 缓存分类数据，避免重复获取
  const cachedCategories = ref([])

  // 文案库相关
  const copywritingOptions = ref([])
  const selectedCopywritingPath = ref([])
  const selectedCopywritingCategory = ref(null)
  const selectedCopywriting = ref(null)
  const copywritingList = ref([])
  const copywritingSearchKeyword = ref('')
  const loadingCopywriting = ref(false)
  const copywritingPagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 图片库相关
  const imageOptions = ref([])
  const selectedImagePath = ref([])
  const selectedImageCategory = ref(null)
  const fetchedImages = ref([])
  const imageSearchKeyword = ref('')
  const imagePagination = reactive({
    page: 1,
    pageSize: 12,
    total: 0
  })

  // 视频库相关
  const videoOptions = ref([])

  // HeyGem音频选择相关（基于分类）
  const heygemAudioList = ref([])
  const heygemAudioSearchKeyword = ref('')
  const heygemSelectedAudioCategory = ref(null)
  const heygemAudioPagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0
  })
  const loadingHeygemAudio = ref(false)

  // HeyGem视频库相关
  const heygemSelectedVideoPath = ref([])
  const heygemSelectedVideoCategory = ref(null)
  const heygemFetchedVideos = ref([])
  const heygemVideoSearchKeyword = ref('')
  const heygemVideoPagination = reactive({
    page: 1,
    pageSize: 12,
    total: 0
  })
  const loadingHeygemVideos = ref(false)
  const playingVideos = ref(new Set()) // 记录正在播放的视频ID
  const imageErrors = ref(new Set()) // 记录图片加载错误的视频ID

  // 任务列表
  const tasks = ref([])
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0
  })

  // 获取音乐分类列表
  const fetchCategories = async () => {
    try {
      const res = await getCategoryList()
      if (res.code === 0 && res.data) {
        return res.data
      }
      return []
    } catch (error) {
      console.error('获取分类失败:', error)
      return []
    }
  }

  // 构建音色分类选项（仅分类，不包含音频文件）
  const buildVoiceOptions = (categories) => {
    // 递归处理分类，保持树形结构
    const processCategory = (cat) => {
      if (cat.ID === 0) return null // 排除"全部分类"

      const result = {
        ID: cat.ID,
        name: cat.name,
        children: []
      }

      // 处理子分类
      if (cat.children && cat.children.length > 0) {
        for (const child of cat.children) {
          const processedChild = processCategory(child)
          if (processedChild) {
            result.children.push(processedChild)
          }
        }
      }

      // 如果没有子分类，则删除children属性
      if (result.children.length === 0) {
        delete result.children
      }

      return result
    }

    // 处理所有顶级分类
    const options = []
    for (const category of categories) {
      if (category.ID !== 0) {
        const processedCategory = processCategory(category)
        if (processedCategory) {
          options.push(processedCategory)
        }
      }
    }

    return options
  }

  // 构建HeyGem音频分类选项（支持所有顶级分类）
  const buildHeygemAudioCategoryOptions = (categories) => {
    const options = []

    const processCategory = (cat) => {
      if (cat.ID === 0) return null // 排除"全部分类"

      // 所有分类都可用，不再排除特定分类

      const option = {
        ID: cat.ID,
        name: cat.name,
        children: []
      }

      // 递归处理子分类
      if (cat.children) {
        for (const child of cat.children) {
          const childOption = processCategory(child)
          if (childOption) {
            option.children.push(childOption)
          }
        }
      }

      return option
    }

    for (const category of categories) {
      const option = processCategory(category)
      if (option) {
        options.push(option)
      }
    }

    return options
  }

  // 构建文案分类级联选择器选项（仅分类，不包含文案）
  const buildCopywritingOptions = (categories) => {
    // 递归处理分类，保持树形结构
    const processCategory = (cat) => {
      if (cat.ID === 0) return null // 排除"全部分类"

      const result = {
        ID: cat.ID,
        name: cat.name,
        children: []
      }

      // 处理子分类
      if (cat.children && cat.children.length > 0) {
        for (const child of cat.children) {
          const processedChild = processCategory(child)
          if (processedChild) {
            result.children.push(processedChild)
          }
        }
      }

      // 如果没有子分类，则删除children属性
      if (result.children.length === 0) {
        delete result.children
      }

      return result
    }

    // 处理所有顶级分类
    const options = []
    for (const category of categories) {
      if (category.ID !== 0) {
        const processedCategory = processCategory(category)
        if (processedCategory) {
          options.push(processedCategory)
        }
      }
    }

    return options
  }

  // 获取文案分类和构建选项
  const fetchCopywritingCategories = async () => {
    try {
      const res = await getCopywritingCategoryList()
      console.log('文案分类API响应:', res) // 调试日志
      if (res.code === 0 && res.data) {
        const options = buildCopywritingOptions(res.data)
        console.log('构建的文案分类选项:', options) // 调试日志
        copywritingOptions.value = options
      }
    } catch (error) {
      console.error('获取文案分类失败:', error)
    }
  }

  // 处理文案分类选择
  const handleCopywritingSelect = (value) => {
    console.log('文案分类选择值:', value) // 调试日志

    if (value && value.length > 0) {
      // 最后一个值是选中的分类ID
      const categoryId = value[value.length - 1]
      selectedCopywritingCategory.value = categoryId
      copywritingPagination.page = 1
      console.log('选中的文案分类ID:', categoryId) // 调试日志
      // 立即获取该分类下的文案
      fetchCopywritingFromLibrary()
    } else {
      // 清空分类时，清空文案列表
      selectedCopywritingCategory.value = null
      copywritingList.value = []
      copywritingPagination.total = 0
      selectedCopywriting.value = null
      console.log('清空文案分类') // 调试日志
    }
  }

  // 从文案库获取文案
  const fetchCopywritingFromLibrary = async () => {
    // 如果没有选择分类且没有搜索关键词，不执行搜索
    if (!selectedCopywritingCategory.value && !copywritingSearchKeyword.value) {
      copywritingList.value = []
      copywritingPagination.total = 0
      return
    }

    loadingCopywriting.value = true
    try {
      const params = {
        page: copywritingPagination.page,
        pageSize: copywritingPagination.pageSize
      }

      // 传递分类ID参数
      if (selectedCopywritingCategory.value) {
        params.categoryId = selectedCopywritingCategory.value
      }

      // 传递搜索关键词参数
      if (copywritingSearchKeyword.value) {
        params.keyword = copywritingSearchKeyword.value
      }

      console.log('获取文案参数:', params) // 调试日志

      const res = await getCopywritingList(params)
      if (res.code === 0 && res.data) {
        copywritingList.value = res.data.list || []
        copywritingPagination.total = res.data.total || 0
      } else {
        copywritingList.value = []
        copywritingPagination.total = 0
        ElMessage.warning('获取文案失败: ' + (res.msg || '未知错误'))
      }
    } catch (error) {
      console.error('获取文案失败:', error)
      ElMessage.error('获取文案失败')
      copywritingList.value = []
      copywritingPagination.total = 0
    } finally {
      loadingCopywriting.value = false
    }
  }

  // 选择文案
  const selectCopywriting = (copywriting) => {
    selectedCopywriting.value = copywriting
    formData.script = copywriting.content
  }

  // 清除选择的文案
  const clearSelectedCopywriting = () => {
    selectedCopywriting.value = null
    formData.script = ''
  }

  // 文案搜索防抖
  let copywritingSearchTimeout = null
  const debounceCopywritingSearch = () => {
    clearTimeout(copywritingSearchTimeout)
    copywritingSearchTimeout = setTimeout(() => {
      copywritingPagination.page = 1
      fetchCopywritingFromLibrary()
    }, 500)
  }

  // 文案分页处理
  const handleCopywritingSizeChange = (val) => {
    copywritingPagination.pageSize = val
    copywritingPagination.page = 1
    fetchCopywritingFromLibrary()
  }

  const handleCopywritingCurrentChange = (val) => {
    copywritingPagination.page = val
    fetchCopywritingFromLibrary()
  }

  // 构建图片级联选择器选项
  const buildImageOptions = (categories) => {
    const options = []

    const processCategory = (cat, parentPath = []) => {
      if (cat.ID !== 0) {
        const categoryOption = {
          ID: cat.ID,
          name: cat.name,
          children: cat.children
            ? cat.children.map((child) => processCategory(child, [...parentPath, cat])).filter(Boolean)
            : undefined
        }

        // 如果没有子分类，则作为叶子节点
        if (!categoryOption.children || categoryOption.children.length === 0) {
          delete categoryOption.children
        }

        return categoryOption
      }
      return null
    }

    categories.forEach((category) => {
      if (category.ID !== 0) {
        const option = processCategory(category)
        if (option) {
          options.push(option)
        }
      }
    })

    return options
  }

  // 获取图片分类
  const fetchImageCategories = async () => {
    try {
      const res = await getResourceCategories('image')
      console.log('图片分类API响应:', res) // 调试日志
      if (res.code === 0 && res.data) {
        const options = buildImageOptions(res.data)
        console.log('构建的图片选项:', options) // 调试日志
        imageOptions.value = options
      }
    } catch (error) {
      console.error('获取图片分类失败:', error)
    }
  }

  // 获取视频分类
  const fetchVideoCategories = async () => {
    try {
      const res = await getResourceCategories('video')
      console.log('视频分类API响应:', res) // 调试日志
      if (res.code === 0 && res.data) {
        const options = buildVideoOptions(res.data)
        console.log('构建的视频选项:', options) // 调试日志
        videoOptions.value = options
      }
    } catch (error) {
      console.error('获取视频分类失败:', error)
    }
  }

  // 构建视频级联选择器选项
  const buildVideoOptions = (categories) => {
    const options = []

    const processCategory = (cat, parentPath = []) => {
      if (cat.ID !== 0) {
        const categoryOption = {
          ID: cat.ID,
          name: cat.name,
          children: cat.children
            ? cat.children.map((child) => processCategory(child, [...parentPath, cat])).filter(Boolean)
            : undefined
        }

        // 如果没有子分类，则作为叶子节点
        if (!categoryOption.children || categoryOption.children.length === 0) {
          delete categoryOption.children
        }

        return categoryOption
      }
      return null
    }

    categories.forEach((category) => {
      if (category.ID !== 0) {
        const option = processCategory(category)
        if (option) {
          options.push(option)
        }
      }
    })

    return options
  }

  // 处理图片分类选择
  const handleImageCategorySelect = (value) => {
    console.log('图片分类选择值:', value) // 调试日志

    if (value && value.length > 0) {
      // 由于emitPath: true，value是路径数组，取最后一级作为分类ID
      selectedImageCategory.value = value[value.length - 1]
      imagePagination.page = 1
      console.log('选中的图片分类ID:', selectedImageCategory.value) // 调试日志
      // 立即获取该分类下的图片
      fetchImagesFromLibrary()
    } else {
      // 清空分类时，清空图片列表
      selectedImageCategory.value = null
      fetchedImages.value = []
      imagePagination.total = 0
      console.log('清空图片分类') // 调试日志
    }
  }

  // 从图片库获取图片
  const fetchImagesFromLibrary = async () => {
    // 如果没有选择分类且没有搜索关键词，不执行搜索
    if (!selectedImageCategory.value && !imageSearchKeyword.value) {
      fetchedImages.value = []
      imagePagination.total = 0
      return
    }

    loadingImages.value = true
    try {
      const params = {
        type: 'image',
        page: imagePagination.page,
        pageSize: imagePagination.pageSize
      }

      // 传递分类ID参数
      if (selectedImageCategory.value) {
        params.category_id = selectedImageCategory.value
      }

      // 传递搜索关键词参数
      if (imageSearchKeyword.value) {
        params.keyword = imageSearchKeyword.value
      }

      console.log('获取图片参数:', params) // 调试日志

      const res = await getResourceLibrary(params)
      if (res.code === 0 && res.data) {
        fetchedImages.value = res.data.list || []
        imagePagination.total = res.data.total || 0
      } else {
        fetchedImages.value = []
        imagePagination.total = 0
        ElMessage.warning('获取图片失败: ' + (res.msg || '未知错误'))
      }
    } catch (error) {
      console.error('获取图片失败:', error)
      ElMessage.error('获取图片失败')
      fetchedImages.value = []
      imagePagination.total = 0
    } finally {
      loadingImages.value = false
    }
  }

  // 初始化数据
  const initData = async () => {
    const categories = await fetchCategories()
    cachedCategories.value = categories // 缓存分类数据
    voiceOptions.value = buildVoiceOptions(categories)
    heygemAudioCategoryOptions.value = buildHeygemAudioCategoryOptions(categories)

    await fetchCopywritingCategories()
    await fetchImageCategories()
    await fetchVideoCategories()
    await fetchTasks()

    // 转换视频分类供级联选择
    try {
      const creativeCategoriesRes = await getVideoCategoryList()
      if (creativeCategoriesRes.code === 0 && creativeCategoriesRes.data && creativeCategoriesRes.data.list) {
        categoryOptions.value = creativeCategoriesRes.data.list.map((item) => ({
          value: item.ID,
          label: item.name,
          children:
            item.children && item.children.length > 0
              ? item.children.map((child) => ({
                  value: child.ID,
                  label: child.name,
                  children:
                    child.children && child.children.length > 0
                      ? child.children.map((gc) => ({ value: gc.ID, label: gc.name }))
                      : undefined
                }))
              : undefined
        }))
      }
    } catch (e) {
      console.error('加载视频分类失败', e)
    }
  }

  // 处理音色分类选择
  const handleVoiceSelect = (value) => {
    console.log('音色分类选择值:', value) // 调试日志

    if (value && value.length > 0) {
      // 最后一个值是选中的分类ID
      const categoryId = value[value.length - 1]
      selectedVoiceCategory.value = categoryId
      voicePagination.page = 1
      console.log('选中的音色分类ID:', categoryId) // 调试日志
      // 立即获取该分类下的音频
      fetchVoiceFromLibrary()
    } else {
      // 清空分类时，清空音频列表
      selectedVoiceCategory.value = null
      voiceList.value = []
      voicePagination.total = 0
      selectedVoice.value = null
      formData.referenceAudioId = null
      console.log('清空音色分类') // 调试日志
    }
  }

  // 从音色库获取音频
  const fetchVoiceFromLibrary = async () => {
    // 如果没有选择分类且没有搜索关键词，不执行搜索
    if (!selectedVoiceCategory.value && !voiceSearchKeyword.value) {
      voiceList.value = []
      voicePagination.total = 0
      return
    }

    loadingVoice.value = true
    try {
      const params = {
        page: voicePagination.page,
        pageSize: voicePagination.pageSize
      }

      // 传递分类ID参数
      if (selectedVoiceCategory.value) {
        params.categoryId = selectedVoiceCategory.value
      }

      // 传递搜索关键词参数
      if (voiceSearchKeyword.value) {
        params.keyword = voiceSearchKeyword.value
      }

      console.log('获取音色参数:', params) // 调试日志

      const res = await getMusicList(params)
      if (res.code === 0 && res.data) {
        const musicList = res.data.list || []

        // 获取当前分类的名称（使用缓存的分类数据）
        let selectedCategoryName = '未分类'
        if (selectedVoiceCategory.value && cachedCategories.value.length > 0) {
          selectedCategoryName = findCategoryNameById(selectedVoiceCategory.value, cachedCategories.value) || '未分类'
        }

        // 为每个音频项添加分类名称和处理URL
        voiceList.value = musicList.map((music) => ({
          ...music,
          categoryName: selectedCategoryName,
          fileUrl:
            music.fileUrl?.indexOf('http') === 0 ? music.fileUrl : import.meta.env.VITE_BASE_API + '/' + music.fileUrl
        }))

        // 更新musicMap
        musicList.forEach((music) => {
          musicMap.value.set(music.ID, {
            ...music,
            categoryName: selectedCategoryName,
            fileUrl:
              music.fileUrl?.indexOf('http') === 0 ? music.fileUrl : import.meta.env.VITE_BASE_API + '/' + music.fileUrl
          })
        })

        voicePagination.total = res.data.total || 0
      } else {
        voiceList.value = []
        voicePagination.total = 0
        ElMessage.warning('获取音色失败: ' + (res.msg || '未知错误'))
      }
    } catch (error) {
      console.error('获取音色失败:', error)
      ElMessage.error('获取音色失败')
      voiceList.value = []
      voicePagination.total = 0
    } finally {
      loadingVoice.value = false
    }
  }

  // 选择音色
  const selectVoice = (voice) => {
    selectedVoice.value = voice
    formData.referenceAudioId = voice.ID
  }

  // 清除选择的音色
  const clearSelectedVoice = () => {
    selectedVoice.value = null
    formData.referenceAudioId = null
  }

  // 音色搜索防抖
  let voiceSearchTimeout = null
  const debounceVoiceSearch = () => {
    clearTimeout(voiceSearchTimeout)
    voiceSearchTimeout = setTimeout(() => {
      voicePagination.page = 1
      fetchVoiceFromLibrary()
    }, 500)
  }

  // 音色分页处理
  const handleVoiceSizeChange = (val) => {
    voicePagination.pageSize = val
    voicePagination.page = 1
    fetchVoiceFromLibrary()
  }

  const handleVoiceCurrentChange = (val) => {
    voicePagination.page = val
    fetchVoiceFromLibrary()
  }

  // 处理HeyGem音色选择
  const handleHeygemVoiceSelect = (value) => {
    if (value) {
      heygemFormData.referenceAudioId = value
      selectedHeygemVoice.value = musicMap.value.get(value)
    } else {
      heygemFormData.referenceAudioId = null
      selectedHeygemVoice.value = null
    }
  }

  // 选择HeyGem直接音频（新方法）
  const selectHeygemDirectAudio = (audio) => {
    selectedHeygemDirectAudio.value = audio
    heygemFormData.directAudioId = audio.ID
  }

  // 处理HeyGem音频分类选择
  const handleHeygemDirectAudioCategorySelect = (value) => {
    console.log('HeyGem音频分类选择值:', value)

    if (value && value.length > 0) {
      heygemSelectedAudioCategory.value = value[value.length - 1]
      heygemAudioPagination.page = 1
      console.log('选中的HeyGem音频分类ID:', heygemSelectedAudioCategory.value)
      fetchHeygemAudioList()
    } else {
      heygemSelectedAudioCategory.value = null
      heygemAudioList.value = []
      heygemAudioPagination.total = 0
      selectedHeygemDirectAudio.value = null
      heygemFormData.directAudioId = null
      console.log('清空HeyGem音频分类')
    }
  }

  // 根据分类ID查找分类名称
  const findCategoryNameById = (categoryId, categories) => {
    const findInCategories = (cats) => {
      for (const cat of cats) {
        if (cat.ID === categoryId) {
          return cat.name
        }
        if (cat.children) {
          const found = findInCategories(cat.children)
          if (found) return found
        }
      }
      return null
    }
    return findInCategories(categories)
  }

  // 获取HeyGem音频列表（基于分类）
  const fetchHeygemAudioList = async () => {
    if (!heygemSelectedAudioCategory.value && !heygemAudioSearchKeyword.value) {
      heygemAudioList.value = []
      heygemAudioPagination.total = 0
      return
    }

    loadingHeygemAudio.value = true
    try {
      const params = {
        type: 'audio',
        page: heygemAudioPagination.page,
        pageSize: heygemAudioPagination.pageSize
      }

      if (heygemSelectedAudioCategory.value) {
        params.category_id = heygemSelectedAudioCategory.value
      }

      if (heygemAudioSearchKeyword.value) {
        params.keyword = heygemAudioSearchKeyword.value
      }

      console.log('获取HeyGem音频参数:', params)

      const res = await getResourceLibrary(params)
      if (res.code === 0 && res.data) {
        const audioList = res.data.list || []

        // 获取当前分类的名称（使用缓存的分类数据）
        let selectedCategoryName = '未分类'
        if (heygemSelectedAudioCategory.value && cachedCategories.value.length > 0) {
          selectedCategoryName =
            findCategoryNameById(heygemSelectedAudioCategory.value, cachedCategories.value) || '未分类'
        }

        // 为每个音频项添加分类名称
        heygemAudioList.value = audioList.map((audio) => ({
          ...audio,
          categoryName: selectedCategoryName,
          fileUrl:
            audio.fileUrl?.indexOf('http') === 0 ? audio.fileUrl : import.meta.env.VITE_BASE_API + '/' + audio.fileUrl
        }))

        heygemAudioPagination.total = res.data.total || 0
      } else {
        heygemAudioList.value = []
        heygemAudioPagination.total = 0
        ElMessage.warning('获取音频失败: ' + (res.msg || '未知错误'))
      }
    } catch (error) {
      console.error('获取HeyGem音频失败:', error)
      ElMessage.error('获取音频失败')
      heygemAudioList.value = []
      heygemAudioPagination.total = 0
    } finally {
      loadingHeygemAudio.value = false
    }
  }

  // HeyGem音频搜索防抖
  let heygemAudioSearchTimeout = null
  const debounceHeygemAudioSearch = () => {
    clearTimeout(heygemAudioSearchTimeout)
    heygemAudioSearchTimeout = setTimeout(() => {
      heygemAudioPagination.page = 1
      fetchHeygemAudioList()
    }, 500)
  }

  // HeyGem音频分页处理
  const handleHeygemAudioSizeChange = (val) => {
    heygemAudioPagination.pageSize = val
    heygemAudioPagination.page = 1
    fetchHeygemAudioList()
  }

  const handleHeygemAudioCurrentChange = (val) => {
    heygemAudioPagination.page = val
    fetchHeygemAudioList()
  }

  // 处理HeyGem模式切换
  const handleHeygemModeChange = (mode) => {
    console.log('HeyGem模式切换:', mode)

    // 更新校验规则
    if (mode === 1) {
      // 模式1：音频+视频
      heygemRules.directAudioId = [{ required: true, message: '请选择音频文件', trigger: 'change' }]
      heygemRules.referenceAudioId = [{ required: false }]
      heygemRules.script = [{ required: false }]
    } else if (mode === 2) {
      // 模式2：参考音频+台词+视频
      heygemRules.directAudioId = [{ required: false }]
      heygemRules.referenceAudioId = [{ required: true, message: '请选择参考音色', trigger: 'change' }]
      heygemRules.script = [
        { required: true, message: '请输入台词内容', trigger: 'blur' },
        { min: 1, max: 500, message: '台词长度应在1-500字符之间', trigger: 'blur' }
      ]
    }

    // 清理表单数据
    if (mode === 1) {
      heygemFormData.referenceAudioId = null
      heygemFormData.referenceAudioPath = []
      heygemFormData.script = ''
      selectedHeygemVoice.value = null
    } else {
      heygemFormData.directAudioId = null
      heygemFormData.directAudioPath = []
      selectedHeygemDirectAudio.value = null
      heygemSelectedAudioCategory.value = null
      heygemAudioList.value = []
      heygemAudioSearchKeyword.value = ''
      heygemAudioPagination.page = 1
      heygemAudioPagination.total = 0
    }
  }

  // 选择图片
  const selectImage = (image) => {
    formData.characterImage = image
  }

  // 选择HeyGem视频
  const selectHeygemVideo = (video) => {
    heygemFormData.characterVideo = video
  }

  // 检查视频是否正在播放
  const isVideoPlaying = (videoId) => {
    return playingVideos.value.has(videoId)
  }

  // 处理视频悬停
  const handleVideoHover = (videoId, isHover) => {
    if (isHover) {
      playingVideos.value.add(videoId)
    } else {
      playingVideos.value.delete(videoId)
    }
  }

  // 检查图片是否加载错误
  const hasImageError = (videoId) => {
    return imageErrors.value.has(videoId)
  }

  // 处理封面图片加载错误
  const handleCoverImageError = (videoId) => {
    imageErrors.value.add(videoId)
  }

  // 获取图片URL
  const getImageUrl = (url) => {
    if (!url) return ''
    return url.indexOf('http') === 0 ? url : import.meta.env.VITE_BASE_API + '/' + url
  }

  // 获取视频URL
  const getVideoUrl = (url) => {
    if (!url) return ''
    return url.indexOf('http') === 0 ? url : import.meta.env.VITE_BASE_API + '/' + url
  }

  // 处理图片加载错误
  const handleImageError = (event) => {
    event.target.style.display = 'none'
  }

  // 处理视频加载错误
  const handleVideoError = (event) => {
    event.target.style.display = 'none'
  }

  // 防抖搜索
  let searchTimeout = null
  const debounceImageSearch = () => {
    clearTimeout(searchTimeout)
    searchTimeout = setTimeout(() => {
      imagePagination.page = 1
      fetchImagesFromLibrary()
    }, 500)
  }

  // 图片分页处理
  const handleImageSizeChange = (val) => {
    imagePagination.pageSize = val
    imagePagination.page = 1
    fetchImagesFromLibrary()
  }

  const handleImageCurrentChange = (val) => {
    imagePagination.page = val
    fetchImagesFromLibrary()
  }

  // HeyGem视频分类选择
  const handleHeygemVideoCategorySelect = (value) => {
    console.log('HeyGem视频分类选择值:', value)

    if (value && value.length > 0) {
      heygemSelectedVideoCategory.value = value[value.length - 1]
      heygemVideoPagination.page = 1
      console.log('选中的HeyGem视频分类ID:', heygemSelectedVideoCategory.value)
      fetchHeygemVideosFromLibrary()
    } else {
      heygemSelectedVideoCategory.value = null
      heygemFetchedVideos.value = []
      heygemVideoPagination.total = 0
      console.log('清空HeyGem视频分类')
    }
  }

  // 获取HeyGem视频
  const fetchHeygemVideosFromLibrary = async () => {
    if (!heygemSelectedVideoCategory.value && !heygemVideoSearchKeyword.value) {
      heygemFetchedVideos.value = []
      heygemVideoPagination.total = 0
      return
    }

    loadingHeygemVideos.value = true
    try {
      const params = {
        type: 'video',
        page: heygemVideoPagination.page,
        pageSize: heygemVideoPagination.pageSize
      }

      if (heygemSelectedVideoCategory.value) {
        params.category_id = heygemSelectedVideoCategory.value
      }

      if (heygemVideoSearchKeyword.value) {
        params.keyword = heygemVideoSearchKeyword.value
      }

      console.log('获取HeyGem视频参数:', params)

      const res = await getResourceLibrary(params)
      if (res.code === 0 && res.data) {
        heygemFetchedVideos.value = res.data.list || []
        heygemVideoPagination.total = res.data.total || 0
      } else {
        heygemFetchedVideos.value = []
        heygemVideoPagination.total = 0
        ElMessage.warning('获取视频失败: ' + (res.msg || '未知错误'))
      }
    } catch (error) {
      console.error('获取HeyGem视频失败:', error)
      ElMessage.error('获取视频失败')
      heygemFetchedVideos.value = []
      heygemVideoPagination.total = 0
    } finally {
      loadingHeygemVideos.value = false
    }
  }

  // HeyGem视频搜索防抖
  let heygemVideoSearchTimeout = null
  const debounceHeygemVideoSearch = () => {
    clearTimeout(heygemVideoSearchTimeout)
    heygemVideoSearchTimeout = setTimeout(() => {
      heygemVideoPagination.page = 1
      fetchHeygemVideosFromLibrary()
    }, 500)
  }

  // HeyGem视频分页处理
  const handleHeygemVideoSizeChange = (val) => {
    heygemVideoPagination.pageSize = val
    heygemVideoPagination.page = 1
    fetchHeygemVideosFromLibrary()
  }

  const handleHeygemVideoCurrentChange = (val) => {
    heygemVideoPagination.page = val
    fetchHeygemVideosFromLibrary()
  }

  // 切换音频播放/暂停
  const toggleAudioPlay = (audioUrl) => {
    const isPlaying = playingStates.value.get(audioUrl)

    if (isPlaying) {
      const audio = playingAudios.value.get(audioUrl)
      if (audio) {
        audio.pause()
        playingStates.value.set(audioUrl, false)
      }
    } else {
      playingAudios.value.forEach((audio, url) => {
        if (url !== audioUrl) {
          audio.pause()
          playingStates.value.set(url, false)
        }
      })

      let audio = playingAudios.value.get(audioUrl)
      if (!audio) {
        audio = new Audio(audioUrl)
        playingAudios.value.set(audioUrl, audio)

        audio.addEventListener('ended', () => {
          playingStates.value.set(audioUrl, false)
        })

        audio.addEventListener('error', () => {
          console.error('播放失败:', audioUrl)
          ElMessage.error('播放失败')
          playingStates.value.set(audioUrl, false)
        })
      }

      audio
        .play()
        .then(() => {
          playingStates.value.set(audioUrl, true)
        })
        .catch((error) => {
          console.error('播放失败:', error)
          ElMessage.error('播放失败')
          playingStates.value.set(audioUrl, false)
        })
    }
  }

  // 提交任务
  const submitTask = async () => {
    try {
      await digitalHumanForm.value.validate()

      if (!selectedVoice.value) {
        ElMessage.warning('请选择参考音色')
        return
      }

      if (!formData.characterImage) {
        ElMessage.warning('请选择人物图片')
        return
      }

      loading.value = true

      const requestData = {
        script: formData.script,
        action: formData.action,
        characterImageUrl: getImageUrl(formData.characterImage.fileUrl),
        referenceAudioUrl: selectedVoice.value.fileUrl,
        referenceAudioName: selectedVoice.value.name,
        videoSize: formData.videoSize,
        fps: formData.fps,
        categoryId: selectedCategories.value || 0,
        workflowType: formData.workflowType
      }

      console.log('准备发送的请求数据:', requestData)

      const res = await createDigitalHumanTask(requestData)
      if (res.code === 0) {
        ElMessage.success('数字人视频任务已提交，请稍候查看结果')
        await fetchTasks()
      } else {
        ElMessage.error('创建任务失败: ' + (res.msg || '未知错误'))
      }
    } catch (error) {
      console.error('提交失败:', error)
      ElMessage.error('提交失败: ' + (error.message || '未知错误'))
    } finally {
      loading.value = false
    }
  }

  // 重置表单
  const resetForm = () => {
    digitalHumanForm.value?.resetFields()
    formData.referenceAudioPath = []
    selectedVoiceCategory.value = null
    selectedVoice.value = null
    voiceList.value = []
    voiceSearchKeyword.value = ''
    voicePagination.page = 1
    voicePagination.total = 0
    formData.characterImage = null
    scriptSource.value = 'manual'
    selectedCopywritingPath.value = []
    selectedCopywritingCategory.value = null
    selectedCopywriting.value = null
    copywritingList.value = []
    copywritingSearchKeyword.value = ''
    copywritingPagination.page = 1
    copywritingPagination.total = 0
    selectedImagePath.value = []
    selectedImageCategory.value = null
    fetchedImages.value = []
    imageSearchKeyword.value = ''
    imagePagination.page = 1
    imagePagination.total = 0
    advancedOpen.value = []
  }

  // 重置HeyGem表单
  const resetHeygemForm = () => {
    heygemForm.value?.resetFields()
    heygemFormData.mode = 1 // 重置为默认模式
    heygemFormData.directAudioId = null
    heygemFormData.directAudioPath = []
    heygemFormData.referenceAudioId = null
    heygemFormData.referenceAudioPath = []
    heygemFormData.script = ''
    selectedHeygemVoice.value = null
    selectedHeygemDirectAudio.value = null
    heygemSelectedAudioCategory.value = null
    heygemAudioList.value = []
    heygemAudioSearchKeyword.value = ''
    heygemAudioPagination.page = 1
    heygemAudioPagination.total = 0
    heygemFormData.characterVideo = null
    heygemSelectedVideoPath.value = []
    heygemSelectedVideoCategory.value = null
    heygemFetchedVideos.value = []
    heygemVideoSearchKeyword.value = ''
    heygemVideoPagination.page = 1
    heygemVideoPagination.total = 0
    playingVideos.value.clear() // 清除视频播放状态
    imageErrors.value.clear() // 清除图片错误状态
  }

  // 处理工作流切换
  const handleWorkflowChange = (activeName) => {
    console.log('切换工作流:', activeName)
    // 可以在这里做一些特定的处理
  }

  // 提交HeyGem任务
  const submitHeygemTask = async () => {
    try {
      await heygemForm.value.validate()

      if (!heygemFormData.characterVideo) {
        ElMessage.warning('请选择人物视频')
        return
      }

      // 根据模式验证不同的必需字段
      if (heygemFormData.mode === 1) {
        if (!selectedHeygemDirectAudio.value) {
          ElMessage.warning('请选择音频文件')
          return
        }
      } else if (heygemFormData.mode === 2) {
        if (!selectedHeygemVoice.value) {
          ElMessage.warning('请选择参考音色')
          return
        }
        if (!heygemFormData.script) {
          ElMessage.warning('请输入台词内容')
          return
        }
      }

      heygemLoading.value = true

      // 根据模式构建不同的请求数据
      const requestData = {
        mode: heygemFormData.mode,
        characterVideoUrl: getVideoUrl(heygemFormData.characterVideo.url || heygemFormData.characterVideo.fileUrl),
        categoryId: heygemSelectedCategories.value || 0,
        workflowType: 3 // HeyGem工作流类型
      }

      if (heygemFormData.mode === 1) {
        // 模式1：音频+视频
        requestData.directAudioUrl = selectedHeygemDirectAudio.value.fileUrl
        requestData.directAudioName = selectedHeygemDirectAudio.value.name
      } else {
        // 模式2：参考音频+台词+视频
        requestData.referenceAudioUrl = selectedHeygemVoice.value.fileUrl
        requestData.referenceAudioName = selectedHeygemVoice.value.name
        requestData.script = heygemFormData.script
      }

      console.log('准备发送的HeyGem请求数据:', requestData)

      const res = await createHeygemDigitalHumanTask(requestData)
      if (res.code === 0) {
        ElMessage.success('HeyGem数字人视频任务已提交，请稍候查看结果')
        await fetchTasks()
      } else {
        ElMessage.error('创建HeyGem任务失败: ' + (res.msg || '未知错误'))
      }
    } catch (error) {
      console.error('提交HeyGem任务失败:', error)
      ElMessage.error('提交失败: ' + (error.message || '未知错误'))
    } finally {
      heygemLoading.value = false
    }
  }

  // 获取任务列表
  const fetchTasks = async () => {
    try {
      const res = await getDigitalHumanTaskList({
        page: pagination.page,
        pageSize: pagination.pageSize
      })
      if (res.code === 0) {
        tasks.value = res.data.list || []
        pagination.total = res.data.total || 0
      }
    } catch (error) {
      console.error('获取任务列表失败:', error)
    }
  }

  // 刷新任务列表
  const refreshTasks = () => {
    fetchTasks()
  }

  // 检查任务状态
  const checkTaskStatus = async (taskId, taskIndex = null) => {
    // 检查taskId是否有效
    if (!taskId || taskId.trim() === '') {
      // 如果传递了任务索引，直接使用；否则尝试查找
      let targetIndex = taskIndex
      if (targetIndex === null) {
        targetIndex = tasks.value.findIndex((task) => task.taskId === taskId || (!task.taskId && task.id))
      }

      if (targetIndex !== null && targetIndex !== -1 && targetIndex < tasks.value.length) {
        tasks.value[targetIndex].status = 'FAILED'
        tasks.value[targetIndex].errorMsg = '任务ID为空，无法查询状态'
        tasks.value[targetIndex].endTime = new Date().toISOString()
        ElMessage.warning('任务ID为空，已将该任务标记为失败')
      } else {
        ElMessage.error('任务ID为空，无法刷新状态')
      }
      return
    }

    try {
      const res = await getDigitalHumanTaskStatus(taskId)
      if (res.code === 0) {
        await fetchTasks()
        if (res.data.status === 'SUCCEEDED') {
          ElMessage.success('任务已完成')
        } else if (res.data.status === 'FAILED') {
          ElMessage.error('任务失败: ' + (res.data.errorMsg || '未知错误'))
        } else {
          ElMessage.success('任务状态已更新')
        }
      } else {
        ElMessage.error('刷新失败: ' + (res.msg || '未知错误'))
      }
    } catch (error) {
      console.error('检查状态失败:', error)
      if (error.response && error.response.status === 404) {
        ElMessage.error('任务不存在或任务ID无效')
      } else {
        ElMessage.error('系统错误，请稍后重试')
      }
    }
  }

  // 预览视频
  const previewVideo = (videoUrl) => {
    previewVideoUrl.value = videoUrl
    videoPreviewVisible.value = true
  }

  // 下载视频
  const downloadVideo = async (task) => {
    try {
      await downloadFile(task.videoUrl, `数字人视频_${task.action}_${task.taskId}.mp4`)
    } catch (error) {
      console.error('下载失败:', error)
      ElMessage.error('下载失败')
    }
  }

  // 获取状态类型
  const getStatusType = (status) => {
    const statusMap = {
      RUNNING: 'warning',
      SUCCEEDED: 'success',
      FAILED: 'danger',
      PENDING: 'info',
      SUSPENDED: 'warning',
      NOT_FOUND: 'danger',
      UNKNOWN: 'info'
    }
    return statusMap[status] || 'info'
  }

  // 获取状态文本
  const getStatusText = (status) => {
    const statusMap = {
      RUNNING: '处理中',
      SUCCEEDED: '已完成',
      FAILED: '失败',
      PENDING: '等待中',
      SUSPENDED: '挂起',
      NOT_FOUND: '未找到',
      UNKNOWN: '未知'
    }
    return statusMap[status] || '未知'
  }

  // 格式化时长
  const formatDuration = (seconds) => {
    if (!seconds || seconds <= 0) return '--'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // 分页处理
  const handleSizeChange = (val) => {
    pagination.pageSize = val
    fetchTasks()
  }

  const handleCurrentChange = (val) => {
    pagination.page = val
    fetchTasks()
  }

  onMounted(() => {
    initData()
  })
</script>

<style scoped>
  .digital-human-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
  }

  .workflow-tabs-card {
    margin-bottom: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .tab-description {
    padding: 15px 0;
    color: var(--el-text-color-regular);
  }

  .tab-description p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
  }

  .heygem-features {
    margin-top: 10px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .heygem-instructions {
    margin-bottom: 25px;
  }

  .instruction-content {
    font-size: 14px;
    line-height: 1.6;
  }

  .instruction-content ol {
    margin: 10px 0;
    padding-left: 20px;
  }

  .instruction-content li {
    margin: 5px 0;
  }

  .mode-description {
    margin-top: 15px;
  }

  .mode-tip {
    margin: 0;
    padding: 10px 15px;
    background: var(--el-fill-color-light);
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.5;
    color: var(--el-text-color-regular);
  }

  .page-header {
    margin-bottom: 30px;
    padding: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .header-left h2 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 600;
  }

  .description {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
    line-height: 1.5;
  }

  .header-icon {
    opacity: 0.8;
  }

  .main-form-card {
    margin-bottom: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .audio-selection-wrapper {
    width: 100%;
  }

  .voice-search-wrapper {
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .voice-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    padding: 15px;
    background: var(--el-fill-color-extra-light);
    margin-top: 15px;
  }

  .voice-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .voice-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    background: var(--el-fill-color-blank);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
  }

  .voice-item:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .voice-item.selected {
    border-color: var(--el-color-primary);
    background: rgba(64, 158, 255, 0.05);
  }

  .voice-info {
    flex: 1;
    min-width: 0;
  }

  .voice-controls {
    flex-shrink: 0;
    margin-left: 12px;
  }

  .voice-pagination {
    margin-top: 15px;
    display: flex;
    justify-content: center;
  }

  .empty-voice {
    text-align: center;
    padding: 40px 0;
  }

  .audio-search-wrapper {
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .audio-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    padding: 15px;
    background: var(--el-fill-color-extra-light);
  }

  .audio-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .audio-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    background: var(--el-fill-color-blank);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
  }

  .audio-item:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .audio-item.selected {
    border-color: var(--el-color-primary);
    background: rgba(64, 158, 255, 0.05);
  }

  .audio-info {
    flex: 1;
    min-width: 0;
  }

  .audio-main-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .audio-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    margin-right: 12px;
  }

  .audio-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
  }

  .audio-category {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    white-space: nowrap;
  }

  .audio-duration {
    font-size: 11px;
    color: var(--el-color-info);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    white-space: nowrap;
  }

  .audio-controls {
    flex-shrink: 0;
    margin-left: 12px;
  }

  .audio-pagination {
    margin-top: 15px;
    display: flex;
    justify-content: center;
  }

  .empty-audio {
    text-align: center;
    padding: 40px 0;
  }

  .selected-voice-preview {
    margin-top: 15px;
    padding: 15px;
    background: var(--el-fill-color-light);
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .voice-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .voice-main-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .voice-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    margin-right: 12px;
    min-width: 0; /* 允许flex项目收缩到内容以下 */
  }

  .voice-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
  }

  .voice-category {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    white-space: nowrap;
  }

  .voice-duration {
    font-size: 11px;
    color: var(--el-color-info);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    white-space: nowrap;
  }

  .voice-preview-controls {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .script-wrapper {
    width: 100%;
  }

  .script-source-selector {
    margin-bottom: 15px;
  }

  .script-input {
    width: 100%;
  }

  .library-script {
    width: 100%;
  }

  .copywriting-search-wrapper {
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .copywriting-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    padding: 15px;
    background: var(--el-fill-color-extra-light);
    margin-top: 15px;
  }

  .copywriting-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .copywriting-item {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 15px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    background: var(--el-fill-color-blank);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
  }

  .copywriting-item:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .copywriting-item.selected {
    border-color: var(--el-color-primary);
    background: rgba(64, 158, 255, 0.05);
  }

  .copywriting-info {
    flex: 1;
    min-width: 0;
  }

  .copywriting-title {
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .copywriting-content {
    font-size: 14px;
    color: var(--el-text-color-regular);
    line-height: 1.5;
    margin-bottom: 8px;
    word-break: break-all;
  }

  .copywriting-meta {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .copywriting-length {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    background: var(--el-color-info-light-9);
    padding: 2px 6px;
    border-radius: 4px;
  }

  .copywriting-pagination {
    margin-top: 15px;
    display: flex;
    justify-content: center;
  }

  .empty-copywriting {
    text-align: center;
    padding: 40px 0;
  }

  .selected-copywriting-preview {
    margin-top: 15px;
    padding: 15px;
    background: var(--el-fill-color-light);
    border-radius: 6px;
  }

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .preview-title {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .copywriting-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .copywriting-category {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    background: var(--el-color-primary-light-9);
    padding: 2px 8px;
    border-radius: 4px;
  }

  .image-search-wrapper {
    margin-top: 15px;
  }

  .selected-script-preview {
    margin-top: 15px;
  }

  .copywriting-option {
    padding: 5px 0;
  }

  .copywriting-title {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .copywriting-preview {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 3px;
  }

  .image-selection-wrapper {
    width: 100%;
  }

  .video-selection-wrapper {
    width: 100%;
  }

  .video-search-wrapper {
    margin-top: 15px;
  }

  .image-gallery {
    margin-top: 20px;
    min-height: 300px;
    padding: 15px;
    border-radius: 10px;
    background: var(--el-fill-color-extra-light);
  }

  .empty-gallery {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }

  .image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
  }

  .image-item {
    cursor: pointer;
    border-radius: 12px;
    overflow: hidden;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    background: var(--el-fill-color-lighter);
  }

  .image-item:hover {
    border-color: var(--el-color-primary);
    transform: translateY(-4px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
  }

  .image-item.selected {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2);
  }

  .image-wrapper {
    position: relative;
    width: 100%;
    min-height: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: var(--el-fill-color-blank);
  }

  .image-wrapper img {
    max-width: 100%;
    max-height: 280px;
    width: auto;
    height: auto;
    object-fit: contain;
    transition: transform 0.3s ease;
    border-radius: 8px;
  }

  .image-item:hover .image-wrapper img {
    transform: scale(1.05);
  }

  .image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 10px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
  }

  .image-item:hover .image-overlay {
    transform: translateY(0);
  }

  .image-name {
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 2px;
  }

  .image-dimensions {
    font-size: 10px;
    opacity: 0.8;
  }

  .selected-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: var(--el-color-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }

  .image-pagination {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  .video-gallery {
    margin-top: 20px;
    min-height: 300px;
    padding: 15px;
    border-radius: 10px;
    background: var(--el-fill-color-extra-light);
  }

  .video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
  }

  .video-item {
    cursor: pointer;
    border-radius: 12px;
    overflow: hidden;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    background: var(--el-fill-color-lighter);
  }

  .video-item:hover {
    border-color: var(--el-color-primary);
    transform: translateY(-4px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
  }

  .video-item.selected {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2);
  }

  .video-wrapper {
    position: relative;
    width: 100%;
    height: 180px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: #f0f0f0;
    border-radius: 8px;
  }

  .video-cover,
  .video-player {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
    border-radius: 8px;
    background: #000;
  }

  .video-item:hover .video-cover,
  .video-item:hover .video-player {
    transform: scale(1.05);
  }

  .play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 48px;
    height: 48px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    transition: all 0.3s ease;
    pointer-events: none;
  }

  .video-item:hover .play-icon {
    background: rgba(0, 0, 0, 0.8);
    transform: translate(-50%, -50%) scale(1.1);
  }

  .video-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #999;
  }

  .placeholder-icon {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .placeholder-text {
    font-size: 12px;
  }

  .video-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 10px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
  }

  .video-item:hover .video-overlay {
    transform: translateY(0);
  }

  .video-name {
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 2px;
  }

  .video-duration {
    font-size: 10px;
    opacity: 0.8;
  }

  .video-pagination {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  .action-tips {
    margin-top: 10px;
    width: 100%;
  }

  .tips-content p {
    font-size: 13px;
  }

  .advanced-settings-wrapper {
    width: 100%;
  }

  .advanced-settings-content {
    margin: 0;
    padding: 0;
  }

  .task-list-card {
    margin-top: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .script-preview {
    line-height: 1.5;
    color: var(--el-text-color-primary);
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .workflow-tag {
    flex-shrink: 0;
  }

  .mode-tag {
    flex-shrink: 0;
    margin-left: 4px;
  }

  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  .video-preview-wrapper {
    text-align: center;
  }

  :deep(.el-collapse-item__header) {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    padding: 0 20px;
    height: 48px;
    line-height: 48px;
  }

  :deep(.el-collapse) {
    border: none;
  }

  :deep(.el-collapse-item) {
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
  }

  :deep(.el-collapse-item__header) {
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-extra-light);
    padding: 0 16px;
    border-radius: 6px 6px 0 0;
  }

  :deep(.el-collapse-item__content) {
    padding: 16px;
    border-radius: 0 0 6px 6px;
  }

  :deep(.el-form-item) {
    margin-bottom: 24px;
  }

  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-input-number) {
    width: 100%;
  }

  :deep(.el-radio-group) {
    width: 100%;
  }
</style>
