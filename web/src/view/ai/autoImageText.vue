<template>
  <div class="auto-imagetext-container">
    <el-card class="auto-imagetext-card">
      <div class="page-layout">
        <div class="main-config-area">
          <el-form :model="formData" label-position="right" label-width="120px" class="auto-imagetext-form">
            <!-- 主要配置区域，使用 Tabs -->
            <div class="main-content">
              <el-tabs v-model="activeTab" class="config-tabs">
                <!-- 图片素材 Tab -->
                <el-tab-pane name="images">
                  <template #label>
                    <span>
                      <el-icon>
                        <Picture />
                      </el-icon>
                      <span style="margin-left: 5px">图片素材</span>
                    </span>
                  </template>
                  <ImageMaterials
                    :materials="formData.imageMaterials"
                    @update:materials="formData.imageMaterials = $event"
                    @groupsChanged="handleGroupsChanged"
                    ref="imageMaterialsRef"
                  />
                </el-tab-pane>

                <!-- 标题设置 Tab -->
                <el-tab-pane name="title">
                  <template #label>
                    <span>
                      <el-icon>
                        <Document />
                      </el-icon>
                      <span style="margin-left: 5px">标题设置</span>
                    </span>
                  </template>
                  <div class="title-settings-wrapper">
                    <TitleSettings
                      :titles="formData.titleSettings"
                      @update:titles="handleTitlesUpdate"
                      :aspectRatio="'1:1'"
                      @active-title-changed="handleActiveTitleChanged"
                    />
                  </div>
                </el-tab-pane>

                <!-- 背景音乐 Tab -->
                <el-tab-pane name="music">
                  <template #label>
                    <span>
                      <el-icon>
                        <Headset />
                      </el-icon>
                      <span style="margin-left: 5px">背景音乐</span>
                    </span>
                  </template>
                  <BackgroundMusic
                    :musics="formData.backgroundMusics"
                    @update:musics="formData.backgroundMusics = $event"
                  />
                </el-tab-pane>

                <!-- 贴纸设置 Tab -->
                <el-tab-pane name="sticker">
                  <template #label>
                    <span>
                      <el-icon>
                        <Star />
                      </el-icon>
                      <span style="margin-left: 5px">贴纸设置</span>
                    </span>
                  </template>
                  <StickerSettings
                    :stickers="formData.stickerSettings"
                    @update:stickers="formData.stickerSettings = $event"
                  />
                </el-tab-pane>

                <!-- 其他设置 Tab -->
                <el-tab-pane name="other">
                  <template #label>
                    <span>
                      <el-icon>
                        <Setting />
                      </el-icon>
                      <span style="margin-left: 5px">其他设置</span>
                    </span>
                  </template>
                  <OtherSettings
                    :settings="formData.otherSettings"
                    :pseudoOriginalSettings="formData.pseudoOriginalSettings"
                    :selectedCategories="selectedCategories"
                    :categoryOptions="categoryOptions"
                    :isMultipleGroups="isMultipleGroups"
                    :imageMaterials="formData.imageMaterials"
                    :topicCategoryOptions="topicCategoryOptions"
                    @update:settings="updateOtherSettings"
                    @update:pseudoOriginalSettings="updatePseudoOriginalSettings"
                    @update:selectedCategories="selectedCategories = $event"
                    @update:topicSettings="updateTopicSettings"
                  />
                </el-tab-pane>
              </el-tabs>

              <!-- 底部提交按钮 -->
              <div class="form-actions">
                <el-button type="primary" @click="handleSubmit" :loading="loading" size="large" class="submit-btn">
                  生成图文
                </el-button>
              </div>
            </div>
          </el-form>
        </div>
        <div class="preview-area-container">
          <ImageTextPreview
            :imageMaterials="formData.imageMaterials"
            :titleSettings="formData.titleSettings"
            :stickerSettings="formData.stickerSettings"
            :activeTitleIndex="currentActiveTitleIndex"
            :otherSettings="formData.otherSettings"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
  export default {
    name: 'AutoImageText'
  }
</script>

<script setup>
  import { ref, reactive, onMounted, computed } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Picture, Document, Headset, Star, Setting } from '@element-plus/icons-vue'
  import { submitAutoImageTextTask } from '@/api/ai/auto-imagetext'
  import { getVideoCategoryList } from '@/api/creative/videoCategory'
  import { getCategoryList } from '@/api/media/topicCategory'
  import { fontList } from '@/components/videoEffects.js'

  // 导入组件
  import ImageMaterials from '@/components/autoImageText/ImageMaterials.vue'
  import TitleSettings from '@/components/autoImageText/TitleSettings.vue'
  import BackgroundMusic from '@/components/autoImageText/BackgroundMusic.vue'
  import StickerSettings from '@/components/autoImageText/StickerSettings.vue'
  import OtherSettings from '@/components/autoImageText/OtherSettings.vue'
  import ImageTextPreview from '@/components/autoImageText/ImageTextPreview.vue'

  // 创建子组件引用
  const imageMaterialsRef = ref(null)

  // 添加 activeTab 状态
  const activeTab = ref('images')

  // 加载状态
  const loading = ref(false)
  const selectedCategories = ref([])
  const categoryOptions = ref([])

  // 话题相关状态
  const topicCategoryOptions = ref([])

  // 表单数据
  const formData = reactive({
    // 图片素材
    imageMaterials: [
      {
        mediaId: '', // 媒体ID
        mediaUrl: '' // 媒体URL
      }
    ],

    // 标题设置
    titleSettings: [
      {
        height: 0.15,
        fontFamily: fontList[0]?.value || '', // 默认字体：使用字体列表中的第一个字体
        fontSize: 20,
        alignment: 'center',
        fontStyle: {
          type: 'flower',
          color: '#ffffff',
          styleType: 'none',
          backgroundColor: '',
          borderColor: '',
          borderWidth: 1
        },
        flowerStyle: '',
        randomEffect: true,
        content: '',
        id: Date.now() + Math.random().toString(36).substring(2, 9),
        copywritingSelectionParams: null
      }
    ],

    // 背景音乐
    backgroundMusics: [],

    // 贴纸设置
    stickerSettings: [],

    // 其他设置
    otherSettings: {
      generateCount: undefined, // 生成数量
      minImageCount: 3, // 最小图片数量
      maxImageCount: 9, // 最大图片数量
      titleApplyMode: 1, // 标题应用模式：1-仅第一张 2-每张都加
      stickerApplyMode: 1, // 贴纸应用模式：1-仅第一张 2-每张都加
      imageTextDescription: '', // 图文描述
      topic: '' // 话题
    },

    // 伪原创设置
    pseudoOriginalSettings: {
      brightnessMin: 0.95, // 亮度调整最小值
      brightnessMax: 1.05, // 亮度调整最大值
      contrastMin: 0.98, // 对比度调整最小值
      contrastMax: 1.02, // 对比度调整最大值
      saturationMin: 0.98, // 饱和度调整最小值
      saturationMax: 1.02, // 饱和度调整最大值
      rotationMin: -0.5, // 旋转角度最小值（度）
      rotationMax: 0.5, // 旋转角度最大值（度）
      sizeMin: 0.99, // 尺寸调整最小值
      sizeMax: 1.01 // 尺寸调整最大值
    },

    // 话题设置
    topicSettings: {
      categoryId: null,
      selectionMode: 'none',
      coreTopics: [],
      finalTopicGroups: [],
      finalTopicOptions: [],
      customTopicGroups: [],
      searchStatus: []
    }
  })

  // 更新其他设置
  const updateOtherSettings = (settings) => {
    formData.otherSettings = {
      ...formData.otherSettings,
      ...settings
    }
  }

  // 更新话题设置
  const updateTopicSettings = (settings) => {
    formData.topicSettings = {
      ...formData.topicSettings,
      ...settings
    }
  }

  // 更新伪原创设置
  const updatePseudoOriginalSettings = (settings) => {
    formData.pseudoOriginalSettings = {
      ...formData.pseudoOriginalSettings,
      ...settings
    }
  }

  // 在组件加载时获取分类数据
  onMounted(async () => {
    try {
      await Promise.all([
        loadCategories(), // 获取视频分类
        loadTopicCategories() // 获取话题分类
      ])
      // 初始化时检查分组数量
      handleGroupsChanged(formData.imageMaterials.length)
    } catch (error) {
      console.error('Error during onMounted execution:', error)
      ElMessage.error('页面加载期间发生错误，部分数据可能无法加载')
    }
  })

  // 加载视频分类
  const loadCategories = async () => {
    try {
      const response = await getVideoCategoryList()
      if (response.code === 0 && response.data && response.data.list) {
        categoryOptions.value = response.data.list.map((item) => ({
          value: item.ID,
          label: item.name,
          children:
            item.children && item.children.length > 0
              ? item.children.map((child) => ({
                  value: child.ID,
                  label: child.name,
                  children:
                    child.children && child.children.length > 0
                      ? child.children.map((grandChild) => ({
                          value: grandChild.ID,
                          label: grandChild.name
                        }))
                      : undefined
                }))
              : undefined
        }))
      } else {
        categoryOptions.value = []
        ElMessage.warning('获取视频分类失败: ' + (response.message || '数据格式错误或无数据'))
      }
    } catch (error) {
      console.error('获取视频分类失败:', error)
      categoryOptions.value = []
      ElMessage.warning('获取视频分类失败')
    }
  }

  // 加载话题分类
  const loadTopicCategories = async () => {
    try {
      const response = await getCategoryList()
      if (response.code === 0 && response.data) {
        // 构建话题分类树形结构
        topicCategoryOptions.value = response.data.map((item) => ({
          ID: item.ID,
          name: item.name,
          children:
            item.children && item.children.length > 0
              ? item.children.map((child) => ({
                  ID: child.ID,
                  name: child.name,
                  children:
                    child.children && child.children.length > 0
                      ? child.children.map((grandChild) => ({
                          ID: grandChild.ID,
                          name: grandChild.name
                        }))
                      : undefined
                }))
              : undefined
        }))
      } else {
        topicCategoryOptions.value = []
        ElMessage.warning('获取话题分类失败: ' + (response.message || '数据格式错误或无数据'))
      }
    } catch (error) {
      console.error('获取话题分类失败:', error)
      topicCategoryOptions.value = []
      ElMessage.warning('获取话题分类失败')
    }
  }

  // 提交表单
  const handleSubmit = async () => {
    try {
      // 验证表单前，先调用图片组件的验证方法，过滤无效素材
      if (imageMaterialsRef.value) {
        formData.imageMaterials = imageMaterialsRef.value.validateMaterials()
      }

      if (!validateForm()) {
        return
      }

      loading.value = true

      // 构建提交参数
      const params = buildSubmitParams()

      // 发送请求
      const response = await submitAutoImageTextTask(params)

      // 判断返回值类型处理
      if (typeof response === 'string') {
        ElMessage.success('任务提交成功，正在后台生成图文，请耐心等待...')
      } else if (response && response.code === 0) {
        ElMessage.success('任务提交成功，正在后台生成图文，请耐心等待...')
      } else {
        ElMessage.error((response && response.message) || '提交任务失败')
        loading.value = false
        return
      }

      // 任务提交成功后将生成数量置空
      formData.otherSettings.generateCount = undefined
    } catch (error) {
      console.error('提交任务出错:', error)
      ElMessage.error('提交任务出错: ' + (error.message || '未知错误'))
    } finally {
      // 保持按钮loading状态5秒后才恢复
      setTimeout(() => {
        loading.value = false
      }, 5000)
    }
  }

  // 验证表单
  const validateForm = () => {
    // 过滤有效的图片素材
    const validMaterials = formData.imageMaterials.filter((m) => m.mediaId || m.mediaUrl)

    if (validMaterials.length === 0) {
      ElMessage.warning('请至少添加一个图片素材')
      activeTab.value = 'images'
      return false
    }

    // 检查生成数量是否已填写且大于0
    if (
      formData.otherSettings.generateCount === null ||
      formData.otherSettings.generateCount === undefined ||
      formData.otherSettings.generateCount <= 0
    ) {
      ElMessage.warning('请填写有效的生成数量 (必须大于0)')
      activeTab.value = 'other'
      return false
    }

    // 检查图文描述是否已填写
    if (!formData.otherSettings.imageTextDescription || formData.otherSettings.imageTextDescription.trim() === '') {
      ElMessage.warning('请填写图文描述（必填）')
      activeTab.value = 'other'
      return false
    }

    // 检查是否选择了图文分类
    if (selectedCategories.value.length === 0) {
      ElMessage.warning('请选择图文分类')
      activeTab.value = 'other'
      return false
    }

    // 检查图片数量设置
    if (
      formData.otherSettings.minImageCount < 1 ||
      formData.otherSettings.maxImageCount < formData.otherSettings.minImageCount
    ) {
      ElMessage.warning('图片数量设置错误，最小数量不能小于1，最大数量不能小于最小数量')
      activeTab.value = 'other'
      return false
    }

    return true
  }

  // 构建提交参数
  const buildSubmitParams = () => {
    // 使用标题列表中的第一个标题作为任务名称
    const mainTitleText =
      formData.titleSettings && formData.titleSettings.length > 0 && formData.titleSettings[0].content
        ? formData.titleSettings[0].content.trim()
        : ''

    // 从话题设置中提取话题数据
    let topicData = ''
    if (formData.topicSettings && formData.topicSettings.selectionMode !== 'none') {
      const allTopics = []

      // 从每个分组中提取话题
      if (formData.topicSettings.finalTopicGroups && Array.isArray(formData.topicSettings.finalTopicGroups)) {
        formData.topicSettings.finalTopicGroups.forEach((group) => {
          if (Array.isArray(group)) {
            allTopics.push(...group)
          }
        })
      }

      // 去重并过滤有效话题
      const uniqueTopics = [...new Set(allTopics)].filter((topic) => topic && topic.trim())

      // 如果有话题，转换为JSON字符串
      if (uniqueTopics.length > 0) {
        topicData = JSON.stringify(uniqueTopics)
      }
    }

    const params = {
      taskName: mainTitleText ? mainTitleText : '批量图文-' + new Date().toLocaleString(),
      imageMaterials: formData.imageMaterials.filter((m) => m.mediaId || m.mediaUrl),
      titleSettings: formData.titleSettings || [],
      backgroundMusics: formData.backgroundMusics || [],
      stickerSettings: formData.stickerSettings || [],
      pseudoOriginalSettings: formData.pseudoOriginalSettings,
      generateCount: formData.otherSettings.generateCount || 1,
      minImageCount: formData.otherSettings.minImageCount || 3,
      maxImageCount: formData.otherSettings.maxImageCount || 9,
      titleApplyMode: formData.otherSettings.titleApplyMode || 1,
      stickerApplyMode: formData.otherSettings.stickerApplyMode || 1,
      categoryIds: selectedCategories.value,
      imageTextDescription: formData.otherSettings.imageTextDescription || '',
      topic: topicData, // 使用从topicSettings中提取的话题数据
      topicSettings: formData.topicSettings || {}
    }

    return params
  }

  const currentActiveTitleIndex = ref(-1)

  const handleActiveTitleChanged = (newIndex) => {
    currentActiveTitleIndex.value = newIndex
  }

  // 新添加的方法，用于安全处理标题更新
  const handleTitlesUpdate = (newTitles) => {
    if (!Array.isArray(newTitles)) {
      console.error('接收到无效的标题数据')
      return
    }

    formData.titleSettings = newTitles
  }

  // 处理分组变化
  const handleGroupsChanged = (groupsCount) => {
    // 如果有多个分组，将最小/最大图片数量设为分组数
    if (groupsCount > 1) {
      formData.otherSettings.minImageCount = groupsCount
      formData.otherSettings.maxImageCount = groupsCount
    }
  }

  // 计算是否为多分组模式
  const isMultipleGroups = computed(() => {
    return formData.imageMaterials.length > 1
  })
</script>

<style scoped>
  .auto-imagetext-container {
    max-width: 1600px;
    margin: 0 auto;
  }

  .auto-imagetext-card {
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .main-content {
    margin-top: 20px;
  }

  .form-actions {
    margin-top: 30px;
    text-align: center;
    padding: 10px 0;
  }

  .submit-btn {
    padding: 12px 36px;
    font-size: 16px;
    background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
    border: none;
    transition: all 0.3s;
  }

  .submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(75, 108, 183, 0.4);
  }

  .auto-imagetext-form {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }

  .main-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }

  .config-tabs {
    flex-grow: 1;
  }

  .page-layout {
    display: flex;
    gap: 20px;
  }

  .main-config-area {
    flex: 3;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }

  .preview-area-container {
    flex: 2;
    min-width: 0;
    max-width: 30%;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f9f9f9;
    position: sticky;
    top: 20px;
    align-self: flex-start;
    max-height: calc(100vh - 90px);
    overflow-y: auto;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .auto-imagetext-form {
      padding: 15px;
    }
  }
</style>
