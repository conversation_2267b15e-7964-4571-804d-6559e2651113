<template>
  <div v-loading.fullscreen.lock="fullscreenLoading">
    <div class="flex flex-col md:flex-row gap-6 p-4">
      <!-- 左侧分类树 -->
      <CategoryTree
        :selected-category-id="search.categoryId"
        @node-click="handleNodeClick"
        @category-updated="onCategoryUpdated"
        ref="categoryTreeRef"
      />

      <!-- 右侧内容区 -->
      <div class="flex-1 bg-white text-slate-700 dark:text-slate-400 dark:bg-slate-900 rounded-lg shadow-sm">
        <div class="gva-table-box mt-0 mb-0 p-4">
          <warning-bar :title="'账号授权请先选择分类再扫码：抖音--我--右上角菜单--我的二维码--扫一扫'" class="mb-4" />

          <!-- 搜索和操作区域 -->
          <AccountFilter
            v-model="searchForm"
            @submit="onSubmit"
            @show-auth-dialog="showSearchUniqueIdDialog"
            @refresh-all="checkAllUsersStatus"
            @filter-invalid-login="getInValidLoginUsers"
            @export-uid="exportAllUids"
          />

          <!-- 表格区域 -->
          <AccountTable
            ref="accountTableRef"
            :table-data="tableData"
            :page="page"
            :page-size="pageSize"
            :total="total"
            :video-category-map="videoCategoryMap"
            :get-category-name="getCategoryName"
            :get-category-exists="getCategoryExists"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            @update-category="handleUpdateCategory"
            @check-login-status="checkLoginStatus"
            @show-bind-info="showBindInfoDialog"
            @real-name-action="handleRealNameAction"
            @phone-action="handlePhoneAction"
            @show-remark="showRemarkDialog"
            @move-up="moveUp"
            @move-down="moveDown"
            @show-auto-publish="showAutoPublishDialog"
            @show-content-setting="showContentSettingDialog"
            @single-flush="singleFlush"
            @view-awemes="viewAwemes"
            @show-user-detail="showUserDetailDialog"
            @show-transfer="showTransferDialog"
            @disable-product="handleDisableProduct"
            @product-action="handleProductAction"
            @delete-user="deleteUserFunc"
            @save-sort="handleSaveSort"
          />
        </div>
      </div>
    </div>

    <!-- 账号授权弹窗组件 -->
    <AccountAuthDialog
      v-model:searchVisible="searchUniqueIdDialogVisible"
      v-model:authVisible="getQrCodeDialogVisible"
      :categories="categories"
      :user-info="currentAuthUser"
      @auth-success="handleAuthSuccess"
      @clear-user-info="currentAuthUser = null"
    />

    <!-- 实名弹窗 -->
    <RealNameDialog
      v-model="bindRealNameDialogVisible"
      :user-data="currentRealNameUser"
      @success="handleRealNameSuccess"
    />

    <!-- 绑定手机号弹窗 -->
    <PhoneBindDialog v-model="bindPhoneDialogVisible" :user-data="currentPhoneUser" @success="handlePhoneBindSuccess" />

    <!-- 自动发布设置弹窗 -->
    <AutoPublishDialog
      v-model:visible="autoPublishDialogVisible"
      :user="currentPublishUser"
      :video-categories="videoCategories"
      @success="handleAutoPublishSuccess"
    />

    <!-- 营销设定弹窗 -->
    <MarketingSettingDialog
      v-model:visible="contentSettingDialogVisible"
      :user-data="contentSettingUser"
      @success="handleMarketingSettingSuccess"
    />

    <!-- 更新分类弹窗 -->
    <el-dialog v-model="updateCategoryDialogVisible" title="更新分类" width="400px" center draggable>
      <el-form :model="categoryFormData">
        <el-form-item label="选择分类">
          <el-cascader
            v-model="categoryFormData.categoryId"
            :options="categories"
            :props="{
              children: 'children',
              label: 'name',
              value: 'ID',
              checkStrictly: true,
              emitPath: false
            }"
            placeholder="请选择分类"
            style="width: 100%"
            clearable
            filterable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="updateCategoryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmUpdateCategory">确定</el-button>
      </template>
    </el-dialog>

    <!-- 绑定信息弹窗组件 -->
    <BindInfoDialog
      v-model:visible="bindInfoDialogVisible"
      :bind-info="currentBindInfo"
      @check-ip-health="checkUserIPHealth"
      @refresh-data="getTableData"
    />

    <!-- 添加备注弹窗 -->
    <el-dialog v-model="remarkDialogVisible" title="账号备注" width="400px" center draggable>
      <div class="p-4">
        <el-form :model="remarkForm" label-width="80px">
          <el-form-item label="账号">
            <span>{{ remarkForm.nickname }}</span>
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="remarkForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="remarkDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmUpdateRemark">确定</el-button>
      </template>
    </el-dialog>

    <!-- 用户详情弹窗 -->
    <UserDetailDialog
      v-model:visible="userDetailDialogVisible"
      :user-detail="currentUserDetail"
      :get-category-name="getCategoryName"
      @check-ip-health="checkUserIPHealth"
    />

    <!-- 作品管理弹窗 -->
    <el-dialog
      v-model="awemeDialogVisible"
      :title="`${selectedUser?.nickname || ''} - 作品管理`"
      width="95%"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      top="2vh"
      class="aweme-dialog"
    >
      <div v-if="selectedUser" class="aweme-content">
        <DyAwemeManager :initial-dy-user-ids="[selectedUser.ID]" />
      </div>
    </el-dialog>

    <!-- 账号转移弹窗 -->
    <TransferDialog v-model="transferDialogVisible" :user-data="transferUserData" @success="handleTransferSuccess" />
  </div>
</template>

<script setup>
  import { ref, nextTick, onMounted, onUnmounted, watch } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    getUserList,
    deleteUser,
    toggleProductEnabled as toggleProductEnabledAPI,
    updateCategory,
    updateUserRemark,
    saveUserSort
  } from '@/api/douyin/dyUser'
  import { getProductFeed } from '@/api/douyin/dyProduct'
  import { checkSingleIP } from '@/api/douyin/ip'
  import { getUserLoginInfoForMore, checkAllUserStatusForMore } from '@/api/douyin/douyinForMore'
  import { getVideoCategoryList } from '@/api/creative/videoCategory'

  import WarningBar from '@/components/warningBar/warningBar.vue'
  import DyAwemeManager from '@/components/douyin/DyAwemeManager.vue'
  import CategoryTree from '@/components/douyin/accountList/CategoryTree.vue'
  import AccountFilter from '@/components/douyin/accountList/AccountFilter.vue'
  import AccountTable from '@/components/douyin/accountList/AccountTable.vue'
  import AccountAuthDialog from '@/components/douyin/accountList/AccountAuthDialog.vue'
  import BindInfoDialog from '@/components/douyin/accountList/BindInfoDialog.vue'
  import RealNameDialog from '@/components/douyin/accountList/RealNameDialog.vue'
  import PhoneBindDialog from '@/components/douyin/accountList/PhoneBindDialog.vue'
  import AutoPublishDialog from '@/components/douyin/accountList/AutoPublishDialog.vue'
  import UserDetailDialog from '@/components/douyin/accountList/UserDetailDialog.vue'
  import MarketingSettingDialog from '@/components/douyin/accountList/MarketingSettingDialog.vue'
  import TransferDialog from '@/components/douyin/accountList/TransferDialog.vue'

  defineOptions({
    name: 'AccountList'
  })

  const page = ref(1)
  const total = ref(0)
  const pageSize = ref(100)
  const search = ref({
    nickname: null,
    uniqueId: null,
    categoryId: 0,
    invalidLogin: null
  })

  // 筛选表单数据（用于组件通信）
  const searchForm = ref({
    nickname: null,
    uniqueId: null
  })
  const tableData = ref([])
  const accountTableRef = ref(null)

  // 分类相关数据
  const categories = ref([])

  // 分页
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }

  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }

  const onSubmit = () => {
    // 同步筛选数据
    search.value.nickname = searchForm.value.nickname
    search.value.uniqueId = searchForm.value.uniqueId
    page.value = 1
    // 主动查询时清空筛选未登录状态
    search.value.invalidLogin = null
    getTableData()
  }

  const getInValidLoginUsers = () => {
    // 清空搜索条件，只筛选未登录用户
    search.value.nickname = null
    search.value.uniqueId = null
    searchForm.value.nickname = null
    searchForm.value.uniqueId = null
    search.value.invalidLogin = 1
    page.value = 1
    getTableData()
  }

  const videoCategoryList = ref([])
  const videoCategoryMap = ref({})
  const videoCategories = ref([])

  const fetchVideoCategoryList = async () => {
    const res = await getVideoCategoryList({
      page: 1,
      pageSize: 100
    })
    if (res.code === 0) {
      const disableCategories = (categories) => {
        return categories.map((category) => {
          const newCategory = { ...category }
          if (newCategory.status === 2) {
            newCategory.disabled = true
          }
          if (newCategory.children && newCategory.children.length > 0) {
            newCategory.children = disableCategories(newCategory.children)
          }
          return newCategory
        })
      }
      const processedList = disableCategories(res.data.list || [])
      videoCategoryList.value = processedList
      videoCategories.value = processedList // Restore assignment

      const newMap = {}
      const buildMap = (categories) => {
        for (const category of categories) {
          newMap[category.ID] = category.name
          if (category.children && category.children.length > 0) {
            buildMap(category.children)
          }
        }
      }
      buildMap(processedList)
      videoCategoryMap.value = newMap
    } else {
      ElMessage.error('获取视频库分类失败：', res.msg)
    }
  }

  // 查询
  const getTableData = async () => {
    // 显示视频类库
    await fetchVideoCategoryList()

    const table = await getUserList({
      page: page.value,
      pageSize: pageSize.value,
      ...search.value
    })
    if (table.code === 0) {
      tableData.value = table.data.list
      total.value = table.data.total
      page.value = table.data.page
      pageSize.value = table.data.pageSize
    }

    // 在下一个时钟周期通知子组件初始化拖拽排序
    await nextTick()
    if (accountTableRef.value) {
      accountTableRef.value.initSortable()
    }
  }

  // 初始化数据
  getTableData()

  // 处理排序保存事件
  const handleSaveSort = async ({ oldIndex, newIndex }) => {
    // 更新本地数据
    const movedItem = tableData.value.splice(oldIndex, 1)[0]
    tableData.value.splice(newIndex, 0, movedItem)

    // 保存排序到后端
    await saveSort()
  }

  // 保存排序
  const saveSort = async () => {
    try {
      const userIds = tableData.value.map((item) => item.ID)
      const params = search.value.categoryId ? { categoryId: search.value.categoryId } : {}

      await saveUserSort({ userIds }, params)
      ElMessage.success('排序保存成功')
    } catch (error) {
      console.error('保存排序失败:', error)
      ElMessage.error('保存排序失败')
      // 重新获取数据，恢复原始顺序
      getTableData()
    }
  }

  // 上移功能
  const moveUp = async (index) => {
    if (index === 0) return

    // 交换数组中的位置
    const item = tableData.value[index]
    tableData.value.splice(index, 1)
    tableData.value.splice(index - 1, 0, item)

    // 保存排序
    await saveSort()
  }

  // 下移功能
  const moveDown = async (index) => {
    if (index === tableData.value.length - 1) return

    // 交换数组中的位置
    const item = tableData.value[index]
    tableData.value.splice(index, 1)
    tableData.value.splice(index + 1, 0, item)

    // 保存排序
    await saveSort()
  }

  const deleteUserFunc = async (row) => {
    ElMessageBox.confirm('此操作将永久删除该用户, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        const res = await deleteUser(row)
        if (res.code === 0) {
          ElMessage({
            type: 'success',
            message: '删除成功!'
          })
          if (tableData.value.length === 1 && page.value > 1) {
            page.value--
          }
          getTableData()
        }
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '已取消删除'
        })
      })
  }

  // 分类树组件引用
  const categoryTreeRef = ref(null)
  const flattenCategories = ref([])

  const flattenCategoryTree = (categories) => {
    const result = []
    const flatten = (items, prefix = '') => {
      items.forEach((item) => {
        if (item.ID !== 0) {
          // 排除"全部分类"
          result.push({
            ID: item.ID,
            name: prefix + item.name
          })
        }
        if (item.children && item.children.length > 0) {
          flatten(item.children, prefix + item.name + ' / ')
        }
      })
    }
    flatten(categories)
    return result
  }

  const handleNodeClick = (node) => {
    // 清空搜索条件
    search.value.nickname = null
    search.value.uniqueId = null
    searchForm.value.nickname = null
    searchForm.value.uniqueId = null
    search.value.categoryId = node.ID
    search.value.invalidLogin = null // 切换分类时清空筛选未登录状态
    page.value = 1
    getTableData()
  }

  const onCategoryUpdated = () => {
    // 当分类更新时，同步CategoryTree组件的分类数据到弹窗中
    if (categoryTreeRef.value && categoryTreeRef.value.categories) {
      categories.value = categoryTreeRef.value.categories
      flattenCategories.value = flattenCategoryTree(categoryTreeRef.value.categories)
    }
  }

  const searchUniqueIdDialogVisible = ref(false)
  const getQrCodeDialogVisible = ref(false)
  const fullscreenLoading = ref(false)
  const currentAuthUser = ref(null)

  // 处理授权成功事件
  const handleAuthSuccess = () => {
    // 清空当前授权用户信息
    currentAuthUser.value = null
    getTableData()
  }

  const showSearchUniqueIdDialog = async () => {
    searchUniqueIdDialogVisible.value = true
  }

  // 直接显示授权弹窗（用于已有用户重新授权）
  const showAuthDialogDirectly = async (row) => {
    console.log('准备为用户重新授权:', row)

    // 设置当前授权用户信息
    currentAuthUser.value = {
      uniqueId: row.uniqueId,
      accountType: row.accountType,
      categoryId: row.categoryId,
      bindIP: row.bindIP,
      nickname: row.nickname
    }

    // 打开授权弹窗，AccountAuthDialog组件会自动处理用户信息
    getQrCodeDialogVisible.value = true
  }

  const directProductSearch = async (row) => {
    try {
      // 添加确认对话框
      ElMessageBox.confirm('确定要为该账号发送选品请求吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      })
        .then(() => {
          // 用户确认后发送请求，禁用全局loading
          getProductFeed(
            {
              token: row.imToken,
              page: 1,
              pageSize: 100
            },
            {
              donNotShowLoading: true // 禁用全局loading
            }
          )
            .then((res) => {
              if (res.code === 0) {
                // 使用接口返回的提示信息
                ElMessage.success(res.msg || '选品请求已发送，请稍后在【选品中心】-【商品列表】下查看结果')
                console.log('选品请求已发送')
              }
            })
            .catch((err) => {
              console.error('选品数据获取失败:', err.message)
              ElMessage.error('发送选品请求失败：' + err.message)
            })
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '已取消选品请求'
          })
        })
    } catch (err) {
      ElMessage.error('发送选品请求失败：' + err.message)
    }
  }

  // 合并选品按钮功能
  const handleProductAction = async (row) => {
    if (!row.isProductEnabled) {
      // 如果选品未开启，先开启选品
      try {
        // 更新状态
        row.isProductEnabled = true

        const res = await toggleProductEnabledAPI({
          id: row.ID,
          enabled: true
        })

        if (res.code === 0) {
          ElMessage.success(res.msg || '选品已开启')
          // 开启成功后，立即执行选品操作
          directProductSearch(row)
          getTableData() // 刷新列表
        } else {
          // 如果API调用失败，恢复状态
          row.isProductEnabled = false
          ElMessage.error('开启选品失败')
        }
      } catch (err) {
        console.error('更新选品状态失败:', err.message)
        ElMessage.error('更新选品状态失败：' + err.message)
        // 恢复开关状态
        row.isProductEnabled = false
      }
    } else {
      // 如果选品已开启，直接执行选品操作
      directProductSearch(row)
    }
  }

  // 实名
  const handleRealNameAction = (row) => {
    currentRealNameUser.value = { ...row }
    bindRealNameDialogVisible.value = true
  }

  // 绑定手机号：handlePhoneAction
  const handlePhoneAction = (row) => {
    currentPhoneUser.value = { ...row }
    bindPhoneDialogVisible.value = true
  }

  // 实名认证成功回调
  const handleRealNameSuccess = () => {
    getTableData()
  }

  // 绑定手机号成功回调
  const handlePhoneBindSuccess = () => {
    getTableData()
  }

  // 处理关闭选品
  const handleDisableProduct = async (row) => {
    // 添加确认对话框
    ElMessageBox.confirm('确定要关闭选品功能吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          // 更新状态
          row.isProductEnabled = false

          const res = await toggleProductEnabledAPI({
            id: row.ID,
            enabled: false
          })

          if (res.code === 0) {
            ElMessage.success(res.msg || '选品已关闭')
            getTableData() // 刷新列表
          } else {
            // 如果API调用失败，恢复状态
            row.isProductEnabled = true
            ElMessage.error('关闭选品失败')
          }
        } catch (err) {
          console.error('更新选品状态失败:', err.message)
          ElMessage.error('更新选品状态失败：' + err.message)
          // 恢复开关状态
          row.isProductEnabled = true
        }
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '已取消操作'
        })
      })
  }

  // 实名相关
  const bindRealNameDialogVisible = ref(false)
  const currentRealNameUser = ref({})

  // 绑定设备相关
  const bindPhoneDialogVisible = ref(false)
  const currentPhoneUser = ref({})

  const getLoginInfo = async (row, loginForce = 0, chatForce = 0) => {
    const req = { uniqueId: row.uniqueId, loginForce: loginForce, chatForce: chatForce }
    try {
      return await getUserLoginInfoForMore(req)
    } catch (err) {
      throw new Error(`getLoginInfo异常：uniqueId:${row.uniqueId},err:${err.message}`)
    }
  }

  const checkLoginStatus = async (row) => {
    try {
      const res = await getLoginInfo(row, 1, 0)
      if (res.msg != '成功') {
        ElMessage.warning('用户登录失效，正在重新获取登录二维码...')
        if (res.data && res.data.ID) {
          row.status = res.data.status
        } else {
          row.status = 0
        }
        // 直接跳转到二维码弹窗，无需经过搜索弹窗
        await showAuthDialogDirectly(row)
      } else {
        ElMessage.success('刷新成功')
        row.awemeCount = res.data.awemeCount
        row.followerCount = res.data.followerCount
        row.likeCount = res.data.likeCount
        row.status = res.data.status
      }
    } catch (err) {
      console.error('检查登录状态失败:', err.message)
      ElMessage.error('检查登录状态失败：' + err.message)
    }
  }

  const singleFlush = async (row) => {
    try {
      const res = await getLoginInfo(row, 1, 1)
      if (res.msg != '成功') {
        ElMessage.error('刷新失败：' + res.msg)
        if (res.data && res.data.ID) {
          row.status = res.data.status
        } else {
          row.status = 0
        }
      } else {
        ElMessage.success('刷新成功')
        getTableData()
        return
      }
    } catch (err) {
      console.error('检查登录状态失败:', err.message)
      ElMessage.error('检查登录状态失败：' + err.message)
    }
  }

  // 检查所有用户状态
  const checkAllUsersStatus = async () => {
    try {
      ElMessage.warning('刷新全部可能需要较长时间，请稍后...')
      const res = await checkAllUserStatusForMore({ categoryId: search.value.categoryId })
      console.log('checkAllUserStatusForMore:', res)
      if (res.code != 0) {
        ElMessage.error('刷新失败：' + res.msg)
      }
      ElMessage.success('刷新完成')
      getTableData()
    } catch (error) {
      console.error('刷新失败:', error)
      ElMessage.error('刷新失败')
    }
  }

  // 分类数据通过CategoryTree组件管理，移除直接调用

  // 自动发布相关
  const autoPublishDialogVisible = ref(false)
  const currentPublishUser = ref(null)

  // 显示自动发布弹窗
  const showAutoPublishDialog = (row) => {
    currentPublishUser.value = row
    autoPublishDialogVisible.value = true
  }

  // 自动发布成功回调
  const handleAutoPublishSuccess = () => {
    getTableData()
  }

  const contentSettingDialogVisible = ref(false)
  const contentSettingUser = ref({})

  // 发布模板相关

  const showContentSettingDialog = (row) => {
    contentSettingUser.value = { ...row }
    contentSettingDialogVisible.value = true
  }

  const handleMarketingSettingSuccess = () => {
    getTableData()
  }

  const getCategoryName = (categoryId) => {
    const findCategory = (list) => {
      for (const item of list) {
        if (item.ID === categoryId) {
          return item.name
        }
        if (item.children && item.children.length > 0) {
          const found = findCategory(item.children)
          if (found) return found
        }
      }
      return null
    }
    const categories = categoryTreeRef.value?.categories || []
    const name = findCategory(categories)
    if (name) {
      return name
    }
    // 如果在当前分类中找不到，说明可能是已删除的分类，只显示分类ID
    return `分类(${categoryId})`
  }

  const updateCategoryDialogVisible = ref(false)
  const categoryFormData = ref({
    ID: 0,
    categoryId: 0
  })

  const handleUpdateCategory = (row) => {
    // 确保分类数据是最新的
    if (categoryTreeRef.value && categoryTreeRef.value.categories) {
      categories.value = categoryTreeRef.value.categories
    }

    categoryFormData.value = {
      ID: row.ID,
      categoryId: row.categoryId
    }
    updateCategoryDialogVisible.value = true
  }

  const confirmUpdateCategory = async () => {
    try {
      const res = await updateCategory({
        id: categoryFormData.value.ID,
        categoryId: categoryFormData.value.categoryId
      })
      if (res.code === 0) {
        ElMessage.success('更新分类成功')
        updateCategoryDialogVisible.value = false
        getTableData()
      }
    } catch (err) {
      ElMessage.error('更新分类失败：' + err.message)
    }
  }

  const getCategoryExists = (categoryId) => {
    const findCategory = (list) => {
      for (const item of list) {
        if (item.ID === categoryId) {
          return true
        }
        if (item.children && item.children.length > 0) {
          const found = findCategory(item.children)
          if (found) return true
        }
      }
      return false
    }
    const categories = categoryTreeRef.value?.categories || []
    return findCategory(categories)
  }

  // 绑定信息弹窗
  const bindInfoDialogVisible = ref(false)
  const currentBindInfo = ref({
    bindIP: '',
    did: '',
    iid: '',
    ID: null,
    nickname: '',
    ipSort: null,
    ipId: null
  })

  const showBindInfoDialog = async (row) => {
    currentBindInfo.value = { ...row }
    bindInfoDialogVisible.value = true
  }

  // 备注相关
  const remarkDialogVisible = ref(false)
  const remarkForm = ref({
    id: null,
    nickname: '',
    remark: ''
  })

  const showRemarkDialog = (row) => {
    remarkForm.value = {
      id: row.ID,
      nickname: row.nickname,
      remark: row.remark || ''
    }
    remarkDialogVisible.value = true
  }

  const confirmUpdateRemark = async () => {
    try {
      const res = await updateUserRemark({
        id: remarkForm.value.id,
        remark: remarkForm.value.remark
      })

      if (res.code === 0) {
        ElMessage.success('更新备注成功')
        remarkDialogVisible.value = false
        getTableData() // 刷新列表
      }
    } catch (err) {
      console.error('更新备注失败:', err.message)
      ElMessage.error('更新备注失败：' + err.message)
    }
  }

  // 监听CategoryTree组件的分类数据变化
  watch(
    () => categoryTreeRef.value?.categories,
    (newCategories) => {
      if (newCategories) {
        categories.value = newCategories
        flattenCategories.value = flattenCategoryTree(newCategories)
      }
    },
    { immediate: true }
  )

  // 组件挂载时的初始化
  onMounted(() => {
    // 确保在组件挂载时分类数据能正确初始化
    nextTick(() => {
      if (categoryTreeRef.value && categoryTreeRef.value.categories) {
        categories.value = categoryTreeRef.value.categories
        flattenCategories.value = flattenCategoryTree(categoryTreeRef.value.categories)
      }
    })
  })

  // 组件卸载时的清理已移至子组件
  onUnmounted(() => {
    // 清理逻辑已移至子组件
  })

  // 用户详情相关
  const userDetailDialogVisible = ref(false)
  const currentUserDetail = ref({})

  // 作品管理弹窗相关变量
  const awemeDialogVisible = ref(false)
  const selectedUser = ref(null)

  // 账号转移相关变量
  const transferDialogVisible = ref(false)
  const transferUserData = ref({})

  const showUserDetailDialog = (row) => {
    currentUserDetail.value = { ...row }
    userDetailDialogVisible.value = true
  }

  // 检查用户绑定IP的健康状态
  const checkUserIPHealth = async (user) => {
    if (!user.bindIP) {
      ElMessage.warning('该用户未绑定代理IP')
      return
    }

    try {
      const res = await checkSingleIP({ ip: user.bindIP })
      if (res.code === 0) {
        const result = res.data
        if (result.isHealthy) {
          ElMessage.success(
            `用户 ${user.nickname} 的代理IP ${user.bindIP} 健康检查通过，响应时间: ${result.responseTime}ms`
          )
        } else {
          ElMessage.warning(`用户 ${user.nickname} 的代理IP ${user.bindIP} 健康检查失败: ${result.errorMessage}`)
        }
        // 刷新用户列表以显示最新状态
        getTableData()
      }
    } catch (err) {
      console.error('检查IP健康状态失败:', err)
      ElMessage.error('检查IP健康状态失败')
    }
  }

  const viewAwemes = (row) => {
    console.log('查看待发布视频', row.ID)
    // 设置当前选中的用户信息
    selectedUser.value = row
    awemeDialogVisible.value = true
  }

  // 显示账号转移弹窗
  const showTransferDialog = (row) => {
    transferUserData.value = { ...row }
    transferDialogVisible.value = true
  }

  // 转移成功回调
  const handleTransferSuccess = () => {
    getTableData()
  }

  // 导出全部用户UID
  const exportAllUids = async () => {
    try {
      ElMessage.info('正在导出UID数据，请稍候...')

      // 获取全部用户数据（不分页）
      const response = await getUserList({
        page: 1,
        pageSize: 10000, // 设置较大的分页数以获取全部数据
        ...search.value
      })

      if (response.code === 0 && response.data.list) {
        const users = response.data.list

        // 提取UID数据，过滤掉空的UID
        const uids = users.filter((user) => user.uid && user.uid.trim() !== '').map((user) => user.uid)

        if (uids.length === 0) {
          ElMessage.warning('没有找到有效的UID数据')
          return
        }

        // 生成CSV内容
        const csvContent = 'UID\n' + uids.join('\n')

        // 创建并下载文件
        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', `用户UID导出_${new Date().toISOString().slice(0, 10)}.csv`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        ElMessage.success(`成功导出 ${uids.length} 个用户UID`)
      } else {
        ElMessage.error('获取用户数据失败')
      }
    } catch (error) {
      console.error('导出UID失败:', error)
      ElMessage.error('导出UID失败：' + error.message)
    }
  }
</script>
<style scoped>
  /* 作品管理弹窗样式 */
  .aweme-dialog .el-dialog__body {
    padding: 10px;
    max-height: 85vh;
    overflow-y: auto;
  }

  .aweme-content {
    width: 100%;
    height: 100%;
  }
</style>
