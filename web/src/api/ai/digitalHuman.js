import service from '@/utils/request'

// createDigitalHumanTask 创建数字人视频任务
export const createDigitalHumanTask = (data) => {
  return service({
    url: '/ai/digital-human/create',
    method: 'post',
    data
  })
}

// getDigitalHumanTaskList 获取数字人视频任务列表
export const getDigitalHumanTaskList = (params) => {
  return service({
    url: '/ai/digital-human/list',
    method: 'get',
    params
  })
}

// getDigitalHumanTaskStatus 获取数字人视频任务状态
export const getDigitalHumanTaskStatus = (taskId) => {
  return service({
    url: `/ai/digital-human/status/${taskId}`,
    method: 'get'
  })
}

// createHeygemDigitalHumanTask 创建HeyGem数字人视频任务
export const createHeygemDigitalHumanTask = (data) => {
  return service({
    url: '/ai/digital-human/heygem/create',
    method: 'post',
    data
  })
}

// getHeygemTaskList 获取HeyGem任务列表
export const getHeygemTaskList = (params) => {
  return service({
    url: '/ai/digital-human/heygem/list',
    method: 'get',
    params
  })
}

// getHeygemTaskStatus 获取HeyGem任务状态
export const getHeygemTaskStatus = (taskId) => {
  return service({
    url: `/ai/digital-human/heygem/status/${taskId}`,
    method: 'get'
  })
}