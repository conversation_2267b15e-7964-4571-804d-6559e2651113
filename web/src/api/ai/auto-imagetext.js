import service from '@/utils/request'

// 提交批量图文生成任务
export const submitAutoImageTextTask = (data) => {
  return service({
    url: '/ai/imagetext/auto/submit',
    method: 'post',
    data
  })
}

// 获取任务状态
export const getAutoImageTextTaskStatus = (taskId) => {
  return service({
    url: `/ai/imagetext/auto/status/${taskId}`,
    method: 'get'
  })
}

// 获取任务列表
export const getAutoImageTextTaskList = (params) => {
  return service({
    url: '/ai/imagetext/auto/list',
    method: 'get',
    params
  })
}

// 删除任务
export const deleteAutoImageTextTask = (data) => {
  return service({
    url: '/ai/imagetext/auto/delete',
    method: 'delete',
    data
  })
} 