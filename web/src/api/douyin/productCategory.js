import service from '@/utils/request'

// 创建商品分类
export const createProductCategory = (data) => {
  return service({
    url: '/douyin/product-category',
    method: 'post',
    data
  })
}

// 更新商品分类
export const updateProductCategory = (id, data) => {
  return service({
    url: `/douyin/product-category/${id}`,
    method: 'put',
    data
  })
}

// 获取商品分类列表
export const getProductCategoryList = (params) => {
  return service({
    url: '/douyin/product-category',
    method: 'get',
    params
  })
}

// 根据ID获取商品分类详情
export const getProductCategoryById = (id) => {
  return service({
    url: `/douyin/product-category/${id}`,
    method: 'get'
  })
}

// 删除商品分类
export const deleteProductCategory = (id) => {
  return service({
    url: `/douyin/product-category/${id}`,
    method: 'delete'
  })
}

// 更新分类状态
export const updateCategoryStatus = (data) => {
  return service({
    url: '/douyin/product-category/status',
    method: 'put',
    data
  })
}

// 获取所有启用的分类
export const getAllCategories = () => {
  return service({
    url: '/douyin/product-category/all',
    method: 'get'
  })
}

// 更新分类排序
export const updateCategorySort = (data) => {
  return service({
    url: '/douyin/product-category/sort',
    method: 'put',
    data
  })
} 