import service from '@/utils/request'

// 添加收藏音色
export const addFavoriteVoice = (data) => {
  return service({
    url: '/ai/minimax-voice-favorite/add',
    method: 'post',
    data
  })
}

// 删除收藏音色
export const removeFavoriteVoice = (data) => {
  return service({
    url: '/ai/minimax-voice-favorite/remove',
    method: 'post',
    data
  })
}

// 获取收藏音色列表
export const getFavoriteVoices = () => {
  return service({
    url: '/ai/minimax-voice-favorite/list',
    method: 'get'
  })
} 