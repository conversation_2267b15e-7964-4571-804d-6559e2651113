# FFmpeg 与 GoImaging 使用场景总结

## 处理器选择策略概览

系统采用**智能处理器工厂**模式，根据需求和环境自动选择最适合的图像处理器。

## FFmpeg 处理器

### 主要优势 ✅

- **高性能**: C 语言编写，处理速度快
- **功能全面**: 强大的视频和图像处理能力
- **贴纸支持**: 优秀的图像叠加（overlay）功能
- **滤镜丰富**: 支持复杂的图像效果和变换
- **成熟稳定**: 广泛使用的开源项目

### 使用场景

1. **FFmpeg 支持 drawtext 且需要文字**: 最优选择
2. **只需要贴纸处理**: 首选 FFmpeg
3. **需要复杂图像效果**: FFmpeg 独有
4. **高性能要求**: 大批量处理时优先

### 限制条件 ❌

- **依赖环境**: 需要系统安装 FFmpeg
- **drawtext 滤镜**: 某些 FFmpeg 版本可能不支持
- **部署复杂**: 需要确保 FFmpeg 正确安装

## GoImaging 处理器

### 主要优势 ✅

- **纯 Go 实现**: 无外部依赖，部署简单
- **兼容性强**: 跨平台运行，无需额外安装
- **文字渲染**: 完整的字体支持和颜色控制
- **可控性高**: 精确的位置和样式控制
- **调试友好**: 详细的 Go 语言错误信息

### 使用场景

1. **FFmpeg 不支持 drawtext 时**: 文字处理的备选方案
2. **部署要求简单**: 无法安装 FFmpeg 的环境
3. **文字处理专用**: 只需要文字而不需要贴纸
4. **调试和开发**: Go 语言环境下更容易调试

### 限制条件 ❌

- **性能相对较低**: Go 语言性能不如 FFmpeg
- **不支持贴纸**: 需要调用 FFmpeg 处理贴纸
- **功能有限**: 不支持复杂的图像效果

## 智能选择逻辑

### 处理器优先级

```
处理器注册顺序（优先级从高到低）：
1. FFmpeg处理器
2. GoImaging处理器
```

### 自动选择策略

#### 场景 1：只需要文字处理

```
需求: needTitle=true, needSticker=false

选择逻辑:
1. FFmpeg支持drawtext → 选择FFmpeg ✅
2. FFmpeg不支持drawtext → 选择GoImaging ✅
```

#### 场景 2：只需要贴纸处理

```
需求: needTitle=false, needSticker=true

选择逻辑:
1. 始终选择FFmpeg ✅ (GoImaging不支持贴纸)
```

#### 场景 3：需要文字和贴纸

```
需求: needTitle=true, needSticker=true

选择逻辑:
1. FFmpeg支持drawtext → 选择FFmpeg ✅ (完美匹配)
2. FFmpeg不支持drawtext → 选择GoImaging ✅ (文字优先策略)
   - GoImaging处理文字
   - 后续调用FFmpeg处理贴纸
```

#### 场景 4：都不需要

```
需求: needTitle=false, needSticker=false

选择逻辑:
1. 选择第一个可用处理器（FFmpeg）✅
```

## 混合处理模式

### GoImaging + FFmpeg 协作

当选择 GoImaging 但需要贴纸时：

```go
func (p *GoImagingProcessor) ProcessImage(config ProcessConfig) (string, error) {
    // 1. 先用FFmpeg处理贴纸（如果需要）
    if config.NeedSticker {
        ffmpegProcessor := NewFFmpegProcessor()
        intermediateImagePath, err = ffmpegProcessor.ProcessImage(...)
    }

    // 2. 再用GoImaging添加文字
    if config.NeedTitle {
        processedImagePath, err = p.addTextWithGoImaging(...)
    }
}
```

**优势**:

- 发挥每个处理器的长处
- FFmpeg 处理贴纸，GoImaging 处理文字
- 确保功能完整性

## 实际使用示例

### 示例 1：标准环境（FFmpeg 完整支持）

```
环境: FFmpeg已安装，支持drawtext
需求: 文字 + 贴纸

结果: 选择FFmpeg处理器
- 一站式处理文字和贴纸
- 性能最佳
- 功能完整
```

### 示例 2：受限环境（FFmpeg 无 drawtext）

```
环境: FFmpeg已安装，但不支持drawtext
需求: 文字 + 贴纸

结果: 选择GoImaging处理器
- GoImaging处理文字
- FFmpeg处理贴纸
- 确保功能完整性
```

### 示例 3：纯 Go 环境

```
环境: 无FFmpeg，纯Go部署
需求: 只有文字

结果: 选择GoImaging处理器
- 无外部依赖
- 部署简单
- 文字功能完整
```

## 性能对比

### FFmpeg 处理器

- **文字渲染**: 超快 ⚡⚡⚡
- **贴纸处理**: 超快 ⚡⚡⚡
- **混合处理**: 超快 ⚡⚡⚡
- **资源占用**: 中等

### GoImaging 处理器

- **文字渲染**: 快 ⚡⚡
- **贴纸处理**: 不支持（需要调用 FFmpeg）
- **混合处理**: 中等 ⚡
- **资源占用**: 低

## 部署建议

### 生产环境推荐

```
推荐配置:
1. 安装完整的FFmpeg（包含drawtext）
2. 保留GoImaging作为备选
3. 启用智能选择机制

优势:
- 最佳性能
- 功能完整
- 自动降级
```

### 受限环境方案

```
备选配置:
1. 部署轻量级FFmpeg（仅贴纸功能）
2. 主要依赖GoImaging处理文字
3. 混合模式确保功能完整

优势:
- 部署灵活
- 功能保障
- 兼容性强
```

## 监控指标

### 处理器选择统计

- **FFmpeg 使用率**: 监控 FFmpeg 选择频率
- **GoImaging 使用率**: 监控备选方案使用
- **混合处理率**: 监控协作处理频率

### 性能指标

- **处理时间**: 不同处理器的处理速度
- **成功率**: 处理成功率对比
- **错误类型**: 不同处理器的错误分布

## 总结

| 处理器        | 最佳场景             | 性能   | 兼容性   | 部署难度 |
| ------------- | -------------------- | ------ | -------- | -------- |
| **FFmpeg**    | 完整功能、高性能需求 | ⚡⚡⚡ | 需要安装 | 中等     |
| **GoImaging** | 备选方案、简单部署   | ⚡⚡   | 原生支持 | 简单     |

**智能选择策略确保**:

- ✅ **性能优先**: FFmpeg 可用时优先使用
- ✅ **功能保障**: FFmpeg 不可用时 GoImaging 备选
- ✅ **需求导向**: 根据文字/贴纸需求智能选择
- ✅ **渐进降级**: 优雅处理各种环境限制

这种设计确保了系统在任何环境下都能提供完整的图文处理功能！
