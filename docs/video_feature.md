# 视频功能使用说明

## 视频封面自动提取功能

系统新增了视频封面自动提取功能，当用户上传视频时，如果没有指定封面，系统会自动提取视频的第一帧作为封面。

### 依赖要求

此功能依赖于 FFmpeg 工具，需要在服务器上安装 FFmpeg。

#### Ubuntu/Debian 系统安装方法

```bash
sudo apt update
sudo apt install ffmpeg
```

#### CentOS/RHEL 系统安装方法

```bash
sudo yum install epel-release
sudo yum install ffmpeg ffmpeg-devel
```

#### macOS 安装方法

```bash
brew install ffmpeg
```

#### Windows 安装方法

1. 下载 FFmpeg：https://ffmpeg.org/download.html
2. 解压到任意目录，如 `C:\ffmpeg`
3. 将 `C:\ffmpeg\bin` 添加到系统环境变量 PATH 中

### 验证安装

安装完成后，执行以下命令验证是否安装成功：

```bash
ffmpeg -version
```

如果显示版本信息，则表示安装成功。

## 使用说明

1. 上传视频时，如果不上传封面，系统会自动提取视频的第一帧作为封面
2. 如果自动提取失败，视频仍会上传成功，但没有封面
3. 上传成功后，您可以随时通过"更换封面"功能手动设置封面

## 注意事项

1. 视频第一帧可能不是最具代表性的画面，建议为重要视频手动上传封面
2. 自动提取的封面质量取决于视频本身的质量
3. 对于较大视频文件，自动提取封面可能需要更多时间
4. 系统会在临时目录中创建文件，请确保服务器有足够的临时存储空间
5. 如果遇到"文件不存在"的错误，可能是临时目录权限不足或被提前清理，请检查服务器临时目录的权限设置
