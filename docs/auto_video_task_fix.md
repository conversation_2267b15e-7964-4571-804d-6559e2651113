# AutoVideoTask 状态管理问题修复方案

## 问题分析

在一键成片任务状态检查(`AutoVideoStatusTask`)中发现了以下问题：

### 1. 并发执行问题

- 定时任务每分钟执行一次（`'0 */1 * * * *'`）
- 任务执行时间可能超过 1 分钟（下载视频、处理等步骤耗时较长）
- 如果第一次任务还在执行，第二次任务开始时不会重复处理同样的任务（因为状态已经是 3）

### 2. 程序异常退出问题

- 如果程序崩溃、重启或出现 panic，`defer` 函数可能不会执行
- 导致任务永远停留在状态`3`（处理中）

### 3. 没有启动时清理机制

- 程序重启后没有清理之前因异常而停留在"处理中"状态的任务

### 4. 原代码逻辑漏洞

- `todoIds`虽然不会被下一次任务覆盖，但如果程序重启，之前的`defer`函数就永远不会执行

## 解决方案

### 1. 添加互斥锁机制

```go
type AutoVideoStatusTask struct {
    mutex sync.Mutex // 确保同一时间只有一个任务在执行
}
```

### 2. 超时清理机制

```go
const taskExecutionTimeout = 5 * time.Minute

func (s *AutoVideoStatusTask) cleanupTimeoutTasks(taskTitle string) {
    // 清理超过5分钟的处理中任务
}
```

### 3. 程序启动时清理

在 `server/initialize/gorm.go` 中添加启动时清理逻辑：

```go
func cleanupStuckAutoVideoTasks() {
    // 程序启动时将所有处理中状态的任务重置为未处理状态
}
```

### 4. 更安全的状态管理

- 使用 `TryLock()` 防止并发执行
- 记录处理开始时间，用于超时判断
- 更精确的 defer 清理逻辑

## 修改文件列表

1. `server/plugin/systask/service/auto_video_status_task.go` - 主要修改
2. `server/initialize/gorm.go` - 添加启动清理逻辑

## 状态常量说明

```go
const (
    AutoVideoTaskStatusPending    = 0 // 未处理
    AutoVideoTaskStatusSuccess    = 1 // 成功
    AutoVideoTaskStatusFailed     = 2 // 失败
    AutoVideoTaskStatusProcessing = 3 // 处理中
)
```

## 修复效果

1. **防止并发执行**：同一时间只有一个定时任务实例在执行
2. **超时保护**：超过 5 分钟的处理中任务会被重置
3. **启动时清理**：程序重启时自动清理遗留的处理中状态
4. **更好的日志**：详细记录清理过程和结果

## 使用建议

1. 监控日志中的清理信息，了解任务执行情况
2. 如果频繁出现超时清理，考虑优化任务执行效率
3. 可以根据实际情况调整 `taskExecutionTimeout` 的值

## 测试验证

部署后可以通过以下方式验证修复效果：

1. 查看程序启动日志，确认启动时清理逻辑正常执行
2. 观察定时任务日志，确认不再出现重复执行
3. 模拟程序重启，验证处理中状态的任务能正确清理
