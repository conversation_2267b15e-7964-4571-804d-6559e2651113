# AI 图文生成功能使用说明

## 功能概述

AI 图文生成功能允许用户批量创建带有标题和贴纸的图文内容。系统会根据用户设置的标题应用模式，自动为图片添加文字标题。

## 标题应用模式

### 模式说明

1. **仅第一张图片添加标题** (`titleApplyMode: 1`)

   - 只有每组图文的第一张图片会添加标题
   - 适用于封面图需要标题的场景

2. **每张图片都添加标题** (`titleApplyMode: 2`)
   - 每张图片都会添加标题
   - 适用于所有图片都需要文字说明的场景

### 技术实现

系统使用 FFmpeg 的`drawtext`滤镜来为图片添加文字标题：

```go
// 判断是否需要添加标题
needTitle := titleApplyMode == 2 || (titleApplyMode == 1 && i == 0)
```

## 标题样式配置

### 基本属性

- **内容** (`content`): 标题文字内容
- **字体** (`fontFamily`): 字体名称
- **字号** (`fontSize`): 文字大小
- **对齐** (`alignment`): 文字对齐方式 (left/center/right)
- **位置** (`height`): 文字在图片中的垂直位置比例 (0.0-1.0)

### 字体样式

支持三种字体样式类型：

1. **普通样式** (`styleType: "none"`)

   - 纯文字，无额外效果

2. **背景样式** (`styleType: "background"`)

   - 文字带半透明背景色
   - 配置 `backgroundColor` 属性

3. **边框样式** (`styleType: "border"`)
   - 文字带描边效果
   - 配置 `borderColor` 和 `borderWidth` 属性

### FFmpeg 处理示例

```bash
# 基本文字
ffmpeg -i input.jpg -vf "drawtext=text='标题内容':fontsize=24:fontcolor=white:x=(main_w-text_w)/2:y=main_h*15/100" output.jpg

# 带背景的文字
ffmpeg -i input.jpg -vf "drawtext=text='标题内容':fontsize=24:fontcolor=white:x=(main_w-text_w)/2:y=main_h*15/100:box=1:boxcolor=black@0.8:boxborderw=5" output.jpg

# 带描边的文字
ffmpeg -i input.jpg -vf "drawtext=text='标题内容':fontsize=24:fontcolor=white:x=(main_w-text_w)/2:y=main_h*15/100:borderw=2:bordercolor=black" output.jpg
```

## 前端数据结构

```javascript
titleSettings: [
  {
    content: "标题内容",
    fontFamily: "AlibabaPuHuiTi-3-85-Bold.ttf", // 推荐使用OSS字体文件名
    fontSize: 24,
    alignment: "center",
    height: 0.15, // 15%位置
    fontStyle: {
      type: "normal",
      color: "#ffffff",
      styleType: "background", // none/background/border
      backgroundColor: "#000000",
      borderColor: "#ff0000",
      borderWidth: 2,
    },
  },
]
```

### 字体文件名格式说明

前端传递字体时支持以下格式：

1. **OSS 字体文件名**（推荐）

   ```javascript
   fontFamily: "AlibabaPuHuiTi-3-85-Bold.ttf"
   ```

2. **带路径的字体文件**（兼容格式）

   ```javascript
   fontFamily: "/fonts/AlibabaPuHuiTi-3-85-Bold.ttf"
   ```

3. **系统字体名称**（备选）
   ```javascript
   fontFamily: "Arial" // 或 "SimHei", "SimSun", "Microsoft"
   ```

## 系统要求

### FFmpeg 安装

系统需要安装 FFmpeg 来处理图片。

#### macOS

```bash
brew install ffmpeg
```

#### Ubuntu/Debian

```bash
sudo apt update
sudo apt install ffmpeg
```

#### CentOS/RHEL

```bash
sudo yum install epel-release
sudo yum install ffmpeg ffmpeg-devel
```

### 字体支持

系统支持两种字体加载方式：

#### 1. OSS 云端字体（推荐）

- 前端只需传递字体文件名，如：`AlibabaPuHuiTi-3-85-Bold.ttf`
- 系统自动从 [OSS 字体库](https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/font/) 下载字体文件
- 支持本地缓存，避免重复下载
- 字体文件统一管理，保证一致性

#### 2. 系统内置字体（备选）

系统会根据操作系统自动选择合适的字体路径：

- **macOS**: `/System/Library/Fonts/`
- **Windows**: `C:/Windows/Fonts/`
- **Linux**: `/usr/share/fonts/truetype/dejavu/`

## 技术实现细节

### 字体处理流程

```go
func (s *AutoImageTextService) getFontPath(fontFamily string) string {
    // 1. OSS 字体文件名（推荐方式）
    if !strings.Contains(fontFamily, "/") && strings.HasSuffix(fontFamily, ".ttf") {
        return s.downloadOSSFont(fontFamily)
    }

    // 2. 兼容格式：/fonts/xxx.ttf
    if strings.HasPrefix(fontFamily, "/fonts/") {
        fontFileName := strings.TrimPrefix(fontFamily, "/fonts/")
        return s.downloadOSSFont(fontFileName)
    }

    // 3. 系统字体名称
    return s.getSystemFontPath(fontFamily)
}
```

### OSS 字体下载机制

- **缓存策略**: 字体文件下载后缓存在本地临时目录 `/tmp/font_cache/`
- **重复使用**: 相同字体文件只下载一次，后续直接使用缓存
- **错误处理**: 下载失败时回退到系统默认字体
- **自动清理**: 临时字体文件在系统重启时自动清理

## 使用流程

1. **配置图片素材**: 选择要处理的图片
2. **设置标题内容**: 配置标题文字和样式
3. **选择字体文件**: 使用 OSS 字体文件名或系统字体
4. **选择应用模式**: 决定哪些图片需要添加标题
5. **提交任务**: 系统后台处理图片
6. **获取结果**: 下载处理后的图文内容

## 错误处理

系统具有完善的错误处理机制：

- **图片下载失败**: 返回原图片 URL
- **FFmpeg 处理失败**: 记录错误日志，返回原图片 URL
- **字体文件缺失**: 使用系统默认字体
- **上传失败**: 记录错误日志，返回原图片 URL

## 性能优化

- **临时文件管理**: 自动清理处理过程中的临时文件
- **异步处理**: 图片处理在后台异步执行
- **错误恢复**: 单张图片处理失败不影响其他图片
- **字体缓存**: OSS 字体文件本地缓存，避免重复下载
- **并发处理**: 支持多个图文任务同时处理

## 注意事项

1. **图片格式**: 支持常见的图片格式 (JPG, PNG 等)
2. **文字编码**: 确保文字内容为 UTF-8 编码
3. **特殊字符**: 系统会自动转义 FFmpeg 特殊字符
4. **性能考虑**: 大批量处理时建议分批提交任务
5. **字体文件**: 确保 OSS 字体文件可访问，文件名正确无误
6. **网络依赖**: 首次使用字体需要网络下载，后续使用本地缓存

## 故障排除

### 常见问题

1. **标题没有显示**

   - 检查标题内容是否为空
   - 确认标题应用模式设置
   - 验证 FFmpeg 是否正确安装

2. **字体显示异常**

   - 检查 OSS 字体文件是否存在：[字体库链接](https://da-short-video.oss-cn-shenzhen.aliyuncs.com/uploads/font/)
   - 确认前端传递的字体文件名格式正确
   - 检查网络连接，确保能访问 OSS 服务
   - 查看系统日志中的字体下载相关警告

3. **图片处理失败**
   - 检查图片 URL 是否可访问
   - 确认服务器磁盘空间充足
   - 查看 FFmpeg 错误日志

## 更新记录

### 2024-12-19 字体大小一致性修复

#### 问题描述

之前版本中，图文生成的预览效果与实际生成的图片中的标题大小差异较大，预览时标题显得比实际生成的要大。

#### 解决方案

**后端修改 (`server/service/ai/auto_imagetext.go`)**:

- 将固定的字体像素值改为基于图片高度的相对比例
- 使用 FFmpeg 表达式 `fontsize=main_h*比例` 来自动适应不同尺寸的图片
- 基准计算：`字体比例 = 字体大小 / 1000px`，调整基准高度使生成的字体更小

**前端修改 (`web/src/components/autoImageText/ImageTextPreview.vue`)**:

- 将预览中的字体大小缩小到 60%，以匹配实际生成的图片效果
- 确保预览效果与最终生成的图片标题大小基本一致

#### 使用效果

- 预览效果现在与实际生成的图片效果更加一致
- 字体大小会根据图片的实际尺寸自动缩放
- 支持各种尺寸的图片，从小图到高清大图都能保持合适的标题大小

#### 兼容性

- 现有的字体大小设置无需修改
- 向下兼容，不影响已有的图文生成任务
